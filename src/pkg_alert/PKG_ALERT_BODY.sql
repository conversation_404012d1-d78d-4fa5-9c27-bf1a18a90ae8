CREATE OR REPLACE PACKAGE BODY TEST_1072_4.PKG_ALERT
AS
/*
Modification History
****************************************************************************************************
VERSION  WHO          WHEN      WHAT
-------- ------------ --------- --------------------------------------------------------------------
1072.6   Rchatbouri   14/05/25 Mantis 7622: Scheduled Scenario Alerts : JSON Population Error Leading to incorrect Instance Creation
1072.6   Rchatbouri   13/05/25 Mantis 7664: Alerting: Issue with populating the amounts on alert via smart alert framework
1072.5   Rchatbouri   21/04/25 Mantis 7577: Alert scenarios: Error when columns in the base query exceed a certain number.

...
... Three most recent entries only; see package spec for full history
****************************************************************************************************
*/
   -- Get query text from table DJB_QUERIES
   FUNCTION FN_GET_QUERY_TEXT (P_ID P_SCENARIO.SCENARIO_ID%TYPE)
      RETURN CLOB
   IS
      V_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE := NULL;

CURSOR C_QUERY (CP_ID P_SCENARIO.SCENARIO_ID%TYPE)
      IS
SELECT QUERY_TEXT
FROM P_SCENARIO
WHERE SCENARIO_ID = CP_ID;
BEGIN
      IF P_ID IS NULL
      THEN
          RETURN NULL;CREATE OR REPLACE PACKAGE BODY TEST_1072_4.PKG_ALERT
AS
/*
Modification History
****************************************************************************************************
VERSION  WHO          WHEN      WHAT
-------- ------------ --------- --------------------------------------------------------------------
1072.6   Rchatbouri   14/05/25 Mantis 7622: Scheduled Scenario Alerts : JSON Population Error Leading to incorrect Instance Creation
1072.6   Rchatbouri   13/05/25 Mantis 7664: Alerting: Issue with populating the amounts on alert via smart alert framework
1072.5   Rchatbouri   21/04/25 Mantis 7577: Alert scenarios: Error when columns in the base query exceed a certain number.

...
... Three most recent entries only; see package spec for full history
****************************************************************************************************
*/
   -- Get query text from table DJB_QUERIES
   FUNCTION FN_GET_QUERY_TEXT (P_ID P_SCENARIO.SCENARIO_ID%TYPE)
      RETURN CLOB
   IS
      V_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE := NULL;

CURSOR C_QUERY (CP_ID P_SCENARIO.SCENARIO_ID%TYPE)
      IS
SELECT QUERY_TEXT
FROM P_SCENARIO
WHERE SCENARIO_ID = CP_ID;
BEGIN
      IF P_ID IS NULL
      THEN
          RETURN NULL;
END IF;

OPEN C_QUERY (P_ID);

FETCH C_QUERY INTO V_QUERY_TEXT;

CLOSE C_QUERY;

RETURN V_QUERY_TEXT;
EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_QUERY_TEXT -> Error for ' || P_ID,
                       SQLCODE,
                       SQLERRM
                     );
         RAISE;
RETURN NULL;

END;

   -- Take text string for SQL that selects host_id, entity_id, movement_id and make it into a ref cursor
   FUNCTION FN_GET_CUR (P_SQLTEXT CLOB)
      RETURN SYS_REFCURSOR
   IS
      R_CURSOR   SYS_REFCURSOR;
BEGIN
OPEN R_CURSOR FOR P_SQLTEXT;
RETURN R_CURSOR;
END;

   PROCEDURE PROC_GET_CUR
IS
BEGIN
      -- Sole purpose of procedure is to allow java code to access p_cursor as output of procedure
NULL;
END;

   /* Return count of records for a scenario subquery */
   FUNCTION FN_GET_COUNT_FOR_QUERY (P_ID             P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_EXTRA_CONDS    VARCHAR2 DEFAULT NULL)
      RETURN NUMBER
   IS
      V_CUR          SYS_REFCURSOR;
      V_COUNT        NUMBER;
      V_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE;
BEGIN
      -- Select count from scenario subquery
      V_QUERY_TEXT := 'SELECT COUNT(*) "CNT" FROM ('
                      || CHR(10)
                      || PKG_ALERT.FN_GET_QUERY_TEXT (P_ID)
                      || CHR(10)
                      || ')';

      -- Append conditions if supplied
      IF P_EXTRA_CONDS IS NOT NULL
      THEN
         V_QUERY_TEXT := V_QUERY_TEXT || ' ' || P_EXTRA_CONDS;
END IF;

      -- Get ref cursor from function
      V_CUR := PKG_ALERT.FN_GET_CUR (V_QUERY_TEXT);

FETCH V_CUR INTO V_COUNT;

IF V_CUR%ISOPEN
      THEN
         CLOSE V_CUR;
END IF;

RETURN V_COUNT;
END;

   -- WHOLE ROWS: USE BULK COLLECT - FASTER...
   -- Take ref cursor and and pipe output to plsql table
   -- whose record structure matches p_movement row
   -- it can be treated in SQL as a table having exactly same structure as p_movement

   FUNCTION FN_GET_MOVEMENT_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MOVEMENT_TAB
      PIPELINED
   AS
      L_TAB   T_MOVEMENT_TAB;
BEGIN
      LOOP
FETCH P_CURSOR
          BULK COLLECT INTO L_TAB LIMIT vLimit;
          EXIT WHEN L_TAB.COUNT = 0;

FOR I IN 1 .. L_TAB.COUNT
          LOOP
             PIPE ROW (L_TAB (I));
END LOOP;

END LOOP;
CLOSE P_CURSOR;
END;

   -- Take ref cursor and and pipe output to plsql table
   -- whose record structure matches P_MATCH row
   -- it can be treated in SQL as a table having exactly same structure as P_MATCH

   FUNCTION FN_GET_MATCH_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MATCH_TAB
      PIPELINED
   AS
      L_TAB   T_MATCH_TAB;
BEGIN
FETCH P_CURSOR
    BULK COLLECT INTO L_TAB;

FOR I IN 1 .. L_TAB.COUNT
      LOOP
         PIPE ROW (L_TAB (I));
END LOOP;

CLOSE P_CURSOR;
END;

   FUNCTION FN_GET_P_SCENARIO_COUNT_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_P_SCENARIO_COUNTS_TAB
      PIPELINED
   AS
      L_TAB   T_P_SCENARIO_COUNTS_TAB;
BEGIN
FETCH P_CURSOR
    BULK COLLECT INTO L_TAB;

FOR I IN 1 .. L_TAB.COUNT
      LOOP
         PIPE ROW (L_TAB (I));
END LOOP;

CLOSE P_CURSOR;
END;

   --Determine scenario presentation/notification
   -- This function returns the P_NOTIFY_SCENARIO non-key column values
   -- (ACCESS_REQUIRED, DELIVER_POPUPS, FLASH_ICON, SEND_EMAIL) concatenated together:

   FUNCTION FN_GET_SCENARIO_ACCESS (P_SCENARIO_ID    P_NOTIFY_SCENARIO.SCENARIO_ID%TYPE,
                                    P_HOST_ID        P_NOTIFY_SCENARIO.HOST_ID%TYPE,
                                    P_ROLE_ID        P_NOTIFY_SCENARIO.ROLE_ID%TYPE,
                                    P_ENTITY_ID      P_NOTIFY_SCENARIO.ENTITY_ID%TYPE)
      RETURN VARCHAR
   IS
      R_NOTIFY_SCENARIO          P_NOTIFY_SCENARIO%ROWTYPE;
      V_PERFORM_GENERAL_LOOKUP   VARCHAR2 (1) := NULL;
      V_RETURN_VALS              VARCHAR2 (10);
      V_SEC_ENTITY_COL           P_SCENARIO.SEC_ENTITY_COL%TYPE;
BEGIN
      -- GET INFO FROM SCENARIO TABLE
BEGIN
SELECT SEC_ENTITY_COL
INTO V_SEC_ENTITY_COL
FROM P_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID;
END;

      -- For a scenario where SEC_ENTITY_COL is not null:
      -- First look for the P_NOTIFY_SCENARIO record having the relevant
      -- scenario, host and role, and entity_id=<supplied entity_id>.
      IF V_SEC_ENTITY_COL IS NOT NULL -- GET INFO FROM NOTIFY_SCENARIO TABLE for specific entity
      THEN
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = P_ROLE_ID
  AND ENTITY_ID = P_ENTITY_ID;
EXCEPTION
            WHEN NO_DATA_FOUND
            THEN
               V_PERFORM_GENERAL_LOOKUP := 'Y';
END;
ELSE
         V_PERFORM_GENERAL_LOOKUP := 'Y';
END IF;

      -- If no record is found then look for the P_NOTIFY_SCENARIO record
      -- having the relevant scenario, host and role, and entity_id='All'
      -- This also covers the case where R_SCENARIO.SEC_ENTITY_COL IS NULL
      -- If no record found then assume no access
      IF V_PERFORM_GENERAL_LOOKUP = 'Y'
      THEN
         -- GET INFO FROM NOTIFY_SCENARIO TABLE
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = P_ROLE_ID
  AND ENTITY_ID = 'All';
EXCEPTION
            WHEN NO_DATA_FOUND THEN
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = 'All'
  AND ENTITY_ID = 'All';
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        NULL;
END;
END;
END IF;

      IF R_NOTIFY_SCENARIO.ACCESS_REQUIRED IS NOT NULL
      THEN
         V_RETURN_VALS :=
               NVL (R_NOTIFY_SCENARIO.ACCESS_REQUIRED, 'N')
            || NVL (R_NOTIFY_SCENARIO.DELIVER_POPUPS, 'N')
            || NVL (R_NOTIFY_SCENARIO.FLASH_ICON, 'N')
            || NVL (R_NOTIFY_SCENARIO.SEND_EMAIL, 'N')
            || NVL (R_NOTIFY_SCENARIO.FULL_INSTANCE_ACCESS, 'N');
ELSE
         V_RETURN_VALS := 'NNNNN';
END IF;

RETURN V_RETURN_VALS;
EXCEPTION
      WHEN OTHERS
      THEN
         -- most likely exception is no data_found
         -- returning 'no access' ensures calling code can operate
         V_RETURN_VALS := 'NNNNN';

RETURN V_RETURN_VALS;
END;

   PROCEDURE PROC_POPULATE_SCENARIO_COUNTS(vSystemFlag VARCHAR2)
IS
      /*
      Define selection of alert queries for which pop must run
      Will need extra conditions to ensure only the required (active) queries are processed
      */
      v_err_loc VARCHAR2(10); -- Error location
      vSCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE;
CURSOR C_QUERY_TO_RUN
      IS
SELECT S.*
FROM    P_SCENARIO S
            LEFT OUTER JOIN
        P_SCENARIO_SYSTEM SS
        ON (SS.SCENARIO_ID = S.SCENARIO_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND S.SYSTEM_FLAG = vSystemFlag
  AND NVL(S.RECORD_SCENARIO_INSTANCES, 'N') = 'N'
    /* start/end boundaries are satisfied */
  AND PKG_ALERT.FN_TEST_START_END_TIME (GLOBAL_VAR.SYS_DATE, S.START_TIME, S.END_TIME) = 'Y'
  AND (SS.LAST_RUN_DATE IS NULL -- scenario never checked before
    OR SS.LAST_RUN_DATE > GLOBAL_VAR.SYS_DATE -- Last checked in future (test date anomaly)
    OR -- or scenario was last checked 'run every' time ago
       (SS.LAST_RUN_DATE <
        (  GLOBAL_VAR.SYS_DATE
            - (  TO_NUMBER (
                         TO_CHAR (
                                 TO_DATE (NVL (S.RUN_EVERY, '00:00:00'), 'HH24:MI:SS'),
                                 'SSSSS'))
                / 86400))));

/*
Cursor C_GET_COUNTS_ETY_CCY  defines how to get counts grouped by entity
and currency for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id, currency_code
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY_CCY_THRESH (
         P_SCENARIO_ID         P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL        P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL      P_SCENARIO.SEC_ENTITY_COL%TYPE,
         P_SEC_CURRENCY_COL    P_SCENARIO.SEC_CURRENCY_COL%TYPE,
         P_AMT_THRESH_COL      P_SCENARIO.AMT_THRESHOLD_COL%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || P_SEC_CURRENCY_COL
                         || ' "CURRENCY_CODE", '
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         --> this bit compares amount to ccy threshold only above t are counted
                         || 'COUNT (CASE WHEN '
                         || P_AMT_THRESH_COL
                         || ' >= (SELECT STLCCYTAB.THRESHOLD_PRODUCT FROM S_CURRENCY STLCCYTAB '
                         || 'WHERE STLCCYTAB.HOST_ID = STLSCENQUERY.' || P_SEC_HOST_COL
                         || ' AND STLCCYTAB.ENTITY_ID = STLSCENQUERY.' || P_SEC_ENTITY_COL
                         || ' AND STLCCYTAB.CURRENCY_CODE = STLSCENQUERY.' || P_SEC_CURRENCY_COL ||') '
                         || ' THEN 1 ELSE NULL END ) "SCENARIO_COUNT_OVER_T" ' --above t count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         --|| fn_scenario_with_rownum(
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL
                         || ', '
                         || P_SEC_CURRENCY_COL)));

/*
Cursor C_GET_COUNTS_ETY_CCY  defines how to get counts grouped by entity
and currency for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id, currency_code
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY_CCY (
         P_SCENARIO_ID         P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL        P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL      P_SCENARIO.SEC_ENTITY_COL%TYPE,
         P_SEC_CURRENCY_COL    P_SCENARIO.SEC_CURRENCY_COL%TYPE)
      IS --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || P_SEC_CURRENCY_COL
                         || ' "CURRENCY_CODE", '
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         || 'COUNT(*) "SCENARIO_COUNT_OVER_T" ' -- Total count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL
                         || ', '
                         || P_SEC_CURRENCY_COL)));

/*
Cursor C_GET_COUNTS_ETY defines how to get counts grouped by host-entity
for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY (
         P_SCENARIO_ID       P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL      P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL    P_SCENARIO.SEC_ENTITY_COL%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || q'['All' "CURRENCY_CODE", ]'
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         || 'COUNT(*) "SCENARIO_COUNT_OVER_T" ' -- Total count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL)));

/*
 Cursor C_GET_COUNTS_ETY defines how to get simple count from
 a given query id aliased as 'source_data'.

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
 */
CURSOR C_GET_COUNT_ONLY (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!

SELECT P_SCENARIO_ID SCENARIO_ID,
       GLOBAL_VAR.FN_GET_HOST HOST_ID,
       'All' ENTITY_ID,
       'All' CURRENCY_CODE,
       CNT SCENARIO_COUNT,
       CNT SCENARIO_COUNT_OVER_T,
       'N' EMAIL_FLAG,
       0 EMAILED_SC_COUNT,
       0 EMAILED_SC_COUNT_OVER_T
FROM (SELECT PKG_ALERT.FN_GET_COUNT_FOR_QUERY (P_SCENARIO_ID) CNT FROM DUAL);

-- variables used in timing the processing of a scenario
V_START_TIMESTAMP    TIMESTAMP;
      V_END_TIMESTAMP    TIMESTAMP;
      V_PROCESS_DURATION   INTERVAL DAY (2) TO SECOND (4);

      -- Backup of table P_SCENARIO_COUNTS
      V_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB;
BEGIN
      v_err_loc := 1;
      -- SAFELY Backup the P_SCENARIO_COUNTS table
BEGIN
SELECT * BULK COLLECT INTO V_SCENARIO_COUNTS_TAB_BKP
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR ('SELECT * FROM P_SCENARIO_COUNTS')
        )
     );
EXCEPTION
        WHEN OTHERS THEN
          V_SCENARIO_COUNTS_TAB_BKP := NULL;
END;

      /* LOOP ON QUERIES */
      v_err_loc := 10;
FOR R_QUERY_TO_RUN IN C_QUERY_TO_RUN
      LOOP
        --BEGIN

         v_err_loc := '20';
         V_START_TIMESTAMP := SYSTIMESTAMP;
         vSCENARIO_ID := R_QUERY_TO_RUN.SCENARIO_ID;

         -- Delete any existing count records to zero for this scenario
         -- do not commit.  This ensure totals are zeroed if the query return no
         -- data for a entity/ccy...
         DELETE P_SCENARIO_COUNTS
          WHERE SCENARIO_ID = R_QUERY_TO_RUN.SCENARIO_ID;
         v_err_loc := '30';
         -- Determine how query result are to be handled for security
         -- calculation of totals for strage on temp table
CASE
            --->  Query is threshold enable AND returns HOST ENTITY CCY ...
            -- counts for SCENARIO_COUNT and SCENARIO_COUNT_OVER_T
            --will be grouped and stored by host/entity/Currency
            WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.AMT_THRESHOLD_COL IS NOT NULL)
            THEN
BEGIN
                   v_err_loc := '40';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                      v_err_loc := '41.1';
                            sp_error_log ('',
                                          'SYSTEM',
                                          'DBSERVER',
                                          'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                          || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                          || ' at Location: ' || v_err_loc,
                                          SQLCODE,
                                          'Scenario query text is missing'
                                        );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY_CCY_THRESH (
                                R_QUERY_TO_RUN.SCENARIO_ID,
                                TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL),
                                TRIM (R_QUERY_TO_RUN.SEC_CURRENCY_COL),
                                R_QUERY_TO_RUN.AMT_THRESHOLD_COL)
                       LOOP
                         v_err_loc := '41';
                         INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
            --->  Query returns HOST ENTITY CCY ...
            -- counts will be grouped and stored by host/entity/Currency
WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NOT NULL)
            THEN
BEGIN
                   v_err_loc := '42';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '42.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY_CCY (R_QUERY_TO_RUN.SCENARIO_ID,
                                                   TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                                   TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL),
                                                   TRIM (R_QUERY_TO_RUN.SEC_CURRENCY_COL))
                       LOOP
                            v_err_loc := '42.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc||CHR(10)
                                            || 'SCENARIO_ID='||R_QUERY_TO_RUN.SCENARIO_ID||CHR(10)
                                            || 'SEC_HOST_COL='||R_QUERY_TO_RUN.SEC_HOST_COL||CHR(10)
                                            || 'SEC_ENTITY_COL='||R_QUERY_TO_RUN.SEC_ENTITY_COL||CHR(10)
                                            || 'SEC_CURRENCY_COL='||R_QUERY_TO_RUN.SEC_CURRENCY_COL||CHR(10)
                                            ,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
            --->  Query supports HOST ENTITY  ...
            -- counts will be grouped and stored by host/entity/[Currency='All']
WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NULL)
            THEN
BEGIN
                   v_err_loc := '43';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '43.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY (R_QUERY_TO_RUN.SCENARIO_ID,
                                               TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                               TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL))
                       LOOP
                            v_err_loc := '43.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
ELSE
BEGIN
                   --->  Query does not return entity or ccy ...
                   -- counts will be stored by host/[entity=All]/[Currency=All]
                   v_err_loc := '44';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '44.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT IN C_GET_COUNT_ONLY (R_QUERY_TO_RUN.SCENARIO_ID)
                       LOOP

                            v_err_loc := '44.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
END CASE;

         -- calculate run duration for this scenario
         V_END_TIMESTAMP := SYSTIMESTAMP;
         V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;

         v_err_loc := '60';
         -- Update scenario record with last RUN time = now AND DURATION OF RUN
UPDATE P_SCENARIO_SYSTEM
SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
WHERE SCENARIO_ID = R_QUERY_TO_RUN.SCENARIO_ID;

v_err_loc := '70';
          IF SQL%ROWCOUNT = 0 THEN
            v_err_loc := '80';
INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
SELECT R_QUERY_TO_RUN.SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
FROM DUAL
WHERE NOT EXISTS (SELECT NULL
                  FROM P_SCENARIO_SYSTEM
                  WHERE SCENARIO_ID =  R_QUERY_TO_RUN.SCENARIO_ID);
END IF;

         v_err_loc := '90';
COMMIT;
/*EXCEPTION
       WHEN OTHERS
       THEN
          sp_error_log ('',
                        'SYSTEM',
                        'DBSERVER',
                        'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                        || ' Error for ' || vSCENARIO_ID
                        || ' at Location: ' || v_err_loc,
                        SQLCODE,
                        SQLERRM
                      );
END;*/
END LOOP;
   --< end of query loop
EXCEPTION
   WHEN OTHERS
   THEN
      sp_error_log ('',
                    'SYSTEM',
                    'DBSERVER',
                    'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                    || ' Error for ' || vSCENARIO_ID
                    || ' at Location: ' || v_err_loc,
                    SQLCODE,
                    SQLERRM
                  );
END;

   PROCEDURE INS_UPD_P_SCENARIO_COUNTS (P_SCENARIO_COUNT_REC P_SCENARIO_COUNTS%ROWTYPE, P_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB DEFAULT NULL)
IS
    V_BCKP_ROW P_SCENARIO_COUNTS%ROWTYPE;
    V_FOUND BOOLEAN := FALSE;
BEGIN
BEGIN
INSERT INTO P_SCENARIO_COUNTS (SCENARIO_ID,
                               HOST_ID,
                               ENTITY_ID,
                               CURRENCY_CODE,
                               SCENARIO_COUNT,
                               SCENARIO_COUNT_OVER_T)
VALUES (P_SCENARIO_COUNT_REC.SCENARIO_ID,
        P_SCENARIO_COUNT_REC.HOST_ID,
        P_SCENARIO_COUNT_REC.ENTITY_ID,
        P_SCENARIO_COUNT_REC.CURRENCY_CODE,
        P_SCENARIO_COUNT_REC.SCENARIO_COUNT,
        P_SCENARIO_COUNT_REC.SCENARIO_COUNT_OVER_T);
EXCEPTION
      WHEN DUP_VAL_ON_INDEX
      THEN
UPDATE P_SCENARIO_COUNTS
SET SCENARIO_COUNT = P_SCENARIO_COUNT_REC.SCENARIO_COUNT,
    SCENARIO_COUNT_OVER_T = P_SCENARIO_COUNT_REC.SCENARIO_COUNT_OVER_T
WHERE SCENARIO_ID = P_SCENARIO_COUNT_REC.SCENARIO_ID
  AND HOST_ID = P_SCENARIO_COUNT_REC.HOST_ID
  AND ENTITY_ID = P_SCENARIO_COUNT_REC.ENTITY_ID
  AND CURRENCY_CODE = P_SCENARIO_COUNT_REC.CURRENCY_CODE;
WHEN OTHERS
       THEN
          sp_error_log ('',
                        'SYSTEM',
                        'DBSERVER',
                        'PKG_ALERT.INS_UPD_P_SCENARIO_COUNTS',
                        SQLCODE,
                        SQLERRM
                      );
END;

       -- SAFELY Update the email-flag to 'N' if actual scenario count increased in percentage EMAIL_PCT_DIFF compared to EMAILED_SC_COUNT
BEGIN
           IF P_SCENARIO_COUNTS_TAB_BKP IS NOT NULL THEN
                -- Get the backed up row from backed up table. If not already backed up, then assign current row
BEGIN
FOR i IN 1 .. P_SCENARIO_COUNTS_TAB_BKP.COUNT LOOP
                        IF P_SCENARIO_COUNTS_TAB_BKP(i).SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).HOST_ID=P_SCENARIO_COUNT_REC.HOST_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).ENTITY_ID=P_SCENARIO_COUNT_REC.ENTITY_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).CURRENCY_CODE=P_SCENARIO_COUNT_REC.CURRENCY_CODE
                        THEN
                            V_BCKP_ROW := P_SCENARIO_COUNTS_TAB_BKP(i);
                            V_FOUND := TRUE;
END IF;
                        EXIT WHEN V_FOUND;
END LOOP;

                     IF NOT V_FOUND THEN
                        V_BCKP_ROW := P_SCENARIO_COUNT_REC;
END IF;
EXCEPTION
                    WHEN OTHERS THEN
                        V_BCKP_ROW := P_SCENARIO_COUNT_REC;
END;

                -- Update V_BCKP_ROW.EMAIL_FLAG according to the business
SELECT CASE WHEN ABS(NVL(P_SCENARIO_COUNT_REC.SCENARIO_COUNT,0)-NVL(V_BCKP_ROW.EMAILED_SC_COUNT,0))*100/DECODE(V_BCKP_ROW.EMAILED_SC_COUNT,NULL, 1, 0 ,1, V_BCKP_ROW.EMAILED_SC_COUNT)>=NVL(S.EMAIL_PCT_DIFF,0)
                THEN 'N' -- New
            ELSE V_BCKP_ROW.EMAIL_FLAG
           END INTO V_BCKP_ROW.EMAIL_FLAG
FROM P_SCENARIO S
WHERE S.SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID;

-- Update EMAIL columns
UPDATE P_SCENARIO_COUNTS SET EMAIL_FLAG=V_BCKP_ROW.EMAIL_FLAG,
                             EMAILED_SC_COUNT=V_BCKP_ROW.EMAILED_SC_COUNT,
                             EMAILED_SC_COUNT_OVER_T=V_BCKP_ROW.EMAILED_SC_COUNT_OVER_T
WHERE SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID
  AND HOST_ID=P_SCENARIO_COUNT_REC.HOST_ID
  AND ENTITY_ID=P_SCENARIO_COUNT_REC.ENTITY_ID
  AND CURRENCY_CODE=P_SCENARIO_COUNT_REC.CURRENCY_CODE;
END IF;
EXCEPTION
           WHEN OTHERS
           THEN
            sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.INS_UPD_P_SCENARIO_COUNTS: Error occurred when setting the email flags',
                            SQLCODE,
                            SQLERRM
        );
END;
END;

   /* Accept a timestamp interval and convert it to number of seconds */

   FUNCTION FN_CONV_INTERVAL_TO_SECS (P_IN_INTERVAL INTERVAL DAY TO SECOND)
      RETURN NUMBER
   IS
      V_DURATION_DAYS     NUMBER;
      V_DURATION_HOURS    NUMBER;
      V_DURATION_MINS     NUMBER;
      V_DURATION_SECS     NUMBER;
      V_NUMBER_SECS_OUT   VARCHAR2 (50);
BEGIN
      V_DURATION_DAYS := NVL (EXTRACT (DAY FROM P_IN_INTERVAL), 0);
      V_DURATION_HOURS := NVL (EXTRACT (HOUR FROM P_IN_INTERVAL), 0);
      V_DURATION_MINS := NVL (EXTRACT (MINUTE FROM P_IN_INTERVAL), 0);
      V_DURATION_SECS := NVL (EXTRACT (SECOND FROM P_IN_INTERVAL), 0);
      V_NUMBER_SECS_OUT :=
           V_DURATION_DAYS * 86400
         + V_DURATION_HOURS * 3600
         + V_DURATION_MINS * 60
         + V_DURATION_SECS;
RETURN V_NUMBER_SECS_OUT;
END;

   /*  Accept number of seconds and format it like '1d 15h 46m 39.4832s' */

   FUNCTION FN_CONV_SECS_TO_DUR_STRING (P_IN_SECS NUMBER)
      RETURN VARCHAR2
   IS
      V_DURATION_DAYS    NUMBER;
      V_DURATION_HOURS   NUMBER;
      V_DURATION_MINS    NUMBER;
      V_DURATION_SECS    NUMBER;
      V_TEXT_OUT         VARCHAR2 (50);
BEGIN
      V_DURATION_DAYS := FLOOR (P_IN_SECS / 86400);
      V_DURATION_HOURS := FLOOR (MOD (P_IN_SECS, 86400) / 3600);
      V_DURATION_MINS := FLOOR (MOD (P_IN_SECS, 3600) / 60);
      V_DURATION_SECS := MOD (P_IN_SECS, 60);

      V_TEXT_OUT :=
            TO_CHAR (V_DURATION_DAYS, '90')
         || 'd '
         || TO_CHAR (V_DURATION_HOURS, 'FM90')
         || 'h '
         || TO_CHAR (V_DURATION_MINS, 'FM90')
         || 'm '
         || TO_CHAR (V_DURATION_SECS, 'FM90.0000')
         || 's';
RETURN V_TEXT_OUT;
END;

   /* RETURN 'Y' IF SUPPLIED TIME IS BETWEEN START AND END TIME*/
   /* IF ANY OF THE PARAMETERS IS NULL RETURN 'Y' ANYWAY*/

   FUNCTION FN_TEST_START_END_TIME (P_TEST_DATETIME    DATE,
                                    P_START_TIME       VARCHAR2,
                                    P_END_TIME         VARCHAR2)
      RETURN VARCHAR
   IS
      V_RETURN_VAL   VARCHAR2 (1) := 'Y';
      V_START_TIME   VARCHAR2 (4);
      V_END_TIME     VARCHAR2 (4);
      V_TEST_TIME    VARCHAR2 (4);
BEGIN
      IF P_TEST_DATETIME IS NOT NULL
     AND P_START_TIME IS NOT NULL
     AND P_END_TIME IS NOT NULL
      THEN
         /* Strip colons out of times */
         V_START_TIME := REPLACE (P_START_TIME, ':', '');
         V_END_TIME := REPLACE (P_END_TIME, ':', '');

         /* get time component of test date time supplied */
         V_TEST_TIME := TO_CHAR (P_TEST_DATETIME, 'HH24MI');

         /*DO TESTS*/
         /* normal case e.g. start:0900  end 1700...*/
         IF V_START_TIME < V_END_TIME
        AND (V_TEST_TIME >= V_START_TIME
         AND V_TEST_TIME < V_END_TIME)
         THEN
            V_RETURN_VAL := 'Y';
         /* reverse case e.g. start:2300 end:0100....*/
         ELSIF V_START_TIME >= V_END_TIME
           AND (V_TEST_TIME >= V_START_TIME
             OR V_TEST_TIME < V_END_TIME)
         THEN
            V_RETURN_VAL := 'Y';
ELSE
            /* times not satisfied - return N */
            V_RETURN_VAL := 'N';
END IF;
END IF;

RETURN V_RETURN_VAL;
END;

   -- Accept a query text, a filter, an order and the two bounds and get the refcursor and the count of rows
   PROCEDURE PRC_EXEC_QUERY (p_QUERY_TEXT           VARCHAR2,
                             p_FILTER               VARCHAR2,
                             p_ORDER                VARCHAR2,
                             p_ROW_BEGIN            NUMBER,
                             p_ROW_END              NUMBER,
                             p_ASC_DESC             VARCHAR2,
                             p_CUR_RES       IN OUT SYS_REFCURSOR,
                             p_Count_Rows      OUT NUMBER,
                             p_Query_String    OUT VARCHAR2)
IS
       vFILTER   VARCHAR2 (32000) := '';
       vSQL      CLOB;
       curRefCursor     sys_refcursor;
       CurRefCursorFilter sys_refcursor;
       vDateFormat      VARCHAR2(30);
       VINDVAL          TINDVAL;
       VINDVALUE        VARCHAR2(4000);
       VCOLIDX          NUMBER(5);
       VCOLI            TCOLI;
BEGIN
       -- get date format from s_system_parameters
       vDateFormat := pk_utility.fn_get_date_format('Y');
       -- transform the filter given from java side to a WHERE clause to build the query
       PKG_ALERT.spSplitter(p_FILTER, '|', CurRefCursorFilter);
       LOOP
FETCH CurRefCursorFilter INTO VINDVALUE, VCOLIDX;
            EXIT WHEN CurRefCursorFilter%NOTFOUND;

            IF VINDVALUE != 'All' THEN
                PKG_ALERT.spSplitter(VINDVALUE, '$#$', CurRefCursor);
FETCH CurRefCursor BULK COLLECT INTO VINDVAL, VCOLI;
IF vFILTER IS NOT NULL THEN
                    vFILTER := vFILTER || ' AND ';
END IF;
                IF (LOWER(VINDVAL(3)) = 'date') THEN
                    vFILTER := vFILTER || 'TO_DATE(TO_CHAR(SCENQUERY.' || VINDVAL(1) || ',''' || vDateFormat ||''')' || ','''|| vDateFormat ||''')' || '= TO_DATE(''' || VINDVAL(2) || ''','''|| vDateFormat ||''')';
ELSE
                    vFILTER := vFILTER || ' SCENQUERY.' || VINDVAL(1) || CASE   WHEN VINDVAL(2)='(EMPTY)' THEN ' IS NULL'
                                                                                WHEN VINDVAL(2)='(NOT EMPTY)' THEN ' IS NOT NULL'
                                                                                ELSE '=''' || VINDVAL(2) || ''''
END;
END IF;
END IF;
END LOOP;

       -- Build the query text
SELECT 'SELECT *
             FROM (SELECT ROW_NUMBER () OVER (ORDER BY '
           || DECODE (p_ORDER, '', '1', 'SCENQUERY.' || p_ORDER || ' ' || p_ASC_DESC)
           || ') ROW_,SCENQUERY.* FROM ('
           || CHR(10)
           || p_QUERY_TEXT
           || CHR(10)
           || ') SCENQUERY'
           || DECODE (vFILTER, '', '', ' WHERE ' || vFILTER)
           || DECODE (p_ROW_BEGIN || p_ROW_END,'00', ')',') WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ' || p_ROW_END)
INTO vSQL
FROM DUAL;

OPEN p_cur_res FOR CAST (vSQL AS VARCHAR2);
p_Query_String := SUBSTR(CAST (vSQL AS VARCHAR2), 1, INSTR(vSQL, ' WHERE ROW_ BETWEEN ') - 1) || ' WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ';

       -- get also the count of rows for the query text after applying conditions
EXECUTE IMMEDIATE 'SELECT COUNT(*) FROM ('
    || CHR(10)
    || p_QUERY_TEXT
    || CHR(10)
    || ') SCENQUERY' || CASE WHEN vFILTER IS NULL THEN '' ELSE ' WHERE ' || vFILTER END
    INTO p_Count_Rows;

EXCEPTION
           WHEN OTHERS
           THEN
              sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.PRC_EXEC_QUERY',
                            SQLCODE,
                            SQLERRM
                          );
END;


   PROCEDURE PROC_SCENARIO_COUNT_BY_ENT_CUR (p_SCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE,
                                             p_ROLE_ID S_ROLE.ROLE_ID%TYPE,
                                             p_THRESHOLD IN CHAR,
                                             p_GroupBy VARCHAR,
                                             p_EntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                             p_SortColumn IN VARCHAR2 DEFAULT NULL,
                                             p_SortDirection IN VARCHAR2 DEFAULT NULL,
                                             p_CcyGroup   IN VARCHAR2,
                                             p_IsAlertable   IN VARCHAR2,
                                             pFilter IN VARCHAR2,
                                             p_CurScenarioMaster IN OUT sys_refcursor,
                                             p_CurScenarioSlave IN OUT sys_refcursor) IS
    vCurScenarioMaster VARCHAR2(4000);
    vCurScenarioSlave VARCHAR2(4000);
    curRefCursor        SYS_REFCURSOR;
    vv_filter_slave     VARCHAR2(4000);
    vv_filter_master1   VARCHAR2(4000);
    vv_filter_master2   VARCHAR2(4000);
    vIndValue           VARCHAR2(4000);
    vv_sql              VARCHAR2(4000);
    vColIdx             PLS_INTEGER;
    vv_param_thresh     VARCHAR2(1);
BEGIN
        -- split filter
        pk_utility.spSplitter(pFilter, '|', curRefCursor);
        LOOP
FETCH curRefCursor INTO vIndValue, vColIdx;

            EXIT WHEN curRefCursor%NOTFOUND;

            IF vColIdx = 1 AND vIndValue != 'All' THEN
                vv_filter_slave := vv_filter_slave || ' AND ENTITY_ID = ''' || vIndValue || '''';
                vv_filter_master1 := vv_filter_master1 || ' AND ENTITY_ID = ''' || vIndValue || '''';
            ELSIF vColIdx = 2 AND vIndValue != 'All' THEN
                vv_filter_slave := vv_filter_slave || ' AND CURRENCY_CODE = ''' || vIndValue || '''';
                vv_filter_master1 := vv_filter_master1 || ' AND CURRENCY_CODE = ''' || vIndValue || '''';
            ELSIF vColIdx = 3 AND vIndValue != 'All' THEN
                vv_param_thresh := 'Y';
                --vv_filter_master2 := ' AND SUM(C.SCENARIO_COUNT) = ' || vIndValue;
                vv_filter_slave := vv_filter_slave || ' AND CASE WHEN :pTHRESHOLD = ''Y'' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END = ' || vIndValue;
                vv_filter_master1 := vv_filter_slave;
END IF;

END LOOP;

        IF p_GroupBy IN ('E','C') THEN
            vCurScenarioMaster := q'[SELECT ENTITY_ID,'All' CURRENCY_CODE, SUM(CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) SCENARIO_COUNT
              FROM P_SCENARIO_COUNTS C, P_SCENARIO S
             WHERE C.SCENARIO_ID = :pSCENARIO_ID
               AND C.SCENARIO_ID = S.SCENARIO_ID
               AND (:pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
               AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                       :pROLE_ID,
                                                       C.ENTITY_ID,
                                                       C.CURRENCY_CODE) < 2
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                             C.HOST_ID,
                                                             :pROLE_ID,
                                                             C.ENTITY_ID),1,1)='Y'
               AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE (:pCcyGroup IN ('All', CURRENCY_GROUP_ID) OR CURRENCY_GROUP_ID = 'All') UNION SELECT :pCcyGroup FROM DUAL))
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                            C.HOST_ID,
                                                            :pROLE_ID,
                                                            C.ENTITY_ID),2,3) LIKE DECODE(:pIsAlertable,'Y','%Y%','NNN')]';
            vCurScenarioMaster := vCurScenarioMaster || vv_filter_master1;
            vCurScenarioMaster := vCurScenarioMaster || q'[
            GROUP BY C.ENTITY_ID
            HAVING SUM(CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0 ]' || vv_filter_master2;
SELECT vCurScenarioMaster || DECODE(p_SortColumn, '-1', ' ORDER BY C.ENTITY_ID ',
                                    '2', ' ORDER BY C.ENTITY_ID,CURRENCY_CODE ' || p_SortDirection,
                                    '3', ' ORDER BY C.ENTITY_ID, SUM(DECODE(:pTHRESHOLD, ''Y'', C.SCENARIO_COUNT_OVER_T, C.SCENARIO_COUNT)) ' || p_SortDirection)
INTO vCurScenarioMaster
FROM DUAL;

IF p_SortColumn = '3' THEN
                IF vv_param_thresh = 'Y' THEN
                    OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD, p_THRESHOLD;
ELSE
                    OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
END IF;
ELSE
                IF vv_param_thresh = 'Y' THEN
                OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
ELSE
                OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
END IF;
END IF;

           vCurScenarioSlave := q'[SELECT ENTITY_ID, CURRENCY_CODE, CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END
          FROM P_SCENARIO_COUNTS C, P_SCENARIO S
         WHERE C.SCENARIO_ID = :pSCENARIO_ID
           AND C.SCENARIO_ID = S.SCENARIO_ID
           AND CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END != 0
           AND (:pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
           AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                   :pROLE_ID,
                                                   C.ENTITY_ID,
                                                   C.CURRENCY_CODE) < 2
           AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                      C.HOST_ID,
                                                      :pROLE_ID,
                                                      C.ENTITY_ID),
                                                      1,
                                                      1)='Y'
           AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE (:pCcyGroup IN ('All', CURRENCY_GROUP_ID) OR CURRENCY_GROUP_ID = 'All') UNION SELECT :pCcyGroup FROM DUAL))
           AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                        C.HOST_ID,
                                                        :pROLE_ID,
                                                        C.ENTITY_ID),2,3) LIKE DECODE(:pIsAlertable,'Y','%Y%','NNN')]';
            vCurScenarioSlave := vCurScenarioSlave || vv_filter_slave;
SELECT vCurScenarioSlave || DECODE(p_SortColumn, '-1', ' ORDER BY ENTITY_ID, CURRENCY_CODE ',
                                   '2', ' ORDER BY ENTITY_ID,CURRENCY_CODE ' || p_SortDirection,
                                   '3', ' ORDER BY ENTITY_ID, DECODE(:pTHRESHOLD, ''Y'', C.SCENARIO_COUNT_OVER_T, C.SCENARIO_COUNT) ' || p_SortDirection)
INTO vCurScenarioSlave
FROM DUAL;
IF p_SortColumn = '3' THEN
                IF vv_param_thresh = 'Y' THEN
                    OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                            USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
ELSE
                    OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                            USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
END IF;
ELSE
                IF vv_param_thresh = 'Y' THEN
                OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                        USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
ELSE
                OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                        USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable;
END IF;
END IF;
        ELSIF p_GroupBy = 'N' THEN
            --OPEN p_CurScenarioMaster FOR
            vv_sql := q'[
            SELECT 'All' ENTITY_ID, 'All' CURRENCY_CODE, SUM(CASE WHEN :p_THRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) SCENARIO_COUNT
              FROM P_SCENARIO_COUNTS C, P_SCENARIO S
             WHERE C.SCENARIO_ID = :p_SCENARIO_ID
               AND C.SCENARIO_ID = S.SCENARIO_ID
               AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                       :p_ROLE_ID,
                                                       C.ENTITY_ID,
                                                       C.CURRENCY_CODE) < 2
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                          C.HOST_ID,
                                                          :p_ROLE_ID,
                                                          C.ENTITY_ID),1,1)='Y'
               AND (:p_EntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All') ]' || vv_filter_master1 || q'[
               HAVING SUM(CASE WHEN :p_THRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0
               ]';

OPEN p_CurScenarioMaster FOR vv_sql USING p_THRESHOLD,p_SCENARIO_ID, p_ROLE_ID, p_ROLE_ID, p_EntityID, p_THRESHOLD;
END IF;

        IF curRefCursor%ISOPEN
        THEN
           CLOSE curRefCursor;
END IF;
EXCEPTION
          WHEN OTHERS
          THEN
             IF curRefCursor%ISOPEN
             THEN
                CLOSE curRefCursor;
END IF;
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_SCENARIO_COUNT_BY_ENT_CUR:'||DBMS_UTILITY.format_error_backtrace,
                           SQLCODE,
                           SQLERRM
                          );
END;


   -- Return the counts for each category/scenario for which the user's role has access
   PROCEDURE PROC_POPULATE_SCENARIO_CATEG (pRoleID IN S_ROLE.ROLE_ID%TYPE,
                                           pEntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                           pTHRESHOLD IN VARCHAR,
                                           pAlertableOnly IN VARCHAR,
                                           pCcyGroup IN VARCHAR2,
                                           pCallOption IN VARCHAR2,
                                           pSelectedTab IN P_CATEGORY.DISPLAY_TAB%TYPE,
                                           CurScenCountByCateg IN OUT SYS_REFCURSOR,
                                           vCountAllCateg OUT NUMBER) IS
BEGIN
OPEN CurScenCountByCateg FOR
SELECT TITLE_CAT, DISP_ORDER_CAT, DESC_CAT, SCENARIO_ID, DISP_ORDER_SCEN, TITLE_SCEN, SUM(CNT) AS CNT, TIME_TO_CUT_OFF, IsScenarioAlertable, DESC_SCEN
FROM (SELECT NVL (CAT.TITLE, 'No Category') TITLE_CAT,
             CAT.DISPLAY_ORDER DISP_ORDER_CAT,
             CAT.DESCRIPTION DESC_CAT,
             C.SCENARIO_ID,
             S.DISPLAY_ORDER DISP_ORDER_SCEN,
             S.TITLE TITLE_SCEN,
             SUM(CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) CNT,
             PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
             (
                     CASE WHEN
                              (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,S.START_TIME, S.END_TIME)='Y')
                                  AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                                  AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(S.END_TIME,'HH24:MI')
                              THEN
                              ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(S.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                          ELSE
                              GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(S.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END
             ) AS TIME_TO_CUT_OFF,
             CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3) LIKE '%Y%' THEN 'Y'
                  ELSE 'N' END AS IsScenarioAlertable,
             S.DESCRIPTION DESC_SCEN
      FROM P_SCENARIO_COUNTS C
               INNER JOIN P_SCENARIO S
                          ON (S.SCENARIO_ID = C.SCENARIO_ID)
               LEFT OUTER JOIN P_CATEGORY CAT
                               ON (CAT.CATEGORY_ID = S.CATEGORY_ID)
      WHERE     S.ACTIVE_FLAG = 'Y'
        AND (pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
        AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                pRoleID,
                                                C.ENTITY_ID,
                                                C.CURRENCY_CODE) < 2
        AND SUBSTR (PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                      C.HOST_ID,
                                                      pRoleID,
                                                      C.ENTITY_ID), 1, 1) = 'Y'
        AND DECODE(pAlertableOnly, 'Y', SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3),'Y') LIKE '%Y%'
        AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE pCcyGroup IN (CURRENCY_GROUP_ID, 'All') UNION SELECT pCcyGroup FROM DUAL))
        AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID) LIKE 'Y' || pCallOption || '_'
        AND CAT.DISPLAY_TAB = nvl(pSelectedTab, CAT.DISPLAY_TAB)
      GROUP BY CAT.TITLE,
               CAT.DISPLAY_ORDER,
               CAT.DISPLAY_TAB,
               CAT.DESCRIPTION,
               C.SCENARIO_ID,
               S.DISPLAY_ORDER,
               S.TITLE,
               S.START_TIME,
               S.END_TIME,
               C.HOST_ID,
               C.ENTITY_ID,
               S.DESCRIPTION
      HAVING SUM(CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0
      ORDER BY CAT.DISPLAY_ORDER ASC NULLS LAST,
               CAT.TITLE,
               S.DISPLAY_ORDER ASC NULLS LAST,
               S.TITLE,
               PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID))
GROUP BY TITLE_CAT, DISP_ORDER_CAT, DESC_CAT, SCENARIO_ID, DISP_ORDER_SCEN, TITLE_SCEN, TIME_TO_CUT_OFF, IsScenarioAlertable, DESC_SCEN
ORDER BY DISP_ORDER_CAT ASC NULLS LAST,
         TITLE_CAT,
         DISP_ORDER_SCEN ASC NULLS LAST,
         TITLE_SCEN;

-- Return also the sum of counts for all category
SELECT SUM(CASE WHEN pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END)
INTO vCountAllCateg
FROM P_SCENARIO_COUNTS C
         INNER JOIN P_SCENARIO S
                    ON (S.SCENARIO_ID = C.SCENARIO_ID)
         LEFT OUTER JOIN P_CATEGORY CAT
                         ON (CAT.CATEGORY_ID = S.CATEGORY_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                          pRoleID,
                                          C.ENTITY_ID,
                                          C.CURRENCY_CODE) < 2
  AND SUBSTR (PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                C.HOST_ID,
                                                pRoleID,
                                                C.ENTITY_ID), 1, 1) = 'Y'
  AND DECODE(pAlertableOnly, 'Y', SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3),'Y') LIKE '%Y%'
  AND CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END > 0
  AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE pCcyGroup IN ('All', CURRENCY_GROUP_ID) UNION SELECT pCcyGroup FROM DUAL))
  AND (pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
  AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID) LIKE 'Y' || pCallOption || '_'
  AND CAT.DISPLAY_TAB = nvl(pSelectedTab, CAT.DISPLAY_TAB);
EXCEPTION
          WHEN OTHERS
          THEN
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_POPULATE_SCENARIO_CATEG',
                           SQLCODE,
                           SQLERRM
                          );
END;

   -- Return last run date and duration execution for a given scenario
   FUNCTION fn_get_last_run_duration (pSCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE) RETURN VARCHAR2
   IS
        vLastRunDuration VARCHAR2(200);
BEGIN
SELECT TO_CHAR(LAST_RUN_DATE,'DD/MM/YYYY HH24:MI:SS') || ', taking ' ||
       CASE WHEN LAST_RUN_DURATION_SECS > 0 AND LAST_RUN_DURATION_SECS < 1 THEN '0' ELSE '' END || LAST_RUN_DURATION_SECS || ' seconds'
INTO vLastRunDuration
FROM P_SCENARIO_SYSTEM
WHERE SCENARIO_ID = pSCENARIO_ID;
RETURN vLastRunDuration;
EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN '';
END;

   -- This procedure defines whether any scenario is accessible to the user having non zero count
   -- and returns the count of Flash/Popup and Email flagged scenarios: Querying table P_SCENARIO_COUNTS in conjunction with P_NOTIFY_SCENARIO
   -- Added by Saber Chebka, SwallowTech Tunisia
   PROCEDURE PROC_ALERTABLE_SCENARIO_COUNT(
                                   pRoleID IN S_ROLE.ROLE_ID%TYPE,
                                   pTHRESHOLD IN VARCHAR,
                                   vCountPopUp OUT NUMBER,
                                   vCountFlash OUT NUMBER,
                                   vCountEmail OUT NUMBER) IS
BEGIN
WITH cnts
         AS (  SELECT c.scenario_id,
                      c.host_id,
                      c.entity_id,
                      NVL (SUM (DECODE (pthreshold, 'Y', c.scenario_count_over_t, c.scenario_count)), 0) AS cnt
               --Count over threshold or not
               FROM p_scenario_counts c
                        INNER JOIN p_scenario s ON (s.scenario_id = c.scenario_id)
               WHERE     s.active_flag = 'Y'
                 AND pk_application.fngetcurrencyaccess (c.host_id, pRoleId, c.entity_id, c.currency_code) < 2 --Having access to currency
                 AND pkg_alert.fn_test_start_end_time (GLOBAL_VAR.SYS_DATE, s.start_time, s.end_time) = 'Y'
               GROUP BY c.scenario_id, c.host_id, c.entity_id)
SELECT SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'YY___'
                    THEN NVL (cnt, 0)
                ELSE 0
    END) vcountpopup,
       SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'Y_Y__'
                    THEN NVL (cnt, 0)
                ELSE 0
           END) vcountflash,
       SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'Y__Y_'
                    THEN NVL (cnt, 0)
                ELSE 0
           END) vcountemail
INTO vcountpopup, vcountflash, vcountemail
FROM cnts c;
EXCEPTION
          WHEN OTHERS
          THEN
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_ALERTABLE_SCENARIO_COUNT',
                           SQLCODE,
                           SQLERRM
                          );
END;

   -- Return the facility access for a given role
   FUNCTION fn_Get_Facility_Access(p_host_id in VARCHAR2,
                                 p_role_id in VARCHAR2,
                                 p_entity_id in VARCHAR2,
                                 p_currency_code in VARCHAR2,
                                 p_facility_id in VARCHAR2) return VARCHAR2
   IS
      vAccessFacility P_MENU_ACCESS.ACCESS_ID%TYPE;
BEGIN
SELECT GREATEST(P.ACCESS_ID, PK_APPLICATION.fnGetCurrencyAccess(p_host_id, p_role_id, p_entity_id, p_currency_code))
INTO vAccessFacility
FROM P_MENU_ACCESS P, P_FACILITY F
WHERE P.ROLE_ID = p_role_id
  AND P.MENU_ITEM_ID = F.SEC_MENU_ITEM_ID
  AND F.FACILITY_ID = p_facility_id;

RETURN vAccessFacility;
EXCEPTION
     WHEN NO_DATA_FOUND THEN
        RETURN '2';
END;

   -- return the match list for a given scenario
   PROCEDURE proc_get_list_match (p_scenario_id      in VARCHAR2,
                                  p_entity_id        in VARCHAR2,
                                  p_currency_code    in VARCHAR2,
                                  p_threshold        in VARCHAR2,
                                  p_CcyGroup         in VARCHAR2,
                                  p_ref              in out sys_refcursor) is
     vQuery VARCHAR2(2000);
     vColScenario VARCHAR2(100);
     MATCH_DISP_MOV_ROW VARCHAR2(100) := 'MATCH_DISPLAY_MOVEMENT_ROW';
BEGIN
      vQuery := 'SELECT p.match_id, PKG_ALERT.FN_IS_ROW_HIGHLIGHTED(''' || MATCH_DISP_MOV_ROW || ''', p.host_id, p.entity_id, p.currency_code, p.match_hi_value_date, NULL, '''||p_threshold||''', null, null, p.match_id) scenario_highlighting
      FROM TABLE (PKG_ALERT.FN_GET_MATCH_ROWS (PKG_ALERT.FN_GET_CUR (PKG_ALERT.FN_GET_QUERY_TEXT(''' || p_scenario_id || ''')))) p, s_currency s
      WHERE p.currency_code = s.currency_code
        and p.entity_id = s.entity_id
        and p.host_id = s.host_id';

BEGIN
SELECT replace(AMT_THRESHOLD_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
EXCEPTION
          WHEN OTHERS THEN
              NULL;
END;
        IF p_threshold = 'Y' AND vColScenario IS NOT NULL THEN
            vQuery := vQuery || ' AND p.' || vColScenario || ' >= s.threshold_product ';
END IF;

        IF NVL(p_CcyGroup,'All') != 'All' AND vColScenario IS NOT NULL THEN
            vQuery := vQuery || ' AND s.currency_group_id = ''' || p_CcyGroup || '''';
END IF;

        IF p_currency_code != 'All' THEN
            vQuery := vQuery || ' AND s.currency_code = ''' || p_currency_code || '''';
END IF;

       IF p_entity_id != 'All' THEN
BEGIN
SELECT replace(SEC_ENTITY_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
IF vColScenario IS NOT NULL THEN
                vQuery := vQuery || ' AND p.' || vColScenario || ' = ''' || p_entity_id || '''';
END IF;
EXCEPTION
            WHEN OTHERS THEN
                NULL;
END;
END IF;

      IF p_currency_code != 'All' THEN
BEGIN
SELECT replace(SEC_CURRENCY_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
IF vColScenario IS NOT NULL THEN
                vQuery := vQuery || ' AND p.' || vColScenario || ' = ''' || p_currency_code || '''';
END IF;
EXCEPTION
            WHEN OTHERS THEN
                NULL;
END;
END IF;
OPEN p_ref FOR vQuery;
EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.proc_get_list_match',
                       SQLCODE,
                       SQLERRM
                      );
END;

   -- Return access for facility menu for a given role
   FUNCTION fn_get_facility_menu_access ( pHost_id     in VARCHAR2,
                                          pFacility_id in VARCHAR2,
                                          pRole_id     in VARCHAR2) return VARCHAR2 is
      vaccessfacility   p_menu_access.access_id%TYPE;
BEGIN
SELECT PK_APPLICATION.fnGetMenuAccess(pHost_id, pRole_ID, NVL(SEC_MENU_ITEM_ID,-1))
INTO vAccessFacility
FROM P_FACILITY
WHERE FACILITY_ID = pFacility_id;
RETURN vAccessFacility;
EXCEPTION
      WHEN NO_DATA_FOUND THEN
          RETURN '2';
END;

   /*
   Execute the generic scenario query and return refcursor of records for a given page
   and return the number of all records for all pages.
   */
   PROCEDURE PRC_EXEC_GENERIC_SCENARIO ( p_Scenario_Id          VARCHAR2,
                                         p_FILTER               VARCHAR2,
                                         p_ORDER                VARCHAR2,
                                         p_ROW_BEGIN            NUMBER,
                                         p_ROW_END              NUMBER,
                                         p_ASC_DESC             VARCHAR2,
                                         p_RoleID               VARCHAR2,
                                         p_CcyGroup             VARCHAR2 DEFAULT NULL,
                                         p_Threshold            VARCHAR2 DEFAULT 'N',
                                         p_CUR_RES       IN OUT SYS_REFCURSOR,
                                         p_Count_Rows      OUT  NUMBER,
                                         p_Query_String    OUT VARCHAR2)
IS
       vFILTER          VARCHAR2 (32000) := '';
       vSQL             CLOB;
       vSqlCounts       CLOB;
       vSecEntityCol    VARCHAR2 (100);
       vSecCurrencyCol  VARCHAR2 (100);
       vSecHostCol      VARCHAR2 (100);
       vAmtThreshold    VARCHAR2 (100);
       vSelect          CLOB;
       vFrom            CLOB;
       vWhere           CLOB;
       curRefCursor     sys_refcursor;
       CurRefCursorFilter sys_refcursor;
       vDateFormat      VARCHAR2(30);
       VINDVAL          TINDVAL;
       VINDVALUE        VARCHAR2(4000);
       VCOLIDX          NUMBER(5);
       VCOLI            TCOLI;
BEGIN
       -- get date format from s_system_parameters
       vDateFormat := pk_utility.fn_get_date_format('Y');
       -- transform the filter given from java side to a WHERE clause to build the query
       PKG_ALERT.spSplitter(p_FILTER, '|', CurRefCursorFilter);
       LOOP
FETCH CurRefCursorFilter INTO VINDVALUE, VCOLIDX;
            EXIT WHEN CurRefCursorFilter%NOTFOUND;

            IF VINDVALUE != 'All' THEN
                PKG_ALERT.spSplitter(VINDVALUE, '$#$', CurRefCursor);
FETCH CurRefCursor BULK COLLECT INTO VINDVAL, VCOLI;
IF vFILTER IS NOT NULL THEN
                    vFILTER := vFILTER || ' AND ';
END IF;
                IF (LOWER(VINDVAL(3)) = 'date') THEN
                    vFILTER := vFILTER || 'TO_DATE(TO_CHAR(SCENQUERY.' || VINDVAL(1) || ',''' || vDateFormat ||''')' || ','''|| vDateFormat ||''')' || '= TO_DATE(''' || VINDVAL(2) || ''','''|| vDateFormat ||''')';
ELSE
                    vFILTER := vFILTER || ' SCENQUERY.' || VINDVAL(1) || CASE   WHEN VINDVAL(2)='(EMPTY)' THEN ' IS NULL'
                                                                                WHEN VINDVAL(2)='(NOT EMPTY)' THEN ' IS NOT NULL'
                                                                                ELSE '=''' || VINDVAL(2) || ''''
END;
END IF;
END IF;
END LOOP;

BEGIN
SELECT SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, AMT_THRESHOLD_COL
INTO vSecHostCol, vSecEntityCol, vSecCurrencyCol, vAmtThreshold
FROM P_SCENARIO
WHERE SCENARIO_ID = p_Scenario_id;
EXCEPTION
            WHEN NO_DATA_FOUND THEN
                NULL;
END;

       -- build the query
       vSelect := 'SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY ';
       IF p_ORDER IS NULL THEN
            vSelect := vSelect || '1';
ELSE
            vSelect := vSelect || 'SCENQUERY.' || p_ORDER || ' ' || p_ASC_DESC;
END IF;
       vSelect := vSelect || ')ROW_, SCENQUERY.*';

       vFrom := ' FROM ('
                || CHR(10)
                || PKG_ALERT.FN_GET_QUERY_TEXT(p_Scenario_id)
                || CHR(10)
                || ') SCENQUERY';
       IF p_Threshold = 'Y' AND vAmtThreshold IS NOT NULL THEN
            vFrom := vFrom || ',S_CURRENCY S';
END IF;

       IF vFILTER IS NOT NULL THEN
            vWhere := ' WHERE ' || vFILTER;
END IF;

       IF   vSecCurrencyCol IS NOT NULL
        AND vSecEntityCol IS NOT NULL
        AND vSecHostCol IS NOT NULL THEN
            IF INSTR(vWhere, 'WHERE') > 0 THEN
                vWhere := vWhere || ' AND ';
ELSE
                vWhere := vWhere || ' WHERE ';
END IF;
            vWhere := vWhere || ' (SCENQUERY.' || vSecCurrencyCol ||' IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE CURRENCY_GROUP_ID = ''' || p_CcyGroup
              || ''') OR NVL(''' || p_CcyGroup || ''', ''All'') = ''All'') AND PK_APPLICATION.FNGETCURRENCYACCESS(SCENQUERY.' || vSecHostCol ||', '''|| p_RoleID ||''', SCENQUERY.'|| vSecEntityCol
              ||' ,SCENQUERY.'|| vSecCurrencyCol || ') < 2';
END IF;

       IF p_Threshold = 'Y' AND vAmtThreshold IS NOT NULL THEN
            IF INSTR(vWhere, 'WHERE') > 0 THEN
                vWhere := vWhere || ' AND ';
ELSE
                vWhere := vWhere || ' WHERE ';
END IF;
            vWhere := vWhere || ' SCENQUERY.' || vAmtThreshold || '>= S.THRESHOLD_PRODUCT AND SCENQUERY.' || vSecCurrencyCol || '=S.CURRENCY_CODE AND S.ENTITY_ID = ' ||'SCENQUERY.' || vSecEntityCol;
END IF;

     -- Get the number of records
       vSqlCounts := 'SELECT COUNT(*) ' || vFrom || ' ' || vWhere;

       -- Apply the filter to give only records for a given page
       IF p_ROW_BEGIN || p_ROW_END  != '00' THEN
            vWhere := vWhere || ') WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ' || p_ROW_END;
ELSE
            vWhere := vWhere || ')';
END IF;

       vSQL := vSelect || ' ' || vFrom || ' ' || vWhere;

       p_Query_String := SUBSTR(CAST (vSQL AS VARCHAR2), 1, INSTR(vSQL, ' WHERE ROW_ BETWEEN ') - 1) || ' WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ';

Execute immediate cast(vSqlCounts as VARCHAR2) INTO p_Count_Rows;

OPEN p_cur_res FOR cast(vSQL as VARCHAR2);

EXCEPTION
           WHEN OTHERS
           THEN
              sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.PRC_EXEC_GENERIC_SCENARIO',
                            SQLCODE,
                            SQLERRM
                          );
END;

   /*
    This procedure is used to split a string by using a delimiter
    IT is used instead of PK_UTILITY.SPSPLITTER procedure because this last one don't return
    the last sub-string (after the last delimiter).
   */
   PROCEDURE spSplitter(
                        pSplitString  IN  VARCHAR2,
                        pDeLimiter    IN  VARCHAR2,
                        pMyCur        OUT SYS_REFCURSOR
                       )
   -- -------------------------------------------------------------------------------
   -- Summary : This procedure is used to split the parameter(pSplitString) by using delimiter(pDeLimiter).
   --           pSplitString = 'XXXXpDeLimiterYYYYpDeLimiterZZZZpDeLimiter'.
   --           pDeLimiter   =  | # @ $ ,
   --           This is used in package PK_MOVEMENT_SUMMARY and procedure PARTY_LIST.
   -- -------------------------------------------------------------------------------
IS
BEGIN
      -- Split the supplied parameter(pDeLimiter) string as individual value when pDeLimiter is NOT NULL.
      IF pDeLimiter IS NOT NULL THEN
         OPEN pMyCur
          FOR SELECT REGEXP_SUBSTR (pSplitString, '[^' || pDeLimiter || ']+', 1, LEVEL) ColValue, LEVEL idx
              FROM DUAL
                  CONNECT BY LEVEL - 1 <= LENGTH (REGEXP_REPLACE (pSplitString, '[^' || pDeLimiter || ']+'));
ELSE
         -- Return the cursor with NULL value When pDeLimiter is NULL.
         OPEN pMyCur
          FOR SELECT NULL ColValue, NULL idx
              FROM DUAL;
END IF;
END spSplitter;
   /*

      fn_get_cur_curr_entity()

      A new function in the PKG_ALERT.fn_get_cur_curr_entity which wraps two filters of entity_id
    and currency_code around scenario sql text, if required. (105157)

      PARAMETERS
       p_sqltext         IN VARCHAR2  SQL text (Manditory)
     p_currency_code   IN VARCHAR2  Currency Code or All for ALL currencies  .
     p_entity_id       IN VARCHAR2  p_entity_id entity number  (Expected)
   */

   FUNCTION fn_get_cur_curr_entity (
            p_sqltext         IN VARCHAR2,
            p_currency_code   IN VARCHAR2 DEFAULT NULL,
            p_entity_id       IN VARCHAR2 )
   RETURN SYS_REFCURSOR
   IS
     r_cursor      SYS_REFCURSOR;
     v_sql         VARCHAR2 (32000) := NULL;
     v_where_and   VARCHAR2 (5) := 'WHERE';
     v_curr_ind    BOOLEAN := FALSE;
BEGIN
     -- This Should never happen.
     IF p_sqltext IS NULL THEN
        RETURN NULL;
END IF;

     IF (UPPER (NVL(p_currency_code,'ALL')) = 'ALL' AND UPPER(NVL(p_entity_id,'ALL')) = 'ALL')
        THEN

        -- Both Currency_code and entity_id is ALL So therfore NO filter is needed.
        OPEN r_cursor FOR p_sqltext;
ELSE
        -- Wrap around SQL
        v_sql := 'SELECT fn_get_cur.* FROM ( ' || p_sqltext || ' ) fn_get_cur ';

        --
        -- Optional currency_code filter.
        --
        IF (UPPER (NVL(p_currency_code,'ALL')) <> 'ALL')
        THEN
           v_sql := v_sql
                 || v_where_and
                 || ' fn_get_cur.currency_code = :p_currency_code ';
           v_where_and := ' AND';
           v_curr_ind := TRUE;
END IF;
        --
        -- Manditory p_entity_id filter.
        --
        v_sql := v_sql
              || v_where_and
              || ' fn_get_cur.entity_id = :p_entity_id ';

         --v_where_and := 'AND';

        --
        -- Open cursor for sql text
        --
        IF v_curr_ind THEN
           OPEN r_cursor FOR v_sql USING p_currency_code, p_entity_id;
ELSE
           OPEN r_cursor FOR v_sql USING p_entity_id;
END IF;

END IF;

RETURN r_cursor;

EXCEPTION

      WHEN OTHERS
      THEN

         sp_error_log('',
                      'SYSTEM',
                      'DBSERVER',
                      'PKG_ALERT.fn_get_cur_curr_entity',
                      SQLCODE,
                      SQLERRM
                     );
         RAISE;
RETURN NULL;
END;

   FUNCTION FN_GET_SCENARIO_INSTANCE_ROW (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                          P_SCHEDULED_QUERY P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                          P_USER_ID         VARCHAR2  DEFAULT CONST_SYS_USER)
   RETURN TAB_SCENARIO_INSTANCE PIPELINED
   IS
        V_REF               SYS_REFCURSOR;
        V_ROW               REC_SCENARIO_INSTANCE;
        V_INST_UNIQUE_EXPR  P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
        V_SEC_HOST_COL      P_SCENARIO.SEC_HOST_COL%TYPE;
        V_SEC_ENTITY_COL    P_SCENARIO.SEC_ENTITY_COL%TYPE;
        V_SEC_CURRENCY_COL  P_SCENARIO.SEC_CURRENCY_COL%TYPE;
        V_ACCOUNT_ID_COL    P_SCENARIO.ACCOUNT_ID_COL%TYPE;
        V_SIGN_COL          P_SCENARIO.SIGN_COL%TYPE;
        V_AMOUNT_COL        P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
        V_MOVEMENT_ID_COL   P_SCENARIO.MOVEMENT_ID_COL%TYPE;
        V_MATCH_ID_COL      P_SCENARIO.MATCH_ID_COL%TYPE;
        V_PAYMENT_ID_COL    P_SCENARIO.PAYMENT_ID_COL%TYPE;
        V_SWEEP_ID_COL      P_SCENARIO.SWEEP_ID_COL%TYPE;
        V_OTHER_ID_COL      P_SCENARIO.OTHER_ID_COL%TYPE;
        V_AMT_THRESH_COL    P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
        V_VALUE_DATE_COL    P_SCENARIO.VALUE_DATE_COL%TYPE;
        V_LIST_UNIQUE_IDENT TAB_UNIQUE_IDENT;
        V_IS_UNIQUE_ROW     VARCHAR2(1) := 'Y';
        V_ERROR_LOC         VARCHAR2(5);
        V_SCENARIO_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE;

        V_SCENARIO_ID           TAB_VARCHAR;
        V_UNIQUE_IDENT          TAB_VARCHAR;
        V_HOST_ID               TAB_VARCHAR;
        V_ENTITY_ID             TAB_VARCHAR;
        V_CURRENCY_CODE         TAB_VARCHAR;
        V_ACCOUNT_ID            TAB_VARCHAR;
        V_AMOUNT                TAB_NUMBER;
        V_SIGN                  TAB_VARCHAR;
        V_MOVEMENT_ID           TAB_NUMBER;
        V_MATCH_ID              TAB_NUMBER;
        V_PAYMENT_ID            TAB_NUMBER;
        V_SWEEP_ID              TAB_NUMBER;
        V_OTHER_ID              TAB_VARCHAR;
        V_VALUE_DATE            TAB_DATE;
        V_OVER_THRESHOLD        TAB_VARCHAR;
        V_JSON_ATTRIBUTES       TAB_CLOB;
        V_TAB_LIMIT             NUMBER := 500;
        V_REF_SQL               CLOB;
BEGIN
        V_ERROR_LOC := '10';
SELECT NVL(INSTANCE_UNIQUE_EXPRESSION,'NULL'), NVL(SEC_HOST_COL, 'NULL'), NVL(SEC_ENTITY_COL,'NULL'), NVL(SEC_CURRENCY_COL,'NULL'), NVL(ACCOUNT_ID_COL,'NULL'), NVL(AMT_THRESHOLD_COL,'NULL'), NVL(SIGN_COL,'NULL'), NVL(MOVEMENT_ID_COL,'NULL'), NVL(MATCH_ID_COL,'NULL'), NVL(PAYMENT_ID_COL,'NULL'), NVL(SWEEP_ID_COL,'NULL'), NVL(OTHER_ID_COL, 'NULL'), NVL(AMT_THRESHOLD_COL,'NULL'), NVL(VALUE_DATE_COL, 'NULL')
INTO V_INST_UNIQUE_EXPR, V_SEC_HOST_COL, V_SEC_ENTITY_COL, V_SEC_CURRENCY_COL, V_ACCOUNT_ID_COL, V_AMOUNT_COL, V_SIGN_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_PAYMENT_ID_COL, V_SWEEP_ID_COL, V_OTHER_ID_COL, V_AMT_THRESH_COL, V_VALUE_DATE_COL
FROM P_SCENARIO P
WHERE SCENARIO_ID = P_SCENARIO_ID;

V_ERROR_LOC := '20';
        IF P_SCHEDULED_QUERY IS NOT NULL THEN
            V_ERROR_LOC := '30';
            V_REF_SQL := q'[SELECT ']'
                             || P_SCENARIO_ID      || q'[' "SCENARIO_ID", ]'
                             || V_INST_UNIQUE_EXPR || ' "UNIQUE_EXPRESSION", '
                             || V_SEC_HOST_COL     || ' "HOST_ID", '
                             || V_SEC_ENTITY_COL   || ' "ENTITY_ID", '
                             || V_SEC_CURRENCY_COL || ' "CURRENCY_CODE", '
                             || V_ACCOUNT_ID_COL   || ' "ACCOUNT_ID", '
                             || V_AMOUNT_COL       || ' "AMOUNT", '
                             || V_SIGN_COL         || ' "SIGN", '
                             || V_MOVEMENT_ID_COL  || ' "MOVEMENT_ID", '
                             || V_MATCH_ID_COL     || ' "MATCH_ID", '
                             || V_PAYMENT_ID_COL   || ' "PAYMENT_ID", '
                             || V_SWEEP_ID_COL     || ' "SWEEP_ID", '
                             || V_OTHER_ID_COL     || ' "OTHER_ID", '
                             || V_VALUE_DATE_COL   || ' "VALUE_DATE", '
                             || 'CASE WHEN ' || NVL(V_AMT_THRESH_COL,'NULL') || ' > (SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID='||CASE WHEN V_SEC_HOST_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_HOST_COL END||' AND S.ENTITY_ID='||CASE WHEN V_SEC_ENTITY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_ENTITY_COL END ||' AND S.CURRENCY_CODE='||CASE WHEN V_SEC_CURRENCY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_CURRENCY_COL END ||') THEN ''Y'' ELSE ''N'' END "OVER_THRESHOLD", ';

            DBMS_LOB.APPEND (V_REF_SQL, 'PKG_ALERT.FN_GET_QUERYRESULT_AS_JSON (q''[SELECT * FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCHEDULED_QUERY);
            DBMS_LOB.APPEND (V_REF_SQL, ') WHERE ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' = '']'' || ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' || q''['']'') "JSON_ATTRIBUTES" FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCHEDULED_QUERY);
            DBMS_LOB.APPEND (V_REF_SQL, ') SCENQUERY ');

            V_REF := PKG_ALERT.FN_GET_CUR (V_REF_SQL);
ELSE
          -- Get scenario query text
          V_SCENARIO_QUERY_TEXT := PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID);
          IF V_SCENARIO_QUERY_TEXT IS NOT NULL THEN
            V_ERROR_LOC := '40';
            V_REF_SQL := q'[SELECT ']'
                             || P_SCENARIO_ID      || q'[' "SCENARIO_ID", ]'
                             || V_INST_UNIQUE_EXPR || ' "UNIQUE_EXPRESSION", '
                             || V_SEC_HOST_COL     || ' "HOST_ID", '
                             || V_SEC_ENTITY_COL   || ' "ENTITY_ID", '
                             || V_SEC_CURRENCY_COL || ' "CURRENCY_CODE", '
                             || V_ACCOUNT_ID_COL   || ' "ACCOUNT_ID", '
                             || V_AMOUNT_COL       || ' "AMOUNT", '
                             || V_SIGN_COL         || ' "SIGN", '
                             || V_MOVEMENT_ID_COL  || ' "MOVEMENT_ID", '
                             || V_MATCH_ID_COL     || ' "MATCH_ID", '
                             || V_PAYMENT_ID_COL   || ' "PAYMENT_ID", '
                             || V_SWEEP_ID_COL     || ' "SWEEP_ID", '
                             || V_OTHER_ID_COL     || ' "OTHER_ID", '
                             || V_VALUE_DATE_COL   || ' "VALUE_DATE", '
                             || 'CASE WHEN ' || NVL(V_AMT_THRESH_COL,'NULL') || ' > (SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID='||CASE WHEN V_SEC_HOST_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_HOST_COL END||' AND S.ENTITY_ID='||CASE WHEN V_SEC_ENTITY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_ENTITY_COL END ||' AND S.CURRENCY_CODE='||CASE WHEN V_SEC_CURRENCY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_CURRENCY_COL END ||') THEN ''Y'' ELSE ''N'' END "OVER_THRESHOLD", ';

            DBMS_LOB.APPEND (V_REF_SQL, 'PKG_ALERT.FN_GET_QUERYRESULT_AS_JSON (q''[SELECT * FROM (]''');
            DBMS_LOB.APPEND (V_REF_SQL, ' || PKG_ALERT.FN_GET_QUERY_TEXT (''');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCENARIO_ID);
            DBMS_LOB.APPEND (V_REF_SQL, ''') || ');
            DBMS_LOB.APPEND (V_REF_SQL, 'q''[) WHERE ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' = '']'' || ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' || q''['']'') "JSON_ATTRIBUTES" FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, V_SCENARIO_QUERY_TEXT);
            DBMS_LOB.APPEND (V_REF_SQL, ') SCENQUERY ');

            V_REF := PKG_ALERT.FN_GET_CUR (V_REF_SQL);

END IF;
END IF;

        IF V_REF IS NOT NULL THEN
          V_ERROR_LOC := '50';
          LOOP
FETCH V_REF BULK COLLECT INTO V_SCENARIO_ID, V_UNIQUE_IDENT, V_HOST_ID, V_ENTITY_ID, V_CURRENCY_CODE, V_ACCOUNT_ID, V_AMOUNT, V_SIGN, V_MOVEMENT_ID, V_MATCH_ID, V_PAYMENT_ID, V_SWEEP_ID, V_OTHER_ID, V_VALUE_DATE, V_OVER_THRESHOLD, V_JSON_ATTRIBUTES LIMIT V_TAB_LIMIT;
            EXIT WHEN (V_SCENARIO_ID.FIRST IS NULL OR V_SCENARIO_ID.LAST IS NULL);

FOR I IN V_SCENARIO_ID.FIRST..V_SCENARIO_ID.LAST
            LOOP
              V_ERROR_LOC := '60';
              V_ROW.SCENARIO_ID := V_SCENARIO_ID(I);
              V_ROW.INSTANCE_UNIQUE_EXPRESSION := V_INST_UNIQUE_EXPR;
              V_ROW.UNIQUE_IDENTIFIER := V_UNIQUE_IDENT(I);

              IF V_LIST_UNIQUE_IDENT.EXISTS(V_UNIQUE_IDENT(I)) THEN
                V_IS_UNIQUE_ROW := 'N';
ELSE
                V_LIST_UNIQUE_IDENT(V_UNIQUE_IDENT(I)) := V_UNIQUE_IDENT(I);
END IF;

              V_ROW.HOST_ID := V_HOST_ID(I);
              V_ROW.ENTITY_ID := V_ENTITY_ID(I);
              V_ROW.CURRENCY_CODE := V_CURRENCY_CODE(I);
              V_ROW.ACCOUNT_ID := V_ACCOUNT_ID(I);
              V_ROW.AMOUNT := V_AMOUNT(I);
              V_ROW.SIGN := V_SIGN(I);
              V_ROW.MOVEMENT_ID := V_MOVEMENT_ID(I);
              V_ROW.MATCH_ID := V_MATCH_ID(I);
              V_ROW.PAYMENT_ID := V_PAYMENT_ID(I);
              V_ROW.SWEEP_ID := V_SWEEP_ID(I);
              V_ROW.OTHER_ID := V_OTHER_ID(I);
              V_ROW.VALUE_DATE := V_VALUE_DATE(I);
              V_ROW.OVER_THRESHOLD := V_OVER_THRESHOLD(I);
              V_ROW.JSON_ATTRIBUTES := V_JSON_ATTRIBUTES(I);
              V_ROW.IS_UNIQUE_ROW := V_IS_UNIQUE_ROW;

PIPE ROW (V_ROW);
END LOOP;
END LOOP;

CLOSE V_REF;
ELSE
          V_ERROR_LOC := '70';
PIPE ROW (NULL);
END IF;
EXCEPTION
      WHEN NO_DATA_NEEDED THEN
        NULL;
WHEN OTHERS THEN
        IF V_REF%ISOPEN THEN
          CLOSE V_REF;
END IF;

        sp_error_log (GLOBAL_VAR.FN_GET_HOST,
                       P_USER_ID,
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_ID || ' at location: ' || v_error_loc || chr(10) || DBMS_UTILITY.format_error_backtrace ,
                        SQLCODE,
                        SQLERRM
                       );

END;

   PROCEDURE SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE,
                                       P_LOG_TEXT                   P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                       P_LOG_USER                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
IS
BEGIN
INSERT INTO P_SCENARIO_INSTANCE_LOG (ID,
                                     INSTANCE_ID,
                                     LOG_DATETIME,
                                     LOG_TEXT,
                                     LOG_USER)
VALUES (SEQ_P_SCENARIO_INSTANCE_LOG.NEXTVAL,
        P_SCENARIO_INSTANCE_ID,
        GLOBAL_VAR.SYS_DATE,
        SUBSTR(P_LOG_TEXT, 1, 200),
        P_LOG_USER);
END;

   FUNCTION FN_GET_COL_AUTO (P_PROGRAM_ID       P_SCENARIO_GUI_ALERT_FACILITY.PROGRAM_ID%TYPE)
   RETURN VARCHAR2 RESULT_CACHE
   IS
        V_COL_AUTO  VARCHAR2(4000);
BEGIN
        V_COL_AUTO :=
        CASE P_PROGRAM_ID
            -- Create a movement
            WHEN '5' THEN 'MOVEMENT_ID,INPUT_DATE,UPDATE_DATE,|P_MOVEMENT_SEQUENCE.NEXTVAL,GLOBAL_VAR.SYS_DATE,GLOBAL_VAR.SYS_DATE,'
            -- Update a movement
            WHEN '6' THEN 'UPDATE_DATE,|GLOBAL_VAR.SYS_DATE,'
            -- Insert/Update Account attribute
            WHEN '115' THEN q'[SEQUENCE_KEY,UPDATE_DATE,|SEQ_P_ACCOUNT_ATTRIBUTE.NEXTVAL,GLOBAL_VAR.SYS_DATE,]'
            -- Make a sweep
            WHEN '12' THEN 'ACCOUNT_LEVEL_FLAG,AUTO_SWEEP_SWITCH_MAIN,AUTO_SWEEP_SWITCH_SUB,|PGT,S,S'
            -- Insert/Update Balance
            WHEN '19' THEN 'UPDATE_DATE,|GLOBAL_VAR.SYS_DATE,'
            ELSE NULL
END;
RETURN V_COL_AUTO;
END;

   PROCEDURE SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID     P_SCENARIO_INSTANCE.ID%TYPE,
                                     PV_NEW_STATUS              P_SCENARIO_INSTANCE.STATUS%TYPE,
                                     P_LOG_TEXT                 P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                     P_LOG_USER                 P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
                                     P_EVENT_STATUS             P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE DEFAULT NULL
                                     )
IS
        V_SQL       VARCHAR2(4000);
BEGIN
        V_SQL := q'[
        UPDATE P_SCENARIO_INSTANCE
           SET STATUS = :PV_NEW_STATUS
        ]' ||
        CASE
            WHEN PV_NEW_STATUS = 'A' THEN ', LAST_RAISED_DATETIME = GLOBAL_VAR.SYS_DATE'
            WHEN PV_NEW_STATUS = 'R' THEN ', RESOLVED_DATETIME = GLOBAL_VAR.SYS_DATE' ||
                                          ', RESOLVED_BY_USER = ''' || P_LOG_USER || ''''
END ||
        CASE
            WHEN P_EVENT_STATUS IS NOT NULL THEN q'[, EVENTS_LAUNCH_STATUS = ']' || P_EVENT_STATUS || q'[']'
END ||
        q'[ WHERE ID = :P_SCENARIO_INSTANCE_ID
              AND STATUS <> ']' || PV_NEW_STATUS || q'[']';

        -- No need to update status to PV_NEW_STATUS if instance has already this status
EXECUTE IMMEDIATE V_SQL USING PV_NEW_STATUS, P_SCENARIO_INSTANCE_ID;

IF SQL%ROWCOUNT > 0 THEN
        -- If new status is different to Active then delete the record from P_SCENARIO_ACTIVE_INSTANCE
          IF PV_NEW_STATUS NOT IN ('A','P','O') THEN
            DELETE P_SCENARIO_ACTIVE_INSTANCE
             WHERE ID = P_SCENARIO_INSTANCE_ID;
ELSE
            INSERT INTO P_SCENARIO_ACTIVE_INSTANCE(ID)
SELECT P_SCENARIO_INSTANCE_ID
FROM DUAL
WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = P_SCENARIO_INSTANCE_ID);
END IF;

INSERT INTO P_SCENARIO_INSTANCE_LOG (ID, INSTANCE_ID, LOG_DATETIME, LOG_TEXT, LOG_USER)
VALUES ( SEQ_P_SCENARIO_INSTANCE_LOG.NEXTVAL, P_SCENARIO_INSTANCE_ID, GLOBAL_VAR.SYS_DATE, P_LOG_TEXT, P_LOG_USER);
END IF;
END;

   PROCEDURE SP_UPD_SCENARIO_INSTANCE (P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
                                       P_USER_ID               P_SCENARIO_INSTANCE.RESOLVED_BY_USER%TYPE DEFAULT CONST_SYS_USER)
IS
        V_STATUS                        P_SCENARIO_INSTANCE.STATUS%TYPE;
        V_EVENTS_LAUNCH_STATUS          P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE;
        V_RAISED_DATETIME               P_SCENARIO_INSTANCE.RAISED_DATETIME%TYPE;
        V_PEND_RESOL_TIME_LIMIT         NUMBER;
        V_PEND_RESOL_QUERY_TEXT         P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT%TYPE;
        V_ALLOW_RERAISE_AFTER_EXPIRY    P_SCENARIO.ALLOW_RERAISE_AFTER_EXPIRY%TYPE;
        V_RERAISE_INTERVAL_MINS         NUMBER;
        V_LAST_RAISED_DATETIME          P_SCENARIO_INSTANCE.LAST_RAISED_DATETIME%TYPE;
        V_INSTANCE_EXPIRY_MINS          NUMBER;
        V_RESULT_PEND_RES_QRY_TXT       NUMBER;
        V_LOCK                          VARCHAR2(1);
        VCOUNT                          NUMBER;
        V_SCENARIO_INSTANCE_ID          P_SCENARIO_INSTANCE.ID%TYPE;

CURSOR CUR_INSTANCES
        IS
SELECT PSI.ID, PSI.STATUS, PSI.EVENTS_LAUNCH_STATUS, PSI.LAST_RAISED_DATETIME,
       PS.PENDING_RESOLUTION_TIME_LIMIT/(24*60), PS.PENDING_RESOLUTION_QUERY_TEXT,
       PS.ALLOW_RERAISE_AFTER_EXPIRY, PS.RERAISE_INTERVAL_MINS/(24*60), PSI.LAST_RAISED_DATETIME,
       PS.INSTANCE_EXPIRY_MINS/(24*60)
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO PS ON PSI.SCENARIO_ID = PS.SCENARIO_ID
WHERE PS.SCENARIO_ID = P_SCENARIO_ID
  AND (   (PSI.STATUS = 'A' AND PS.INSTANCE_EXPIRY_MINS > 0)
    OR (PSI.STATUS = 'P' AND (NVL(PS.PENDING_RESOLUTION_TIME_LIMIT, -1) > 0 OR LTRIM(RTRIM(PS.PENDING_RESOLUTION_QUERY_TEXT)) IS NOT NULL))
    OR (PSI.STATUS = 'R' AND (PS.ALLOW_RERAISE_AFTER_EXPIRY = 'I' OR (PS.ALLOW_RERAISE_AFTER_EXPIRY = 'A' AND PS.RERAISE_INTERVAL_MINS > 0)))
    );

BEGIN
OPEN CUR_INSTANCES;
LOOP
FETCH CUR_INSTANCES INTO V_SCENARIO_INSTANCE_ID, V_STATUS, V_EVENTS_LAUNCH_STATUS, V_RAISED_DATETIME,
                   V_PEND_RESOL_TIME_LIMIT, V_PEND_RESOL_QUERY_TEXT,
                   V_ALLOW_RERAISE_AFTER_EXPIRY, V_RERAISE_INTERVAL_MINS, V_LAST_RAISED_DATETIME,
                                   V_INSTANCE_EXPIRY_MINS;
          EXIT WHEN CUR_INSTANCES%NOTFOUND;

            -- Lock current instance to avoid issues that arise from a manual user interacting
            -- with the same instance at the same time
SELECT 'Y' INTO V_LOCK
FROM P_SCENARIO_INSTANCE
WHERE ID = V_SCENARIO_INSTANCE_ID FOR UPDATE NOWAIT;

-- Identifying existing Active instances where instance expiry time is reached/exceeded.
-- These should be Resolved
IF V_STATUS = 'A' THEN
            IF GLOBAL_VAR.SYS_DATE >= (V_LAST_RAISED_DATETIME + V_INSTANCE_EXPIRY_MINS)
               -- Do not resolve expired instances where event fails
               AND V_EVENTS_LAUNCH_STATUS <> 'F'
            THEN
               SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'R', q'[Status passed to Resolved (Instance expiry time reached)]', P_USER_ID);
END IF;
            -- If status Pending exceeds limit then set status to Overdue
        ELSIF V_STATUS = 'P' THEN
                IF (V_PEND_RESOL_TIME_LIMIT > 0 AND GLOBAL_VAR.SYS_DATE >= V_RAISED_DATETIME + V_PEND_RESOL_TIME_LIMIT) THEN
                    -- Set status as Overdue
                  SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'O', q'[Status passed to Overdue (Pending exceeds time limit)]', P_USER_ID);

ELSE
                    -- Check P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT
                    IF V_PEND_RESOL_QUERY_TEXT IS NOT NULL THEN
                      V_RESULT_PEND_RES_QRY_TXT := FN_GET_RESULT_RESOL_QUERY(V_SCENARIO_INSTANCE_ID);

                      IF V_RESULT_PEND_RES_QRY_TXT > 0 THEN
                            -- Set status as Resolved
                          SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'R', q'[Status passed to Resolved (Pending Resolution Query satisfied)]', P_USER_ID);
END IF;
END IF;
END IF;

            ELSIF V_STATUS = 'R' THEN
                -- Reraise immediately after expiry
                IF V_ALLOW_RERAISE_AFTER_EXPIRY = 'I' THEN
SELECT COUNT(*)
INTO VCOUNT
FROM P_SCENARIO_EVENT_FACILITY GAF
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON GAF.ID = GAM.EVENT_FACILITY_ID
         INNER JOIN P_SCENARIO_INSTANCE PSI ON GAM.SCENARIO_ID = PSI.SCENARIO_ID
WHERE PSI.ID = V_SCENARIO_INSTANCE_ID
  AND GAM.REPEAT_ON_RERAISE = 'Y';
IF VCOUNT > 0 THEN
                    SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (reraise immediately)]', P_USER_ID, 'W');
ELSE
                    SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (reraise immediately)]', P_USER_ID, 'N');
END IF;

                -- reraise after n minutes
                ELSIF V_ALLOW_RERAISE_AFTER_EXPIRY = 'A' THEN
                    IF GLOBAL_VAR.SYS_DATE >= (V_LAST_RAISED_DATETIME + V_INSTANCE_EXPIRY_MINS + V_RERAISE_INTERVAL_MINS) THEN
SELECT COUNT(*)
INTO VCOUNT
FROM P_SCENARIO_EVENT_FACILITY GAF
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON GAF.ID = GAM.EVENT_FACILITY_ID
         INNER JOIN P_SCENARIO_INSTANCE PSI ON GAM.SCENARIO_ID = PSI.SCENARIO_ID
WHERE PSI.ID = V_SCENARIO_INSTANCE_ID
  AND GAM.REPEAT_ON_RERAISE = 'Y';
IF VCOUNT > 0 THEN
                        SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (Reraise after n-minutes)]', P_USER_ID, 'W');
ELSE
                        SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (Reraise after n-minutes)]', P_USER_ID, 'N');
END IF;
END IF;
END IF;

END IF;

          -- COMMIT the current scenario instance to unlock this record
COMMIT;
END LOOP;

       IF CUR_INSTANCES%ISOPEN THEN
          CLOSE CUR_INSTANCES;
END IF;
END;

   -- This function launch the event for a given scenario instance
   -- and return 01 if event has run successfully otherwise (failed) return -1
   FUNCTION FN_LAUNCH_SCEN_EVENT (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                  P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER
   IS
        V_SQL                           CLOB;
        V_SQL_PART1                     CLOB;
        V_SQL_PART2                     CLOB;
        V_MAP_COL                       VARCHAR2(128);
        V_MAP_FROM                      VARCHAR2(128);
        V_DATA_TYPE                     VARCHAR2(30);
        V_IS_MANDATARY                  VARCHAR2(1);
        V_PROGRAM_ID                    P_SCENARIO_EVENT_FACILITY.PROGRAM_ID%TYPE;
        V_COL_AUTO_COL                  VARCHAR2(4000);
        V_COL_AUTO_VAL                  VARCHAR2(4000);
        V_HOST_ID                       S_HOST.HOST_ID%TYPE;
        V_STATUS_AFTER_EVENT_TRIGGER    P_SCENARIO.STATUS_AFTER_EVENT_TRIGGER%TYPE;
        V_RETURN_RESULT                 NUMBER := 1;
        V_ACCOUNT_ID_MAIN               P_SWEEP.ACCOUNT_ID_DR%TYPE;
        V_ACCOUNT_ID_SUB                P_SWEEP.ACCOUNT_ID_CR%TYPE;
        V_AMOUNT                        P_SWEEP.ORIGINAL_SWEEP_AMT%TYPE;
        V_ALIGN_ACC_ID                  P_SWEEP.ALIGN_ACCOUNT_ID%TYPE;
        V_VALUE_DATE                    P_SWEEP.VALUE_DATE%TYPE;
        V_ACCOUNT_LEVEL_FLAG            VARCHAR2(3);
        V_COLUMN_VALUE                  CLOB;
        --V_OUT_SUCCESS_STATUS            NUMBER;
        --V_OUT_SWEEP_ID                  P_SWEEP.SWEEP_ID%TYPE;
        V_AUTHORIZE_FLAG_SUB_IN         VARCHAR2(1);
        V_AUTHORIZE_FLAG_SUB_EX         VARCHAR2(1);
        V_AUTHORIZE_FLAG_MAIN_IN        VARCHAR2(1);
        V_AUTHORIZE_FLAG_MAIN_EX        VARCHAR2(1);
        V_AUTO_SWEEP_SWITCH_MAIN        VARCHAR2(1);
        V_AUTO_SWEEP_SWITCH_SUB         VARCHAR2(1);
        V_SWEEP_GROUP_ID                VARCHAR2(1);
        --V_DATE_FORMAT                   VARCHAR2(10) := 'DD/MM/YYYY';
        V_ENTITY_ID_M                   P_SWEEP.ENTITY_ID_DR%TYPE;
        V_ENTITY_ID_S                   P_SWEEP.ENTITY_ID_CR%TYPE;
        V_AUTO_MANUAL                   VARCHAR2(1);
        V_SETTLE_METHOD_CR              P_SWEEP.SETTL_METHOD_CR%TYPE;
        V_SETTLE_METHOD_DR              P_SWEEP.SETTL_METHOD_DR%TYPE;
        V_SWEEP_BOOKCODE_CR             P_SWEEP.BOOKCODE_CR%TYPE;
        V_SWEEP_BOOKCODE_DR             P_SWEEP.BOOKCODE_DR%TYPE;
        V_ACC_TARGET_BALANCE            P_ACCOUNT.TARGET_BALANCE%TYPE;
        V_ACC_TARGET_BALANCE_TYPE       P_ACC_SWEEP_SCHEDULE.TARGET_BALANCE_TYPE%TYPE;
        V_ACC_MIN_AMOUNT                P_ACC_SWEEP_SCHEDULE.MIN_AMOUNT%TYPE;
        V_ADDITIONAL_REF                P_SWEEP.ADDITIONAL_REFERENCE%TYPE;
        V_SWEEP_FROM_BAL_TYPE_CR        P_SWEEP.SWEEP_FROM_BALANCE_TYPE_CR%TYPE;
        V_SWEEP_FROM_BAL_TYPE_DR        P_SWEEP.SWEEP_FROM_BALANCE_TYPE_DR%TYPE;

        V_MOVEMENT_ID                   P_MOVEMENT.MOVEMENT_ID%TYPE;
        V_USE_TYPE                      VARCHAR2(20);
        V_LITERAL_VAL                   VARCHAR2(4000);
        V_MERGE_QUERY                   VARCHAR2(4000);
        V_MRG_SQL_INS_PART              VARCHAR2(4000);
        V_MRG_SQL_UPD_PART              VARCHAR2(4000);
        V_IS_PART_OF_PK                 VARCHAR2(1);
        V_EVENT_FACILITY_ID             P_SCENARIO_EVENT_FACILITY.ID%TYPE;
        V_KEY_VAL                       VARCHAR2(500);
        V_TABLE_NAME                    VARCHAR2(30);
        V_EXECUTE_WHEN                  P_SCENARIO_EVENT_MAPPING.EXECUTE_WHEN%TYPE;
        V_REPEAT_ON_RERAISE             P_SCENARIO_EVENT_MAPPING.REPEAT_ON_RERAISE%TYPE;
        V_RESOLVED_DATETIME             P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE;
        V_STATUS_AFTER_EVENT_LABEL      VARCHAR2(50);
        V_NBR_FAILED_EVENTS             NUMBER := 0;

        V_FORMAT_ID                     P_MESSAGE_FORMATS.FORMAT_ID%TYPE;
        V_MAP_KEY                       P_SCENARIO_EVENT_MAPPING.MAP_KEY%TYPE;
        V_PARAMETERS_XML                VARCHAR2(12);

        V_EXCEPTION_PROC        EXCEPTION;
        PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);

CURSOR CUR_EVENTS IS
SELECT PEF.ID, PEF.PROGRAM_ID, MAP.EXECUTE_WHEN, MAP.REPEAT_ON_RERAISE, PSI.RESOLVED_DATETIME, MAP.MAP_KEY,
       DBMS_LOB.SUBSTR(MAP.PARAMETERS_XML, 12) PARAMETERS_XML
FROM P_SCENARIO_EVENT_FACILITY PEF
         INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (PSI.SCENARIO_ID = MAP.SCENARIO_ID)
WHERE PSI.ID = P_SCENARIO_INSTANCE_ID
  AND PSI.STATUS = 'A'
  AND PSI.EVENTS_LAUNCH_STATUS = 'W'
ORDER BY MAP.ORDINAL;

CURSOR CUR_MAP (P_EVENT_FACILITY_ID IN P_SCENARIO_EVENT_FACILITY.ID%TYPE, P_MAP_KEY P_SCENARIO_EVENT_MAPPING.MAP_KEY%TYPE, P_TABLE_NAME VARCHAR2) IS
          WITH L_USER_COLS
            AS (SELECT COL.TABLE_NAME, COL.COLUMN_NAME, COL.CHAR_LENGTH
                  FROM USER_TAB_COLUMNS COL
                 WHERE TABLE_NAME = UPPER(P_TABLE_NAME))
SELECT EVENT.MAP_COL, REPLACE(EVENT.MAP_FROM, '"'), UPPER(EVENT.DATA_TYPE) DATA_TYPE, EVENT.ISMANDATORY,
       EVENT.USE_TYPE, EVENT.LITERAL_VAL,
       -- Ignore would indicate that no value is being passed for the parameter
       -- This is not the same as specifying an empty Literal value (null) Literal
       CASE WHEN REPLACE(LITERAL_VAL, '"') = CONST_INSTANCE_ID_ATTR
                THEN TO_CHAR(P_SCENARIO_INSTANCE_ID)
            ELSE
                CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT' AND COL.CHAR_LENGTH IS NOT NULL
                         THEN
                         SUBSTR(
                                 CASE WHEN EVENT.USE_TYPE = CONST_LITERAL_USE_TYPE
                                          THEN EVENT.LITERAL_VAL
                                      ELSE CASE WHEN UPPER(EVENT.USE_TYPE) = 'NULL' THEN ''
                                                ELSE PKG_ALERT.FN_GET_SCN_ATTR_VAL(lower(replace(literal_val,'"')), PSI.ID)
                                          END
                                     END,
                                 1, COL.CHAR_LENGTH)
                     ELSE
                         CASE WHEN EVENT.USE_TYPE = CONST_LITERAL_USE_TYPE THEN EVENT.LITERAL_VAL
                              ELSE CASE WHEN UPPER(EVENT.USE_TYPE) = 'NULL' THEN ''
                                        ELSE PKG_ALERT.FN_GET_SCN_ATTR_VAL(lower(replace(literal_val,'"')), PSI.ID)
                                  END
                             END
                    END
           END COLUMN_VAL
FROM P_SCENARIO_EVENT_MAPPING MAP
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
         CROSS JOIN
     XMLTABLE('/mappedParameters/parameter'
                  PASSING XMLTYPE (MAP.PARAMETERS_XML)
                            COLUMNS MAP_COL VARCHAR2(128) path 'name',
              MAP_FROM VARCHAR2(128) path 'map_from',
              DATA_TYPE VARCHAR2(30) path 'data_type',
              ISMANDATORY VARCHAR2(1) path 'isMandatory',
              USE_TYPE VARCHAR2(50) path 'useType',
              LITERAL_VAL VARCHAR2(128) path 'value'
     ) event
         LEFT JOIN L_USER_COLS COL ON (EVENT.MAP_COL = COL.COLUMN_NAME)
WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
  AND MAP.MAP_KEY = P_MAP_KEY
  AND PSI.ID = P_SCENARIO_INSTANCE_ID
  AND NVL(USE_TYPE, ' ')  <> CONST_IGNORE_USE_TYPE
  AND (LITERAL_VAL IS NOT NULL OR UPPER(USE_TYPE) = 'NULL');
BEGIN
        -- Loop on event facilities
OPEN CUR_EVENTS;
LOOP
FETCH CUR_EVENTS INTO V_EVENT_FACILITY_ID, V_PROGRAM_ID, V_EXECUTE_WHEN, V_REPEAT_ON_RERAISE, V_RESOLVED_DATETIME, V_MAP_KEY, V_PARAMETERS_XML;
          EXIT WHEN CUR_EVENTS%NOTFOUND;

BEGIN

            IF (        (V_NBR_FAILED_EVENTS > 0 AND V_EXECUTE_WHEN = 'E')
                     OR (V_NBR_FAILED_EVENTS = 0 AND V_EXECUTE_WHEN = 'S')
                     OR (NVL(V_EXECUTE_WHEN, 'A') = 'A'))
                AND (   V_RESOLVED_DATETIME IS NULL
                    OR (V_REPEAT_ON_RERAISE = 'Y' AND V_RESOLVED_DATETIME IS NOT NULL)
                   )
            THEN
              V_SQL_PART1 := NULL;
              V_SQL_PART2 := NULL;
              V_MRG_SQL_INS_PART := NULL;
              V_MRG_SQL_UPD_PART := NULL;
              V_MERGE_QUERY := NULL;
              V_KEY_VAL := NULL;

              -- Get list columns to be populated automatically and their values
              V_COL_AUTO_COL := SUBSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), 1, INSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), '|')-1);
              V_COL_AUTO_VAL := SUBSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), INSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), '|')+1);

              -- For SEND_MESSAGE event there are no mapping XML in P_SCENARIO_EVENT_MAPPING
              -- Only FORMAT_ID value is supplied
              IF V_PROGRAM_ID = 24 THEN
                V_FORMAT_ID := V_PARAMETERS_XML;
ELSE
                -- Set table_name for each program to be used in truncating data before insert/update data
                V_TABLE_NAME := CASE V_PROGRAM_ID
                                  WHEN 5 THEN 'P_MOVEMENT'
                                  WHEN 6 THEN 'P_MOVEMENT'
                                  WHEN 115 THEN 'P_ACCOUNT_ATTRIBUTE'
                                  WHEN 19 THEN 'P_BALANCE'
                                  WHEN 12 THEN 'P_SWEEP'
                                  ELSE NULL
END;

OPEN CUR_MAP (V_EVENT_FACILITY_ID, V_MAP_KEY, V_TABLE_NAME);
LOOP
FETCH CUR_MAP INTO V_MAP_COL, V_MAP_FROM, V_DATA_TYPE, V_IS_MANDATARY, V_USE_TYPE, V_LITERAL_VAL, V_COLUMN_VALUE;
                  EXIT WHEN CUR_MAP%NOTFOUND;

                  V_SQL_PART1 := V_SQL_PART1 || V_MAP_COL || ',';

                  -- V_COLUMN_VALUE is extracted as string, we need to convert it when its data type is date or numeric
                  IF V_DATA_TYPE IN ('DATE', 'DATETIME') THEN
                      V_SQL_PART2 := V_SQL_PART2 || 'TO_DATE (''' || V_COLUMN_VALUE || ''',''' || CONST_DATE_FORMAT_EVENT || '''),';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'TO_DATE (''' || V_COLUMN_VALUE || ''',''' || CONST_DATE_FORMAT_EVENT || ''') AS ' || V_MAP_COL || ',';
                  ELSIF V_DATA_TYPE IN ('INTEGER','NUMBER') THEN
                      V_SQL_PART2 := V_SQL_PART2 || 'TO_NUMBER(''' || REPLACE(REPLACE(V_COLUMN_VALUE, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || '''),';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'TO_NUMBER(''' || REPLACE(REPLACE(V_COLUMN_VALUE, '.', CONST_DECIMAL), ',', CONST_DECIMAL)|| ''') AS ' || V_MAP_COL || ',';
ELSE
                      V_SQL_PART2 := V_SQL_PART2 || 'q''[' || V_COLUMN_VALUE || ']'',';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'q''[' || V_COLUMN_VALUE || ']'' AS ' || V_MAP_COL || ',';
END IF;

                  -- Variables used in Merge query in Insert/Update Balance
                  V_MRG_SQL_INS_PART := V_MRG_SQL_INS_PART || 'B.'|| V_MAP_COL || ',';

                  IF V_PROGRAM_ID = 19 THEN
                    -- Do not update primary key columns to avoid exception:
                    -- ORA-38104: columns referenced in the on clause cannot be updated
SELECT DECODE(COUNT(*), 0, 'N', 'Y')
INTO V_IS_PART_OF_PK
FROM user_constraints cons
         INNER JOIN user_cons_columns cols
                    ON (cons.constraint_name = cols.constraint_name)
WHERE     cols.table_name = 'P_BALANCE'
  AND cons.constraint_type = 'P'
  AND cons.status = 'ENABLED'
  AND cols.column_name = UPPER(V_MAP_COL);

IF V_IS_PART_OF_PK = 'N' THEN
                      V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.' || V_MAP_COL || ' = B.'|| V_MAP_COL || ',';
ELSE
                      V_KEY_VAL := V_KEY_VAL || V_COLUMN_VALUE||'/';
END IF;

                  ELSIF V_PROGRAM_ID = 115 THEN

                    IF V_MAP_COL NOT IN ('HOST_ID','ENTITY_ID','ACCOUNT_ID','ATTRIBUTE_ID','EFFECTIVE_DATE') THEN
                      V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.' || V_MAP_COL || ' = B.'|| V_MAP_COL || ',';
ELSE
                      V_KEY_VAL := V_KEY_VAL || V_COLUMN_VALUE||'/';
END IF;
END IF;

                  -- V_ACCOUNT_LEVEL_FLAG='PGT',V_AUTHORIZE_FLAG_SUB_IN=NULL,V_AUTHORIZE_FLAG_SUB_EX=NULL,
                  -- V_AUTHORIZE_FLAG_MAIN_IN=NULL,V_AUTHORIZE_FLAG_MAIN_EX=NULL,
                  -- V_AUTO_SWEEP_SWITCH_MAIN='S',V_AUTO_SWEEP_SWITCH_SUB='S', V_SWEEP_GROUP_ID=' ',
                  -- V_USER_ID=P_USER_ID, ALIGN_ACC_ID,V_ENTITY_ID_M(account_id_dr),V_ENTITY_ID_S(accoutn_id_cr),
                  -- V_AUTO_MANUAL='M',VALUE_DATE,
                  IF V_MAP_COL = 'HOST_ID' THEN
                      V_HOST_ID := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ENTITY_ID_DR' THEN
                      V_ENTITY_ID_M := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACCOUNT_ID_DR' THEN
                      V_ACCOUNT_ID_MAIN := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ENTITY_ID_CR' THEN
                      V_ENTITY_ID_S := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACCOUNT_ID_CR' THEN
                      V_ACCOUNT_ID_SUB := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'AMOUNT' THEN
                      V_AMOUNT := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'VALUE_DATE' THEN
                      V_VALUE_DATE := TO_DATE (V_COLUMN_VALUE, CONST_DATE_FORMAT_EVENT);

                  ELSIF V_MAP_COL = 'ALIGN_ACCOUNT_ID' THEN
                      V_ALIGN_ACC_ID := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SETTL_METHOD_CR' THEN
                      V_SETTLE_METHOD_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SETTL_METHOD_DR' THEN
                      V_SETTLE_METHOD_DR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_BOOKCODE_CR' THEN
                      V_SWEEP_BOOKCODE_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_BOOKCODE_DR' THEN
                      V_SWEEP_BOOKCODE_DR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACC_TARGET_BALANCE' THEN
                      V_ACC_TARGET_BALANCE := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ACC_TARGET_BALANCE_TYPE' THEN
                      V_ACC_TARGET_BALANCE_TYPE := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ACC_MIN_AMOUNT' THEN
                      V_ACC_MIN_AMOUNT := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ADDITIONAL_REF' THEN
                      V_ADDITIONAL_REF := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_FROM_BAL_TYPE_CR' THEN
                      V_SWEEP_FROM_BAL_TYPE_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_FROM_BAL_TYPE_DR' THEN
                      V_SWEEP_FROM_BAL_TYPE_DR := V_COLUMN_VALUE;

                  -- Columns for Update movement
                  ELSIF V_MAP_COL = 'MOVEMENT_ID' THEN
                      V_MOVEMENT_ID := TO_NUMBER(TRIM(V_COLUMN_VALUE));

END IF;


END LOOP;


                IF CUR_MAP%ISOPEN THEN
                    CLOSE CUR_MAP;
END IF;

END IF;

              -- Since HOST_ID only ever takes a single value in an installation of Predict,
              -- it need not be supplied to the facility
              IF V_HOST_ID IS NULL THEN
                V_HOST_ID := GLOBAL_VAR.FN_GET_HOST;
END IF;

              -- Add Update_user to V_COL_AUTO_COL
              V_COL_AUTO_COL := V_COL_AUTO_COL || 'UPDATE_USER,';
              V_COL_AUTO_VAL := V_COL_AUTO_VAL || '''' || P_USER_ID || ''',';
              V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.UPDATE_USER = B.UPDATE_USER,A.UPDATE_DATE = GLOBAL_VAR.SYS_DATE,';
              V_MERGE_QUERY := V_MERGE_QUERY || 'q''[' || P_USER_ID || ']'' AS UPDATE_USER,';

              -- Determine the table from the program
              IF V_PROGRAM_ID = 5 THEN
                  -- Insert a movement
                  -- Columns that should be populated automatically are not given from scenario instance
                  -- For movement creation: MOVEMENT_ID=P_MOVEMENT_SEQUENCE.NEXTVAL, INPUT_DATE=GLOBAL_VAR.SYS_DATE
                  V_SQL := q'[BEGIN INSERT INTO P_MOVEMENT (]' ||
                    V_COL_AUTO_COL ||
                  RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[VALUES (]' ||
                    V_COL_AUTO_VAL ||
                  RTRIM(V_SQL_PART2, ',') || '); PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Movement added: '' || P_MOVEMENT_SEQUENCE.CURRVAL); END;';

              -- Update movement
              ELSIF V_PROGRAM_ID = 6 THEN
                  V_SQL := q'[BEGIN UPDATE P_MOVEMENT SET (]' ||
                    V_COL_AUTO_COL ||
                  RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[= (SELECT ]' ||
                    V_COL_AUTO_VAL ||
                  RTRIM(V_SQL_PART2, ',') || ' FROM DUAL) WHERE MOVEMENT_ID = ' || V_MOVEMENT_ID
                  || '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Movement updated: '' || ' || V_MOVEMENT_ID || '); END;';

              -- Make a sweep
              ELSIF V_PROGRAM_ID = 12 THEN

BEGIN
SELECT sweeping_process.get_format_authorize_flag(host_id,entity_id, new_internal_cr_format),
       sweeping_process.get_format_authorize_flag(host_id,entity_id, new_external_cr_format)
INTO V_AUTHORIZE_FLAG_SUB_IN, V_AUTHORIZE_FLAG_SUB_EX
FROM p_account
WHERE entity_id = V_ENTITY_ID_S
  AND account_id = V_ACCOUNT_ID_SUB;
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                      V_AUTHORIZE_FLAG_SUB_IN := NULL;
                      V_AUTHORIZE_FLAG_SUB_EX := NULL;
END;

BEGIN
SELECT sweeping_process.get_format_authorize_flag(host_id,entity_id, new_internal_cr_format),
       sweeping_process.get_format_authorize_flag(host_id,entity_id, new_external_cr_format)
INTO V_AUTHORIZE_FLAG_MAIN_IN, V_AUTHORIZE_FLAG_MAIN_EX
FROM p_account
WHERE entity_id = V_ENTITY_ID_M
  AND account_id = V_ACCOUNT_ID_MAIN;
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                      V_AUTHORIZE_FLAG_MAIN_IN := NULL;
                      V_AUTHORIZE_FLAG_MAIN_EX := NULL;
END;

                  -- (V_ENTITY_ID_S, V_ACCOUNT_ID_SUB) = (Enity_id_cr, account_id_cr)
                  -- (V_ENTITY_ID_M, V_ACCOUNT_ID_MAIN) = (Enity_id_dr, account_id_dr)
                  V_ACCOUNT_LEVEL_FLAG := 'PGT';
                  V_AUTO_SWEEP_SWITCH_MAIN := 'S';
                  V_AUTO_SWEEP_SWITCH_SUB := 'S';
                  V_SWEEP_GROUP_ID := ' ';
                  V_AUTO_MANUAL := 'M';

                  V_SQL :=
                  q'[
                  DECLARE
                      V_OUT_SUCCESS_STATUS    NUMBER;
                      V_OUT_SWEEP_ID          P_SWEEP.SWEEP_ID%TYPE;
                      V_EXCEPTION_PROC        EXCEPTION;
                      PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
                  BEGIN
                  PKG_SWEEP_PROCESS.SWEEP_MOVEMENT_GENERATION (']' || V_HOST_ID || q'[',]' ||
                                                             q'[']' || V_ACCOUNT_ID_SUB || q'[',]' ||
                                                           q'[']' || V_ACCOUNT_ID_MAIN || q'[',]' ||
                                                           q'[']' || V_AMOUNT || q'[',]' ||
                                                           q'[']' || V_ACCOUNT_LEVEL_FLAG || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_SUB_IN || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_SUB_EX || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_MAIN_IN || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_MAIN_EX || q'[',]' ||
                                                           q'[']' || V_AUTO_SWEEP_SWITCH_MAIN || q'[',]' ||
                                                           q'[']' || V_AUTO_SWEEP_SWITCH_SUB || q'[',]' ||
                                                           q'[']' || V_SWEEP_GROUP_ID || q'[',]' ||
                                                           q'[']' || P_USER_ID || q'[',]' ||
                                                           q'[']' || NVL(V_ALIGN_ACC_ID, V_ACCOUNT_ID_MAIN) || q'[',]' ||
                                                           q'[']' || V_ENTITY_ID_S || q'[',]' ||
                                                           q'[']' || V_ENTITY_ID_M || q'[',]' ||
                                                           q'[']' || V_AUTO_MANUAL || q'[',]' ||
                                                           q'[TO_DATE(']' || TO_CHAR(V_VALUE_DATE, CONST_DATE_FORMAT_EVENT) || q'[',']' || CONST_DATE_FORMAT_EVENT || q'['),]' ||
                                                           q'[V_OUT_SUCCESS_STATUS,]' ||
                                                           q'[V_OUT_SWEEP_ID,]' ||
                                                           q'[']' || V_SETTLE_METHOD_CR || q'[',]' ||
                                                           q'[']' || V_SETTLE_METHOD_DR || q'[',]' ||
                                                           q'[']' || V_SWEEP_BOOKCODE_CR || q'[',]' ||
                                                           q'[']' || V_SWEEP_BOOKCODE_DR || q'[',]' ||
                                                           q'[']' || V_ACC_TARGET_BALANCE || q'[',]' ||
                                                           q'[']' || V_ACC_TARGET_BALANCE_TYPE || q'[',]' ||
                                                           q'[']' || V_ACC_MIN_AMOUNT || q'[',]' ||
                                                           q'[']' || V_ADDITIONAL_REF || q'[',]' ||
                                                           q'[']' || V_SWEEP_FROM_BAL_TYPE_CR || q'[',]' ||
                                                           q'[']' || V_SWEEP_FROM_BAL_TYPE_DR || q'[']' ||
                                                             q'[);]' || CHR(10) ||
                                                             q'[IF V_OUT_SUCCESS_STATUS = 0 THEN
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Sweep_id created:' || V_OUT_SWEEP_ID);]' || CHR(10) ||
                                                             q'[ELSE]' || CHR(10) ||
                                                             q'[  PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in creating sweep (See error log)');]' ||
                                                             q'[  RAISE V_EXCEPTION_PROC;]' ||
                                                         q'[ END IF;]' || CHR(10) ||
                                                         q'[ EXCEPTION WHEN OTHERS THEN RAISE; END;]';



              -- Insert/Update Balance
              ELSIF V_PROGRAM_ID = 19 THEN
                    V_SQL := q'[BEGIN MERGE INTO P_BALANCE A USING (SELECT ]' ||
                  RTRIM(V_MERGE_QUERY, ',') || ' FROM DUAL) B ' ||
                  ' ON (A.HOST_ID = B.HOST_ID and A.ENTITY_ID = B.ENTITY_ID and A.BALANCE_DATE = B.BALANCE_DATE and A.BAL_TYPE_ID = B.BAL_TYPE_ID)
                  WHEN NOT MATCHED THEN
                  INSERT ('||
                  V_COL_AUTO_COL || RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[VALUES (]' ||
                  V_COL_AUTO_VAL || RTRIM(V_MRG_SQL_INS_PART, ',')  || ')' ||
                  ' WHEN MATCHED THEN UPDATE SET ' ||
                    RTRIM(V_MRG_SQL_UPD_PART, ',') ||
                    '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Balance Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

              -- Add an account attribute
              ELSIF V_PROGRAM_ID = 115 THEN
                    V_SQL := q'[BEGIN MERGE INTO P_ACCOUNT_ATTRIBUTE A USING (SELECT ]' ||
                    RTRIM(V_MERGE_QUERY, ',') || ' FROM DUAL) B ' ||
                    ' ON (    A.HOST_ID = B.HOST_ID AND A.ENTITY_ID = B.ENTITY_ID AND A.ACCOUNT_ID = B.ACCOUNT_ID AND A.ATTRIBUTE_ID = B.ATTRIBUTE_ID AND A.EFFECTIVE_DATE = B.EFFECTIVE_DATE)
                    WHEN NOT MATCHED THEN
                    INSERT ('||
                    V_COL_AUTO_COL || RTRIM(V_SQL_PART1, ',')  ||
                    ')' ||
                    q'[VALUES (]' ||
                    V_COL_AUTO_VAL || RTRIM(V_MRG_SQL_INS_PART, ',')  || ')' ||
                    ' WHEN MATCHED THEN UPDATE SET ' ||
                    RTRIM(V_MRG_SQL_UPD_PART, ',') ||
                    '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Account attribute Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

              -- Send Message
              ELSIF V_PROGRAM_ID = 24 THEN
                IF V_FORMAT_ID IS NOT NULL THEN
                    V_SQL :=
                    q'[
                    DECLARE
                        V_OUT_SUCCESS_STATUS    NUMBER;
                        V_OUT_SWEEP_ID          P_SWEEP.SWEEP_ID%TYPE;
                        V_EXCEPTION_PROC        EXCEPTION;
                        PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
                    BEGIN
                         pkg_message_format.message_generation (']' || NULL || q'[',]' ||   -- HostID
                                                             q'[']' || NULL || q'[',]' || -- EntityID
                                                             q'[']' || V_FORMAT_ID || q'[',]' || -- FormatID
                                                             q'[']' || NULL || q'[',]' || -- AccountID
                                                             q'[']' || NULL || q'[',]' || -- Amount
                                                             q'[']' || NULL || q'[',]' || -- MessageLetter
                                                             q'[V_OUT_SUCCESS_STATUS,]' ||
                                                             q'[']' || NULL || q'[',]' ||  -- IntermediaryReq
                                                             q'[']' || P_SCENARIO_INSTANCE_ID || q'[']' ||
                                                             q'[);
                                                             IF V_OUT_SUCCESS_STATUS = 0 THEN
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Message sent:]' || V_FORMAT_ID || q'[');
                                                             ELSE
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in sending message (See error log)');]' ||
                                                         q'[    RAISE V_EXCEPTION_PROC;]' ||
                                                         q'[ END IF;
                                                         EXCEPTION WHEN OTHERS THEN RAISE; END;]';

END IF;

END IF;

BEGIN
                  -- Run event
                  IF V_SQL IS NOT NULL THEN
                      V_RETURN_RESULT := 0;
BEGIN
EXECUTE IMMEDIATE V_SQL;
COMMIT;
EXCEPTION
                        WHEN V_EXCEPTION_PROC THEN
                            V_RETURN_RESULT := -1;
                            V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
WHEN OTHERS THEN
                            -- If an exception is raised when running an event then log it
                            SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error when running event ]' || V_EVENT_FACILITY_ID ||': '||SQLCODE||' - '||SQLERRM, P_USER_ID);
                            sp_error_log (V_HOST_ID,
                                          P_USER_ID,
                                          'DBSERVER',
                                          'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || V_SQL,
                                          SQLCODE,
                                          SQLERRM
                                         );

                          V_RETURN_RESULT := -1;
                          V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
END;
END IF;

EXCEPTION
                  WHEN OTHERS THEN
                      -- If an exception is raised when running an event then log it
                      SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error when running event ]' || V_EVENT_FACILITY_ID ||': '||SQLCODE||' - '||SQLERRM, P_USER_ID);
                      sp_error_log (V_HOST_ID,
                                    P_USER_ID,
                                    'DBSERVER',
                                      'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                                    SQLCODE,
                                    SQLERRM
                                   );
                  -- Return an indication that event is completed with failure
                  V_RETURN_RESULT := -1;
                  V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
END;


END IF;

EXCEPTION
            WHEN OTHERS THEN
              sp_error_log (V_HOST_ID,
                            P_USER_ID,
                            'DBSERVER',
                              'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || ' Event ' || V_EVENT_FACILITY_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                            SQLCODE,
                            SQLERRM
                           );
END;
END LOOP;

        -- No event ran
        IF V_RETURN_RESULT <> 1 AND V_NBR_FAILED_EVENTS = 0 THEN
          -- Get STATUS_AFTER_EVENT_TRIGGER from related scenario
SELECT STATUS_AFTER_EVENT_TRIGGER, DECODE (STATUS_AFTER_EVENT_TRIGGER,'P','Pending','A','Active','O','Overdue','R','Resolved')
INTO V_STATUS_AFTER_EVENT_TRIGGER, V_STATUS_AFTER_EVENT_LABEL
FROM P_SCENARIO PS
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (PS.SCENARIO_ID = PSI.SCENARIO_ID)
WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

PKG_ALERT.SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, V_STATUS_AFTER_EVENT_TRIGGER, q'[Status Passed to ]' || V_STATUS_AFTER_EVENT_LABEL || ' (Status after launching events)', P_USER_ID);
END IF;

        IF CUR_EVENTS%ISOPEN THEN
          CLOSE CUR_EVENTS;
END IF;

        -- Do not update event_launch_status only if at least one event has run
        IF V_RETURN_RESULT <> 1 THEN
          IF V_NBR_FAILED_EVENTS > 0 THEN
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;
V_RETURN_RESULT := -1;
ELSE
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = P_SCENARIO_INSTANCE_ID;
END IF;
END IF;

RETURN V_RETURN_RESULT;

EXCEPTION
      WHEN OTHERS THEN
          -- If an exception is raised when running an event then log it
          SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error: ]'||SQLCODE||':'||SQLERRM, P_USER_ID);
          sp_error_log (V_HOST_ID,
                        P_USER_ID,
                        'DBSERVER',
                          'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                        SQLCODE,
                        SQLERRM
                       );
          V_RETURN_RESULT := -1;
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;


IF CUR_EVENTS%ISOPEN THEN
          CLOSE CUR_EVENTS;
END IF;

RETURN V_RETURN_RESULT;
END;

   FUNCTION FN_GET_SCHEDULED_QUERY (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_SCHEDULED_ID    P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE)
   RETURN CLOB
   IS
      V_QUERY_TEXT_SCHEDULE   P_SCENARIO.QUERY_TEXT%TYPE;
BEGIN
      V_QUERY_TEXT_SCHEDULE := PKG_ALERT.FN_GET_QUERY_TEXT(P_SCENARIO_ID);
FOR PARM IN (
        SELECT param_name, param_value
          FROM P_SCENARIO_SCHEDULE SSCH
               CROSS JOIN
               XMLTABLE (
                  '/PARAMETERS/PARAMETER'
                  PASSING XMLTYPE (SSCH.parameter_xml)
                  COLUMNS PARAM_NAME VARCHAR2 (128) PATH 'NAME',
                          PARAM_VALUE VARCHAR2 (128) PATH 'VALUE') params
         WHERE SSCH.SCENARIO_SCHEDULE_ID = P_SCHEDULED_ID
           AND SSCH.parameter_xml IS NOT NULL)
       LOOP
          V_QUERY_TEXT_SCHEDULE := REGEXP_REPLACE(V_QUERY_TEXT_SCHEDULE, 'P\{'||PARM.PARAM_NAME||'\}', 'q''{' ||PARM.PARAM_VALUE || '}''', 1, 0, 'i');
END LOOP;


RETURN V_QUERY_TEXT_SCHEDULE;
END;

    PROCEDURE SP_PROCESS_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                   P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                   P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                   P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL)
IS
        V_TAB_INSTANCE_ID               TAB_INT;
CURSOR CUR_INSTANCES  IS
SELECT INSTANCE_UNIQUE_EXPRESSION, UNIQUE_IDENTIFIER, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID, AMOUNT, SIGN, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OVER_THRESHOLD, VALUE_DATE, OTHER_ID,
       JSON_ATTRIBUTES, IS_UNIQUE_ROW
FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_QUERY_TEXT, P_USER_ID));

CURSOR CUR_OLD_INSTANCES IS
SELECT PSI.ID
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
  AND PSI.ID NOT IN (SELECT * FROM TABLE(V_TAB_INSTANCE_ID));

V_UNIQUE_IDENTIFIER P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
        V_HOST_ID           P_SCENARIO_INSTANCE.HOST_ID%TYPE;
        V_ENTITY_ID         P_SCENARIO_INSTANCE.ENTITY_ID%TYPE;
        V_CCY_CODE          P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE;
        V_ACCOUNT_ID        P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE;
        V_AMOUNT            P_SCENARIO_INSTANCE.AMOUNT%TYPE;
        V_SIGN              P_SCENARIO_INSTANCE.SIGN%TYPE;
        V_MOVEMENT_ID       P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE;
        V_MATCH_ID          P_SCENARIO_INSTANCE.MATCH_ID%TYPE;
        V_SWEEP_ID          P_SCENARIO_INSTANCE.SWEEP_ID%TYPE;
        V_PAYMENT_ID        P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE;
        V_OTHER_ID          P_SCENARIO_INSTANCE.OTHER_ID%TYPE;
        V_VALUE_DATE        P_SCENARIO_INSTANCE.VALUE_DATE%TYPE;
        V_INSTANCE_ID       NUMBER;
        V_OVER_THRESHOLD    P_SCENARIO_INSTANCE.OVER_THRESHOLD%TYPE;
        V_INSTANCE_UNIQUE_EXPRESSION    P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
        --V_DATE_FORMAT       VARCHAR2(30);
        V_ATTRIBUTES_JSON               P_SCENARIO_INSTANCE.ATTRIBUTES_JSON%TYPE;
        V_COUNT_INSTANCES               NUMBER;
        V_COUNT_EVENT_MAP               NUMBER;
        V_START_TIMESTAMP               TIMESTAMP;
        V_END_TIMESTAMP                 TIMESTAMP;
        V_PROCESS_DURATION   INTERVAL DAY (2) TO SECOND (4);
        V_RESULT_LAUNCH_EVENT           NUMBER;
        V_START_TEST_TIME               DATE;
        I                               NUMBER := 1;
        V_IS_UNIQUE_ROW                 VARCHAR2(1);
        V_ERROR_LOCATION                VARCHAR2(10);
        V_NBR_FAILED_INST_EVENT         NUMBER := 0;

BEGIN
      V_ERROR_LOCATION := '10';
      V_START_TIMESTAMP := SYSTIMESTAMP;
      V_START_TEST_TIME := GLOBAL_VAR.SYS_DATE;
OPEN CUR_INSTANCES;
LOOP
FETCH CUR_INSTANCES INTO V_INSTANCE_UNIQUE_EXPRESSION, V_UNIQUE_IDENTIFIER, V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID, V_AMOUNT, V_SIGN, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OVER_THRESHOLD, V_VALUE_DATE, V_OTHER_ID, V_ATTRIBUTES_JSON, V_IS_UNIQUE_ROW;
        EXIT WHEN CUR_INSTANCES%NOTFOUND;
        V_ERROR_LOCATION := '20';

        -- Check if this unique identifier is unique
        IF V_IS_UNIQUE_ROW = 'Y' THEN
          V_ERROR_LOCATION := '30';
          -- Does instance exist already
SELECT COUNT(*)
INTO V_COUNT_INSTANCES
FROM P_SCENARIO_INSTANCE
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND UNIQUE_IDENTIFIER = V_UNIQUE_IDENTIFIER;

V_ERROR_LOCATION := '40';
          IF V_COUNT_INSTANCES = 0 THEN
            -- Are there events to launch
            V_ERROR_LOCATION := '50';
SELECT COUNT(*)
INTO V_COUNT_EVENT_MAP
FROM P_SCENARIO_EVENT_FACILITY PEF
         INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;

V_ERROR_LOCATION := '60';
            -- Make an instance scenario
            IF V_COUNT_EVENT_MAP = 0 THEN
              V_ERROR_LOCATION := '70';
              V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;
INSERT INTO P_SCENARIO_INSTANCE (ID,SCENARIO_ID, UNIQUE_IDENTIFIER, STATUS, RAISED_DATETIME, LAST_RAISED_DATETIME,
                                 RESOLVED_DATETIME, RESOLVED_BY_USER, EVENTS_LAUNCH_STATUS, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID,
                                 AMOUNT, SIGN, OVER_THRESHOLD, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OTHER_ID, VALUE_DATE, ATTRIBUTES_JSON)
VALUES (V_INSTANCE_ID, P_SCENARIO_ID, V_UNIQUE_IDENTIFIER, 'A', GLOBAL_VAR.SYS_DATE, GLOBAL_VAR.SYS_DATE,
        NULL, NULL, 'N', V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID,
        V_AMOUNT, V_SIGN, V_OVER_THRESHOLD, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OTHER_ID, V_VALUE_DATE, V_ATTRIBUTES_JSON);
ELSE
              V_ERROR_LOCATION := '80';
              V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;
INSERT INTO P_SCENARIO_INSTANCE (ID,SCENARIO_ID, UNIQUE_IDENTIFIER, STATUS, RAISED_DATETIME, LAST_RAISED_DATETIME,
                                 RESOLVED_DATETIME, RESOLVED_BY_USER, EVENTS_LAUNCH_STATUS, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID,
                                 AMOUNT, SIGN, OVER_THRESHOLD, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OTHER_ID, VALUE_DATE, ATTRIBUTES_JSON)
VALUES (V_INSTANCE_ID, P_SCENARIO_ID, V_UNIQUE_IDENTIFIER, 'A', GLOBAL_VAR.SYS_DATE, GLOBAL_VAR.SYS_DATE,
        NULL, NULL, 'W', V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID,
        V_AMOUNT, V_SIGN, V_OVER_THRESHOLD, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OTHER_ID, V_VALUE_DATE, V_ATTRIBUTES_JSON);
END IF;
            V_ERROR_LOCATION := '70';
              -- Duplicate this instance into P_SCENARIO_ACTIVE_INSTANCE to be used for performance
INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
SELECT V_INSTANCE_ID
FROM DUAL
WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = V_INSTANCE_ID);
V_ERROR_LOCATION := '80';
                  -- Insert logs
              SP_LOG_SCENARIO_INSTANCE (V_INSTANCE_ID, q'[Scenario instance inserted (]' || P_SCENARIO_ID || ')', P_USER_ID);


ELSE
            V_ERROR_LOCATION := '90';
            -- Checking existing status value
SELECT PSI.ID
INTO V_INSTANCE_ID
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO PS ON PSI.SCENARIO_ID = PS.SCENARIO_ID
WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
  AND PSI.UNIQUE_IDENTIFIER = V_UNIQUE_IDENTIFIER;

END IF;

        -- Instance is not unique
ELSE
          V_ERROR_LOCATION := '90';
          sp_error_log('',
                   'SYSTEM',
                   'DBSERVER',
                   'PKG_ALERT.SP_PROCESS_SCENARIO:  ''' || V_UNIQUE_IDENTIFIER || ''' Is not unique for scenario: ' || P_SCENARIO_ID,
                   SQLCODE,
                   SQLERRM);

END IF;

        V_ERROR_LOCATION := '100';
        -- Launch events for the given instance scenario
        IF V_COUNT_EVENT_MAP > 0 THEN
            V_RESULT_LAUNCH_EVENT := PKG_ALERT.FN_LAUNCH_SCEN_EVENT(V_INSTANCE_ID, P_USER_ID);
END IF;

        IF V_RESULT_LAUNCH_EVENT <> 0 THEN
          V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
END IF;

        -- Add the current instance id into this collection to avoid that it is treated twice
        V_TAB_INSTANCE_ID (I) := V_INSTANCE_ID;
        I := I + 1;
END LOOP;

      V_ERROR_LOCATION := '110';
      -- Calculate scenario counts
      SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID);

      V_ERROR_LOCATION := '120';
      IF CUR_INSTANCES%ISOPEN THEN
        CLOSE CUR_INSTANCES;
END IF;

      V_ERROR_LOCATION := '130';
      -- Launch other existing instances
OPEN CUR_OLD_INSTANCES;
LOOP
FETCH CUR_OLD_INSTANCES INTO V_INSTANCE_ID;
        EXIT WHEN CUR_OLD_INSTANCES%NOTFOUND;

        V_ERROR_LOCATION := '140';
        V_RESULT_LAUNCH_EVENT := PKG_ALERT.FN_LAUNCH_SCEN_EVENT(V_INSTANCE_ID, P_USER_ID);

        IF V_RESULT_LAUNCH_EVENT <> 0 THEN
          V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
END IF;

END LOOP;
      -- Delete array to allow launching events for these instances in the next job execution
      V_TAB_INSTANCE_ID.DELETE;

      V_ERROR_LOCATION := '150';
      IF CUR_OLD_INSTANCES%ISOPEN THEN
        CLOSE CUR_OLD_INSTANCES;
END IF;

      V_ERROR_LOCATION := '160';
      -- Update all existing instances for the given scenario_id
      -- even if these instances are no longer retreived by scenario query
      SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);

      V_ERROR_LOCATION := '170';
      -- calculate run duration for this scenario
      V_END_TIMESTAMP := SYSTIMESTAMP;
      V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;
      -- Update scenario record with last RUN time = now AND DURATION OF RUN
      V_ERROR_LOCATION := '180';
UPDATE P_SCENARIO_SYSTEM
SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
WHERE SCENARIO_ID = P_SCENARIO_ID;

IF SQL%ROWCOUNT = 0 THEN
        V_ERROR_LOCATION := '190';
INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
SELECT P_SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
FROM DUAL
WHERE NOT EXISTS (SELECT NULL
                  FROM P_SCENARIO_SYSTEM
                  WHERE SCENARIO_ID =  P_SCENARIO_ID);
END IF;

      V_ERROR_LOCATION := '200';

      IF P_SCENARIO_SCHEDULE_ID IS NOT NULL THEN
UPDATE P_SCENARIO_SCHEDULE
SET LAST_RUN_STARTED = V_START_TEST_TIME,
    LAST_RUN_ENDED = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_STATUS = CASE WHEN V_NBR_FAILED_INST_EVENT > 0 THEN 'F' ELSE 'S' END
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND SCENARIO_SCHEDULE_ID = P_SCENARIO_SCHEDULE_ID;
END IF;

COMMIT;

EXCEPTION
     WHEN OTHERS THEN
        IF CUR_INSTANCES%ISOPEN THEN
          CLOSE CUR_INSTANCES;
END IF;
        sp_error_log('',
                     'SYSTEM',
                     'DBSERVER',
                     'PKG_ALERT.SP_PROCESS_SCENARIO: Error when running scenario ' || P_SCENARIO_ID || ' at location ' || V_ERROR_LOCATION || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                     SQLCODE,
                     SQLERRM);
END;

  PROCEDURE SP_PROCESS_ALL_SCENARIOS (P_USER_ID     P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
IS
        CURSOR CUR_SCENARIO IS
SELECT S.SCENARIO_ID, S.GENERATION_BASIS
FROM P_SCENARIO S
         LEFT JOIN P_SCENARIO_SYSTEM SS ON (S.SCENARIO_ID = SS.SCENARIO_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND S.RECORD_SCENARIO_INSTANCES = 'Y'
  --AND NVL(S.INSTANCE_EXPIRY_MINS, -1) <> 0
  -- Run only cyclic and Scheduled scenarios not API scenarios
  AND S.GENERATION_BASIS IN ('C','S')
  AND PKG_ALERT.FN_TEST_START_END_TIME (GLOBAL_VAR.SYS_DATE, S.START_TIME, S.END_TIME) = 'Y'
  AND (   SS.LAST_RUN_DATE IS NULL -- scenario never checked before
    OR SS.LAST_RUN_DATE > GLOBAL_VAR.SYS_DATE -- Last checked in future (test date anomaly)
    OR -- or scenario was last checked 'run every' time ago
          (SS.LAST_RUN_DATE <
           (  GLOBAL_VAR.SYS_DATE
               - (  TO_NUMBER (
                            TO_CHAR (
                                    TO_DATE (NVL (S.RUN_EVERY, '00:00:00'), 'HH24:MI:SS'),
                                    'SSSSS'))
                   / 86400))));

V_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE;
        V_QUERY_TEXT_SCHEDULE   P_SCENARIO.QUERY_TEXT%TYPE;
        V_GENERATION_BASIS      P_SCENARIO.GENERATION_BASIS%TYPE;
        V_ERROR_LOCATION        VARCHAR2(3);

BEGIN
        V_ERROR_LOCATION := '10';
        -- insert scenario instances
OPEN CUR_SCENARIO;
LOOP
FETCH CUR_SCENARIO INTO V_SCENARIO_ID, V_GENERATION_BASIS;
            EXIT WHEN CUR_SCENARIO%NOTFOUND;

            V_ERROR_LOCATION := '20';
            -- Scheduled scenario
            IF V_GENERATION_BASIS = 'S' THEN
BEGIN
                V_ERROR_LOCATION := '30';
                -- Get the records to be used from p_scenario_schedule
FOR SCHED IN (
                  SELECT SCH.SCENARIO_SCHEDULE_ID
                    FROM P_SCENARIO_SCHEDULE SCH
                         INNER JOIN P_SCENARIO S
                                 ON (SCH.SCENARIO_ID = S.SCENARIO_ID)
                         LEFT JOIN P_SCENARIO_SYSTEM SYS
                                 ON (SYS.SCENARIO_ID = S.SCENARIO_ID)
                  WHERE S.SCENARIO_ID = V_SCENARIO_ID
                    AND S.ACTIVE_FLAG = 'Y'
                    -- check criteria for running:
                    -- current time must be later than check_time
                    AND SCH.CHECK_TIME <= TO_CHAR (GLOBAL_VAR.SYS_DATE, 'HH24:MI')
                    -- last run must ...
                    AND (-- have been prior to check_time
                         TO_CHAR (NVL(SCH.LAST_RUN_STARTED,SYS.LAST_RUN_DATE), 'HH24:MI') < SCH.CHECK_TIME
                         OR
                         -- appear to have been run in the future - a test date situation
                         NVL(SCH.LAST_RUN_STARTED,SYS.LAST_RUN_DATE) > GLOBAL_VAR.SYS_DATE
                         OR
                         --First execution
                         SYS.LAST_RUN_DATE IS NULL
                         )
                )
                LOOP
                   V_ERROR_LOCATION := '40';
                   V_QUERY_TEXT_SCHEDULE := FN_GET_SCHEDULED_QUERY (V_SCENARIO_ID, SCHED.SCENARIO_SCHEDULE_ID);
                   V_ERROR_LOCATION := '50';
                 IF V_QUERY_TEXT_SCHEDULE IS NOT NULL THEN
                      V_ERROR_LOCATION := '60';
                      PKG_ALERT.SP_PROCESS_SCENARIO(V_SCENARIO_ID, CONST_SYS_USER, V_QUERY_TEXT_SCHEDULE, SCHED.SCENARIO_SCHEDULE_ID);
END IF;
END LOOP;
EXCEPTION
                WHEN OTHERS THEN
                  sp_error_log('',
                               'SYSTEM',
                               'DBSERVER',
                               'PKG_ALERT.SP_PROCESS_ALL_SCENARIOS -> Error for ' || V_SCENARIO_ID || ' at location ' || V_ERROR_LOCATION,
                               SQLCODE,
                               SQLERRM
                       );
END;

            -- Cyclic scenario
ELSE

                SP_PROCESS_SCENARIO (V_SCENARIO_ID, P_USER_ID);
COMMIT;

END IF;
END LOOP;
COMMIT;


IF CUR_SCENARIO%ISOPEN THEN
            CLOSE CUR_SCENARIO;
END IF;

EXCEPTION
        WHEN OTHERS THEN
            IF CUR_SCENARIO%ISOPEN THEN
                CLOSE CUR_SCENARIO;
END IF;

END;

   FUNCTION FN_GET_XML_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2
   IS
      V_SQL               VARCHAR2(200);
      V_XML_INSTANCE_ROW  VARCHAR2(4000);
      V_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE;
BEGIN
BEGIN
EXECUTE IMMEDIATE P_QUERY_TEXT INTO V_INSTANCE_ID;

V_SQL := q'[SELECT HOST_ID,ENTITY_ID,CURRENCY_CODE,ACCOUNT_ID,VALUE_DATE,MOVEMENT_ID,MATCH_ID,SWEEP_ID,PAYMENT_ID,OTHER_ID FROM P_SCENARIO_INSTANCE WHERE ID = ]' || V_INSTANCE_ID;
        V_XML_INSTANCE_ROW := PKG_ALERT.FN_GET_QUERYRESULT_AS_XML(V_SQL, CONST_DATE_FORMAT, 'rowset', 'instance');

SELECT EXTRACT (XMLTYPE(V_XML_INSTANCE_ROW),'/rowset/instance').getclobval()
INTO V_XML_INSTANCE_ROW
FROM DUAL;

EXCEPTION
        WHEN TOO_MANY_ROWS THEN
          V_XML_INSTANCE_ROW := NULL;
WHEN NO_DATA_FOUND THEN
          V_XML_INSTANCE_ROW := NULL;
END;

RETURN V_XML_INSTANCE_ROW;
END;

   FUNCTION FN_GET_JSON_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2
   IS
      V_SQL               VARCHAR2(4000);
      V_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE;
BEGIN
EXECUTE IMMEDIATE P_QUERY_TEXT INTO V_INSTANCE_ID;

V_SQL := q'[SELECT JSON_ARRAYAGG (JSON_OBJECT('HOST_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "HOST_ID"),
                               'ENTITY_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "ENTITY_ID"),
                               'CURRENCY_CODE' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "CURRENCY_CODE"),
                               'ACCOUNT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "ACCOUNT_ID"),
                               'VALUE_DATE' VALUE JSON_OBJECT('TYPE' VALUE 'DATE', 'content' VALUE "VALUE_DATE"),
                               'MOVEMENT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "MOVEMENT_ID"),
                               'MATCH_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "MATCH_ID"),
                               'SWEEP_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "SWEEP_ID"),
                               'PAYMENT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "PAYMENT_ID"),
                               'OTHER_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "OTHER_ID")
                               ))
                   FROM P_SCENARIO_INSTANCE WHERE ID = ]' || V_INSTANCE_ID;



RETURN V_SQL;
END;

   FUNCTION fn_get_queryresult_as_xml(
                                pv_query_text IN VARCHAR2,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
        RETURN CLOB
    AS
        vn_metadataCursorId   NUMBER;
        vn_metadataExecute    NUMBER;
        vn_col_count          INTEGER;
        vt_metadataTab        DBMS_SQL.desc_tab;
        vn_colnum             NUMBER;
        vr_metadata_rec       DBMS_SQL.desc_rec;
        vc_xml_query          VARCHAR2(32000);
        vv_query_text_io      VARCHAR2(32000);
        vv_xml_elements       VARCHAR2(32000);
        vx_result             XMLTYPE;
        case_cdata            VARCHAR2(1000);
        vv_date_format        VARCHAR2(100);
        vv_row_set_tag        VARCHAR2(100);
        vv_row_tag            VARCHAR2(100);
        v_exist_count_col     VARCHAR2(1) := 'N';
        vv_xml_instance_row   VARCHAR2(4000);
BEGIN
        vv_query_text_io := pv_query_text;
        -- Remove double cotes to avoid getting error related to case sensitive in column names
        --vv_query_text_io := REPLACE(vv_query_text_io, '"');
        -- Fill in default values
        IF pv_date_format IS NULL THEN
            vv_date_format := 'YYYY-MM-DD';
ELSE
            vv_date_format := pv_date_format;
END IF;

        IF pv_row_set_tag IS NULL THEN
            vv_row_set_tag := 'rowset';
ELSE
            vv_row_set_tag := pv_row_set_tag;
END IF;

        IF pv_row_tag IS NULL THEN
            vv_row_tag := 'row';
ELSE
            vv_row_tag := pv_row_tag;
END IF;

        -- Oracle metadata, get the list of columns from the select query
        vn_metadataCursorId := DBMS_SQL.open_cursor;
        DBMS_SQL.parse (vn_metadataCursorId, vv_query_text_io, DBMS_SQL.native);
        vn_metadataExecute := DBMS_SQL.EXECUTE (vn_metadataCursorId);
        DBMS_SQL.describe_columns (vn_metadataCursorId, vn_col_count, vt_metadataTab);
        IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
          DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
END IF;

        -- loop inside columns metadata and construct the SQL query for XMLELEMENT's
        vn_colnum := vt_metadataTab.FIRST;
        IF (vn_colnum IS NOT NULL)
        THEN
          vc_xml_query := 'SELECT XMLELEMENT("'||vv_row_set_tag||'",
                               XMLAGG(
                                 XMLELEMENT("'||vv_row_tag||'",<xml_elements>)
                               )
                    )
            FROM (
                SELECT rownum row_number, t.* FROM ('||vv_query_text_io||') t )';

         LOOP
vr_metadata_rec := vt_metadataTab (vn_colnum);
            case_cdata := CASE
                              WHEN vr_metadata_rec.col_type = 12 THEN 'TO_CHAR("'||vr_metadata_rec.col_name||'",'''||vv_date_format||''')'
                              ELSE '"'||vr_metadata_rec.col_name||'"'
END;

            IF v_exist_count_col = 'N' AND vr_metadata_rec.col_name = 'COUNT' THEN
              v_exist_count_col := 'Y';
END IF;

            -- XMLELEMENT sql frangment
            vv_xml_elements := vv_xml_elements                      ||
                                 CHR(10)                            ||
                                 '                                 '||
                                 'XMLELEMENT ("'||lower(vr_metadata_rec.col_name)||'", XMLATTRIBUTES (''' || CASE WHEN vr_metadata_rec.col_type=12 THEN 'DATE' ELSE 'TEXT' END || ''' AS "TYPE"), XMLCDATA ('||case_cdata||')),';
            -- Increment the tab counter
            vn_colnum := vt_metadataTab.NEXT (vn_colnum);
            EXIT WHEN (vn_colnum IS NULL);
END LOOP;
         vv_xml_elements := RTRIM(vv_xml_elements, ',');
END IF;
        vc_xml_query := REPLACE(vc_xml_query, '<xml_elements>',vv_xml_elements);

EXECUTE IMMEDIATE vc_xml_query INTO vx_result;

IF v_exist_count_col = 'Y' AND pv_additional_qry IS NOT NULL THEN
           vv_xml_instance_row := fn_get_xml_instance_row (pv_additional_qry);
           IF vv_xml_instance_row IS NOT NULL THEN
SELECT APPENDCHILDXML(vx_result,
                      'rowset/row',
                      XMLType(vv_xml_instance_row)
       )
INTO vx_result
FROM DUAL;
END IF;
END IF;

        -- Prettify the XML before sending it to client side
RETURN vx_result/*.EXTRACT ('/*')*/.getClobVal(); --Note: Uncommenting .EXTRACT ('/*') will prettify the XML output clob, but a performance bug is encountred on oracle 10g
    EXCEPTION
          WHEN OTHERS
          THEN
            IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
              DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
            END IF;
             sp_error_log ('',
                           'SYSTEM',
                           pkg_s_utility.gl_host_ip,
                           'PKG_ALERT.fn_get_queryresult_as_xml:' || DBMS_UTILITY.format_error_backtrace,
                            SQLCODE,
                            SQLERRM
                            );
             RAISE;
    END;


   FUNCTION fn_get_queryresult_as_json(
                                pv_query_text IN CLOB,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
        RETURN CLOB
    AS
        vn_metadataCursorId   NUMBER;
        vn_metadataExecute    NUMBER;
        vn_col_count          INTEGER;
        vt_metadataTab        DBMS_SQL.desc_tab;
        vn_colnum             NUMBER;
        vr_metadata_rec       DBMS_SQL.desc_rec;
        vc_xml_query          VARCHAR2(32000);
        vv_query_text_io      VARCHAR2(32000);
        vv_xml_elements       VARCHAR2(32000);
        vx_result             CLOB;
        case_cdata            VARCHAR2(32000);
        vv_date_format        VARCHAR2(100);
        vv_row_set_tag        VARCHAR2(100);
        vv_row_tag            VARCHAR2(100);
        v_exist_count_col     VARCHAR2(1) := 'N';
    BEGIN
        vv_query_text_io := pv_query_text;
        -- Remove double cotes to avoid getting error related to case sensitive in column names
        --vv_query_text_io := REPLACE(vv_query_text_io, '"');
        -- Fill in default values
        IF pv_date_format IS NULL THEN
            vv_date_format := 'YYYY-MM-DD';
        ELSE
            vv_date_format := pv_date_format;
        END IF;

        IF pv_row_set_tag IS NULL THEN
            vv_row_set_tag := 'rowset';
        ELSE
            vv_row_set_tag := pv_row_set_tag;
        END IF;

        IF pv_row_tag IS NULL THEN
            vv_row_tag := 'row';
        ELSE
            vv_row_tag := pv_row_tag;
        END IF;

        -- Oracle metadata, get the list of columns from the select query
        vn_metadataCursorId := DBMS_SQL.open_cursor;
        DBMS_SQL.parse (vn_metadataCursorId, vv_query_text_io, DBMS_SQL.native);
        vn_metadataExecute := DBMS_SQL.EXECUTE (vn_metadataCursorId);
        DBMS_SQL.describe_columns (vn_metadataCursorId, vn_col_count, vt_metadataTab);
        IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
          DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
        END IF;

        -- loop inside columns metadata and construct the SQL query for XMLELEMENT's
        vn_colnum := vt_metadataTab.FIRST;
        IF (vn_colnum IS NOT NULL)
        THEN
          vc_xml_query := 'SELECT JSON_OBJECT('''||vv_row_set_tag||''' VALUE
                                 JSON_OBJECT('''||vv_row_tag||''' VALUE JSON_ARRAYAGG (JSON_OBJECT(<xml_elements> RETURNING CLOB) RETURNING CLOB)
                               RETURNING CLOB)
                    RETURNING CLOB)
            FROM (
                SELECT rownum row_number, t.* FROM ('||vv_query_text_io||') t )';

         LOOP
            vr_metadata_rec := vt_metadataTab (vn_colnum);
            case_cdata := CASE
                              WHEN vr_metadata_rec.col_type = 12 THEN 'TO_CHAR("'||vr_metadata_rec.col_name||'",'''||vv_date_format||''')'
                              ELSE CASE WHEN vr_metadata_rec.col_type = 12 THEN 'TO_NUMBER(REPLACE(REPLACE("'||vr_metadata_rec.col_name||'", '','', ''.''), '' '', ''''), ''9999999999999999999999999990D9999'', ''NLS_NUMERIC_CHARACTERS = ''. '')'
                                   ELSE
                                    '"'||vr_metadata_rec.col_name||'"'
                                   END
                          END;

            IF v_exist_count_col = 'N' AND vr_metadata_rec.col_name = 'COUNT' THEN
              v_exist_count_col := 'Y';
            END IF;

            -- XMLELEMENT sql frangment
            vv_xml_elements := vv_xml_elements                      ||
                                 CHR(10)                            ||
                                 '                                 '||
                                 ''''||lower(vr_metadata_rec.col_name)||''' VALUE JSON_OBJECT(''TYPE'' VALUE ''' ||
                                 CASE WHEN vr_metadata_rec.col_type=12 THEN 'DATE'
                                 ELSE CASE WHEN vr_metadata_rec.col_type=2 THEN 'NUMBER'
                                      ELSE 'TEXT'
                                      END
                                 END || ''', ''content'' VALUE '||case_cdata||'),';
            -- Increment the tab counter
            vn_colnum := vt_metadataTab.NEXT (vn_colnum);
            EXIT WHEN (vn_colnum IS NULL);
         END LOOP;

         IF v_exist_count_col = 'Y' AND pv_additional_qry IS NOT NULL THEN
           vv_xml_elements := vv_xml_elements || ' ''INSTANCE'' VALUE (' || fn_get_json_instance_row (pv_additional_qry) || ')';
         END IF;

         vv_xml_elements := RTRIM(vv_xml_elements, ',');
        END IF;
        vc_xml_query := REPLACE(vc_xml_query, '<xml_elements>',vv_xml_elements);

        EXECUTE IMMEDIATE vc_xml_query INTO vx_result;

        RETURN vx_result;
    EXCEPTION
          WHEN OTHERS
          THEN
            IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
              DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
            END IF;
             sp_error_log ('',
                           'SYSTEM',
                           pkg_s_utility.gl_host_ip,
                           'PKG_ALERT.fn_get_queryresult_as_json:' || DBMS_UTILITY.format_error_backtrace,
                            SQLCODE,
                            SQLERRM
                            );
             RAISE;
    END;

    FUNCTION FN_IS_ROW_HIGHLIGHTED (P_FACILITY_ID   P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                    P_HOST_ID       P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                    P_ENTITY_ID     P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                    P_CURRENCY_CODE P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                    P_VALUE_DATE    DATE,
                                    P_USER_ID       S_USERS.USER_ID%TYPE,
                                    P_CCY_THRESHOLD VARCHAR2 DEFAULT 'N',
                                    P_ACCOUNT_ID    P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                    P_MOVEMENT_ID   P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                    P_MATCH_ID      P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                    P_ILM_GROUP_ID  P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                    P_ROLE_ID       S_ROLE.ROLE_ID%TYPE DEFAULT NULL)
    RETURN VARCHAR2
    IS
        V_GUI_HIGHLIGHT     VARCHAR2(1);
        V_ROLE_ID           S_USERS.ROLE_ID%TYPE := P_ROLE_ID;
        V_REQUIRES_SCENARIO_INSTANCE  P_SCENARIO_GUI_ALERT_FACILITY.REQUIRES_SCENARIO_INSTANCE%TYPE;
    BEGIN
        -- Get user's role if p_role_id is not supplied
        IF V_ROLE_ID IS NULL THEN
          BEGIN
          SELECT ROLE_ID
            INTO V_ROLE_ID
            FROM S_USERS
           WHERE USER_ID = P_USER_ID;
          EXCEPTION
              WHEN OTHERS THEN
                  V_ROLE_ID := NULL;
          END;
        END IF;

        -- Is this facility requires scenario instance
        BEGIN
            SELECT REQUIRES_SCENARIO_INSTANCE
              INTO V_REQUIRES_SCENARIO_INSTANCE
              FROM P_SCENARIO_GUI_ALERT_FACILITY
             WHERE ID = P_FACILITY_ID;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                V_REQUIRES_SCENARIO_INSTANCE := 'N';
        END;
        -- If exists data in p_scenario_counts for a critical scenario (P_SCENARIO.CRITICAL_GUI_HIGHLIGHT)
        -- Then return C
        -- If exists data for a non-critical scenario then return Y
        -- If no data in p_scenario_counts then return N
        IF V_REQUIRES_SCENARIO_INSTANCE = 'N' THEN
        SELECT DECODE(MAX(DECODE(CRITICAL_GUI_HIGHLIGHT,NULL,0, 'Y', 2, 1)),2, 'C', 1, 'Y', 'N')
          INTO V_GUI_HIGHLIGHT
        FROM (
                SELECT PS.SCENARIO_ID,
                       NVL(PS.CRITICAL_GUI_HIGHLIGHT,'N') CRITICAL_GUI_HIGHLIGHT,
                       PS.RECORD_SCENARIO_INSTANCES
                  FROM P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO PS ON (PS.SCENARIO_ID = GAM.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_COUNTS PSC ON (PS.SCENARIO_ID = PSC.SCENARIO_ID)
                 WHERE GAF.ID = P_FACILITY_ID
                   AND PS.ACTIVE_FLAG = 'Y'
                   AND (SCENARIO_COUNT_OVER_T > 0 OR P_CCY_THRESHOLD = 'N')
                       -- Attributes required by the facility
                   AND PSC.HOST_ID = P_HOST_ID
                   AND PSC.ENTITY_ID = P_ENTITY_ID
                   AND PSC.CURRENCY_CODE = SUBSTR(P_CURRENCY_CODE, 1, 3)
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, V_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) != '2'
                   AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, V_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
                 GROUP BY PS.SCENARIO_ID, PS.CRITICAL_GUI_HIGHLIGHT, PS.RECORD_SCENARIO_INSTANCES
                 );
        ELSE
              SELECT DECODE(MAX(DECODE(CRITICAL_GUI_HIGHLIGHT,NULL,0, 'Y', 2, 1)),2, 'C', 1, 'Y', 'N')
                INTO V_GUI_HIGHLIGHT
                FROM (
                SELECT P_SCENARIO.SCENARIO_ID,
                       NVL(P_SCENARIO.CRITICAL_GUI_HIGHLIGHT,'N') CRITICAL_GUI_HIGHLIGHT,
                       P_SCENARIO.RECORD_SCENARIO_INSTANCES
                  FROM P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)
                 WHERE GAF.ID = P_FACILITY_ID
                   AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                   AND (PSI.OVER_THRESHOLD = 'Y' OR P_CCY_THRESHOLD = 'N')
                       -- Attributes required by the facility
                   AND NVL(P_HOST_ID, 'All') IN ('All', PSI.HOST_ID)
                   AND NVL(P_ENTITY_ID, 'All') IN ('All', PSI.ENTITY_ID)
                   AND NVL(SUBSTR(P_CURRENCY_CODE, 1, 3), 'All') IN ('All', PSI.CURRENCY_CODE)
                   AND (PSI.VALUE_DATE = P_VALUE_DATE OR P_VALUE_DATE IS NULL)
                   AND (PSI.ACCOUNT_ID = P_ACCOUNT_ID OR P_ACCOUNT_ID IS NULL)
                   AND (PSI.MOVEMENT_ID = P_MOVEMENT_ID OR P_MOVEMENT_ID IS NULL)
                   AND (PSI.MATCH_ID = P_MATCH_ID OR P_MATCH_ID IS NULL)
                   AND PSI.STATUS IN ('A','P','O')
                   --AND ((PSI.HOST_ID,PSI.ENTITY_ID,PSI.ACCOUNT_ID) IN (SELECT HOST_ID,ENTITY_ID,ACCOUNT_ID FROM TABLE(PKG_ILM.FN_GET_ILM_ACC_IN_GROUP(P_ILM_GROUP_ID))) OR NVL(P_ILM_GROUP_ID, 'All') = 'All')
                   AND (PSI.OTHER_ID = P_ILM_GROUP_ID OR (PSI.OTHER_ID IS NULL OR P_ILM_GROUP_ID IS NULL))
                   -- check user's access for entity/currency and scenario
                   AND (   (P_USER_ID IS NULL AND P_ROLE_ID IS NULL)
                        OR
                           (    PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, V_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) != '2'
                            AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSI.SCENARIO_ID, PSI.HOST_ID, V_ROLE_ID, PSI.ENTITY_ID), 1,1) = 'Y')
                        )
                 GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES
             );
        END IF;

        RETURN V_GUI_HIGHLIGHT;
    END;

    FUNCTION FN_GET_HIGHLIGHTING_DETAILS (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                          P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                          P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                          P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                          P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                          P_USER_ID         S_USERS.USER_ID%TYPE,
                                          P_CALL_OPTION     VARCHAR2,
                                          P_SELECTED_TAB    P_CATEGORY.DISPLAY_TAB%TYPE DEFAULT 1,
                                          P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                          P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                          PV_INS_STATUS     VARCHAR2 DEFAULT NULL,
                                          P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                          P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                          P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                          P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                          P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                          P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                          P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL)
    RETURN CLOB --TAB_SCEN_HIGHLIGHT PIPELINED
    IS
        V_SCENARIO_ID                 P_SCENARIO.SCENARIO_ID%TYPE;
        V_RECORD_SCENARIO_INSTANCES   P_SCENARIO.RECORD_SCENARIO_INSTANCES%TYPE;
        V_CUSTOM_TREE_LEVEL1          P_SCENARIO.CUSTOM_TREE_LEVEL1%TYPE;
        V_CUSTOM_TREE_LEVEL2          P_SCENARIO.CUSTOM_TREE_LEVEL2%TYPE;
        V_ROLE_ID                     S_USERS.ROLE_ID%TYPE;
        V_LIST_GROUP_COL              P_SCENARIO_GUI_ALERT_FACILITY.GROUP_COLUMNS%TYPE;
        V_SQL                         CLOB;
        V_SQL_PARMS                   CLOB;
        CUR_SCEN_HIGHLIGHT            SYS_REFCURSOR;
        V_SCEN_DETAILS_XML            CLOB;
        V_REQUIRES_SCENARIO_INSTANCE  P_SCENARIO_GUI_ALERT_FACILITY.REQUIRES_SCENARIO_INSTANCE%TYPE;
        V_HOST_COL                    P_SCENARIO.SEC_HOST_COL%TYPE;
        V_ENTITY_COL                  P_SCENARIO.SEC_ENTITY_COL%TYPE;
        V_CURRENCY_COL                P_SCENARIO.SEC_CURRENCY_COL%TYPE;
        V_ACCOUNT_ID_COL              P_SCENARIO.ACCOUNT_ID_COL%TYPE;
        V_VALUE_DATE_COL              P_SCENARIO.VALUE_DATE_COL%TYPE;
        V_MOVEMENT_ID_COL             P_SCENARIO.MOVEMENT_ID_COL%TYPE;
        V_MATCH_ID_COL                P_SCENARIO.MATCH_ID_COL%TYPE;
        V_SWEEP_ID_COL                P_SCENARIO.SWEEP_ID_COL%TYPE;
        V_PAYMENT_ID_COL              P_SCENARIO.PAYMENT_ID_COL%TYPE;
        V_OTHER_ID_COL                P_SCENARIO.OTHER_ID_COL%TYPE;
        V_GROUP_COLUMNS               VARCHAR2(500);
        V_SQL_INSTANCE                VARCHAR2(10000);
    BEGIN
        -- Get user's role
        BEGIN
        SELECT ROLE_ID--, DECODE (DATE_FORMAT , 1, 'DD/MM/RRRR', 2, 'MM/DD/RRRR', NULL, 'DD/MM/RRRR')
          INTO V_ROLE_ID--, V_DATE_FORMAT
          FROM S_USERS
         WHERE USER_ID = P_USER_ID;
        EXCEPTION
            WHEN OTHERS THEN
                V_ROLE_ID := NULL;
                --V_DATE_FORMAT := 'DD/MM/RRRR';
        END;

        -- Get group columns to be used for grouping results
        BEGIN
            SELECT GROUP_COLUMNS, REQUIRES_SCENARIO_INSTANCE
              INTO V_LIST_GROUP_COL, V_REQUIRES_SCENARIO_INSTANCE
              FROM P_SCENARIO_GUI_ALERT_FACILITY
             WHERE ID = P_FACILITY_ID;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                V_LIST_GROUP_COL := NULL;
                V_REQUIRES_SCENARIO_INSTANCE := 'N';
        END;

        -- All for Wokflow_monitor and scenario_instance_monitor
        IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) OR V_REQUIRES_SCENARIO_INSTANCE = 'N'
        THEN
          V_REQUIRES_SCENARIO_INSTANCE := 'A';
        END IF;

        -- Get list of scenarios
        V_SQL_PARMS := q'[WITH PARMS AS (SELECT ']' ||P_FACILITY_ID|| q'[' P_FACILITY_ID,']' ||
                      P_HOST_ID || q'[' P_HOST_ID,']' ||
                      P_ENTITY_ID || q'[' P_ENTITY_ID,']' ||
                      P_CURRENCY_CODE || q'[' P_CURRENCY_CODE,]' ||
                      'TO_DATE(''' || TO_CHAR(P_VALUE_DATE, 'YYYY-MM-DD') || q'[', 'YYYY-MM-DD') P_VALUE_DATE,']' ||
                      V_ROLE_ID || q'[' P_ROLE_ID,']' ||
                      P_SELECTED_TAB || q'[' P_SELECTED_TAB, ']' ||
                      P_CCY_THRESHOLD || q'[' P_CCY_THRESHOLD,']' ||
                      P_ACCOUNT_ID || q'[' P_ACCOUNT_ID,]' ||
                      NVL(TO_CHAR(P_MOVEMENT_ID), 'NULL') || q'[ P_MOVEMENT_ID,]' ||
                      NVL(TO_CHAR(P_MATCH_ID), 'NULL') || q'[ P_MATCH_ID,']' ||
                      P_ILM_GROUP_ID || q'[' P_ILM_GROUP_ID,]' ||
                      NVL(TO_CHAR(P_SWEEP_ID), 'NULL') || q'[ P_SWEEP_ID, ]' ||
                      NVL(TO_CHAR(P_PAYMENT_ID), 'NULL') || q'[ P_PAYMENT_ID]' ||
             q'[ FROM DUAL)]';

        IF V_REQUIRES_SCENARIO_INSTANCE = 'Y' THEN
          V_SQL := V_SQL_PARMS || q'[
          SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                 SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
            FROM PARMS CROSS JOIN ]' ||
            CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
            THEN
              q'[ P_SCENARIO ]'
            ELSE
              q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                 INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                 INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
            END ||
            q'[
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
            CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
             q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
            END ||
            q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
            CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
            THEN
              q'[ WHERE ]'
            ELSE
              q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
            END ||
            q'[ (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')]' ||
            CASE WHEN NVL(P_HOST_ID, 'All') <> 'All'       THEN q'[ AND PARMS.P_HOST_ID = PSI.HOST_ID]' END ||
            CASE WHEN NVL(P_ENTITY_ID, 'All') <> 'All'     THEN q'[ AND PARMS.P_ENTITY_ID = PSI.ENTITY_ID]' END ||
            CASE WHEN NVL(P_CURRENCY_CODE, 'All') <> 'All' THEN q'[ AND PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE]' END ||
            CASE WHEN P_VALUE_DATE IS NOT NULL             THEN q'[ AND PARMS.P_VALUE_DATE = PSI.VALUE_DATE]' END ||
            CASE WHEN NVL(P_ACCOUNT_ID, 'All') <> 'All'    THEN q'[ AND PARMS.P_ACCOUNT_ID = PSI.ACCOUNT_ID]' END ||
            CASE WHEN NVL(P_MOVEMENT_ID, -1) <> -1   THEN q'[ AND PARMS.P_MOVEMENT_ID = PSI.MOVEMENT_ID]' END ||
            CASE WHEN NVL(P_MATCH_ID, -1) <> -1      THEN q'[ AND PARMS.P_MATCH_ID = PSI.MATCH_ID]' END ||
            CASE WHEN NVL(P_ILM_GROUP_ID, 'All') <> 'All'  THEN q'[ AND PARMS.P_ILM_GROUP_ID = PSI.OTHER_ID]' END ||
            CASE WHEN NVL(P_SWEEP_ID, -1) <> -1      THEN q'[ AND PARMS.P_SWEEP_ID = PSI.SWEEP_ID]' END ||
            CASE WHEN NVL(P_PAYMENT_ID, -1) <> -1    THEN q'[ AND PARMS.P_PAYMENT_ID = PSI.PAYMENT_ID]' END ||
             CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
               q'[ AND PSI.STATUS IN ('A','P','O') ]'
             END ||
             q'[
             -- check user's access for entity/currency and scenario
             AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
             AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
             AND P_SCENARIO.ACTIVE_FLAG = 'Y'
             AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
            ]' ||
            CASE WHEN PV_INS_STATUS IS NULL THEN
              q'[ AND PSI.STATUS IN ('A','P','O') ]'
            ELSE
            q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
            END ||
            CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
              q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
            END;
        ELSE
          IF V_REQUIRES_SCENARIO_INSTANCE <> 'N' THEN
            V_SQL := V_SQL_PARMS || q'[
              SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                     SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
                FROM PARMS CROSS JOIN ]' ||
                CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                THEN
                  q'[ P_SCENARIO ]'
                ELSE
                  q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                     INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                     INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                END ||
                q'[
                     INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
                CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                 q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                END ||
                q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                THEN
                  q'[ WHERE ]'
                ELSE
                  q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                END ||
                q'[ (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')]' ||
                CASE WHEN NVL(P_HOST_ID, 'All') <> 'All'       THEN q'[ AND PARMS.P_HOST_ID = PSI.HOST_ID]' END ||
                CASE WHEN NVL(P_ENTITY_ID, 'All') <> 'All'     THEN q'[ AND PARMS.P_ENTITY_ID = PSI.ENTITY_ID]' END ||
                CASE WHEN NVL(P_CURRENCY_CODE, 'All') <> 'All' THEN q'[ AND PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE]' END ||
                CASE WHEN P_VALUE_DATE IS NOT NULL             THEN q'[ AND PARMS.P_VALUE_DATE = PSI.VALUE_DATE]' END ||
                CASE WHEN NVL(P_ACCOUNT_ID, 'All') <> 'All'    THEN q'[ AND PARMS.P_ACCOUNT_ID = PSI.ACCOUNT_ID]' END ||
                CASE WHEN NVL(P_MOVEMENT_ID, -1) <> -1   THEN q'[ AND PARMS.P_MOVEMENT_ID = PSI.MOVEMENT_ID]' END ||
                CASE WHEN NVL(P_MATCH_ID, -1) <> -1      THEN q'[ AND PARMS.P_MATCH_ID = PSI.MATCH_ID]' END ||
                CASE WHEN NVL(P_ILM_GROUP_ID, 'All') <> 'All'  THEN q'[ AND PARMS.P_ILM_GROUP_ID = PSI.OTHER_ID]' END ||
                CASE WHEN NVL(P_SWEEP_ID, -1) <> -1      THEN q'[ AND PARMS.P_SWEEP_ID = PSI.SWEEP_ID]' END ||
                CASE WHEN NVL(P_PAYMENT_ID, -1) <> -1    THEN q'[ AND PARMS.P_PAYMENT_ID = PSI.PAYMENT_ID]' END ||
                 CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                   q'[ AND PSI.STATUS IN ('A','P','O') ]'
                 END ||
                 q'[
                 -- check user's access for entity/currency and scenario
                 AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                 AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                 AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                 AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
              ]' ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                  q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                     THEN 'Y'
                     ELSE 'N' END = 'Y']'
               END ||
               CASE WHEN PV_INS_STATUS IS NULL THEN
                  q'[ AND PSI.STATUS IN ('A','P','O') ]'
                ELSE
                q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
              END ||
               CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                 q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
               END ||
                q'[ UNION
                 SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                        SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
                  FROM PARMS
                       CROSS JOIN ]' ||
                       CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                       THEN
                        q'[ P_SCENARIO ]'
                       ELSE
                        q'[P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                       END ||
                      q'[ INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)]' ||
                      q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                      CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                      THEN
                        q'[  WHERE ]'
                      ELSE
                        q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                      END ||
                  q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
                   AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
                   AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
                   AND NVL(P_SCENARIO.RECORD_SCENARIO_INSTANCES, 'N') = 'N'
                   -- Do not display zeroes
                   AND PSC.SCENARIO_COUNT > 0
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
                   AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
                   AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                   AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                   AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
                   CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                      q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END = 'Y']'
                   END || q'[ ORDER BY 1,2,3,4]';
          ELSE
            V_SQL := V_SQL_PARMS || q'[
             SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                    SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
              FROM PARMS
                   CROSS JOIN ]' ||
                   CASE WHEN (P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                         ) THEN
                    q'[ P_SCENARIO ]'
                   ELSE
                    q'[P_SCENARIO_GUI_ALERT_FACILITY GAF
                   INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                   INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                   END ||
                  q'[ INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)
                  LEFT JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID)]' ||
                  CASE WHEN (P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                         ) THEN
                    q'[  WHERE ]'
                  ELSE
                    q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                  END ||
              q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
               AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
               AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
               -- Do not display zeroes
               AND PSC.SCENARIO_COUNT > 0
               -- check user's access for entity/currency and scenario
               AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
               AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
               AND P_SCENARIO.ACTIVE_FLAG = 'Y'
               AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                  q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                     THEN 'Y'
                     ELSE 'N' END = 'Y']'
               END || q'[ ORDER BY 1,2,3,4]';
          END IF;
        END IF;
        OPEN CUR_SCEN_HIGHLIGHT FOR V_SQL;

        LOOP
            FETCH CUR_SCEN_HIGHLIGHT INTO V_SCENARIO_ID, V_RECORD_SCENARIO_INSTANCES, V_CUSTOM_TREE_LEVEL1, V_CUSTOM_TREE_LEVEL2,
                  V_HOST_COL, V_ENTITY_COL, V_CURRENCY_COL, V_ACCOUNT_ID_COL, V_VALUE_DATE_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_SWEEP_ID_COL, V_PAYMENT_ID_COL, V_OTHER_ID_COL;
            EXIT WHEN CUR_SCEN_HIGHLIGHT%NOTFOUND;

            V_GROUP_COLUMNS := NULL;

            IF V_RECORD_SCENARIO_INSTANCES = 'Y' THEN

              IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                 AND INSTR (NVL(REPLACE(V_GROUP_COLUMNS,'"'), ' '), 'ENTITY_ID') = 0
                 AND INSTR (NVL(REPLACE(V_LIST_GROUP_COL,'"'), ' '), 'CURRENCY_CODE') = 0
              THEN
                 V_GROUP_COLUMNS :=  V_GROUP_COLUMNS || ',ENTITY_ID';
              END IF;

              IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                 AND INSTR (NVL(REPLACE(V_GROUP_COLUMNS,'"'), ' '), 'CURRENCY_CODE') = 0
                 AND INSTR (NVL(REPLACE(V_LIST_GROUP_COL,'"'), ' '), 'CURRENCY_CODE') = 0
              THEN
                 V_GROUP_COLUMNS :=  V_GROUP_COLUMNS || ',CURRENCY_CODE';
              END IF;

              IF V_CUSTOM_TREE_LEVEL1 IS NOT NULL
                 AND INSTR(',' ||REPLACE(V_GROUP_COLUMNS,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL1,'"')||',')=0
                 AND INSTR(',' ||REPLACE(V_LIST_GROUP_COL,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL1,'"')||',')=0
              THEN
                V_GROUP_COLUMNS := V_GROUP_COLUMNS || ',' || V_CUSTOM_TREE_LEVEL1;
              END IF;

              IF V_CUSTOM_TREE_LEVEL2 IS NOT NULL
                AND INSTR(',' ||REPLACE(V_GROUP_COLUMNS,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL2,'"')||',')=0
                AND INSTR(',' ||REPLACE(V_LIST_GROUP_COL,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL2,'"')||',')=0
              THEN
                V_GROUP_COLUMNS := V_GROUP_COLUMNS || ',' || V_CUSTOM_TREE_LEVEL2;
              END IF;

              IF V_LIST_GROUP_COL IS NOT NULL
              THEN
                V_GROUP_COLUMNS :=  ',' || V_LIST_GROUP_COL || V_GROUP_COLUMNS;
              END IF;

            END IF;

            IF V_REQUIRES_SCENARIO_INSTANCE IN ('A','Y') AND V_RECORD_SCENARIO_INSTANCES = 'Y' THEN
              V_SQL := V_SQL_PARMS || q'[
                  SELECT P_SCENARIO.SCENARIO_ID,
                         CAT.TITLE TITLE_CAT,
                         CAT.DISPLAY_ORDER DISP_ORDER_CAT,
                         CAT.DESCRIPTION DESC_CAT,
                         P_SCENARIO.DISPLAY_ORDER DISP_ORDER_SCEN,
                         P_SCENARIO.TITLE TITLE_SCEN,
                         PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
                        (CASE WHEN
                             (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,P_SCENARIO.START_TIME, P_SCENARIO.END_TIME)='Y')
                             AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                             AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')
                             THEN ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                             ELSE GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END) AS TIME_TO_CUT_OFF,
                         CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END AS IsScenarioAlertable,
                         P_SCENARIO.DESCRIPTION DESC_SCEN,
                         P_SCENARIO.RECORD_SCENARIO_INSTANCES,
                         P_SCENARIO.CATEGORY_ID ]' || CHR(10) ||
                         V_GROUP_COLUMNS ||
                    q'[, q'[]' || LTRIM(V_GROUP_COLUMNS, ',') ||
                    q'<]' AS GROUP_COLUMNS >' ||
                    q'[, CASE WHEN P_CCY_THRESHOLD = 'Y'
                                   THEN SUM(CASE WHEN NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' THEN 1 ELSE 0 END)
                              ELSE COUNT(*)
                         END COUNT
                    FROM PARMS CROSS JOIN ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ P_SCENARIO ]'
                    ELSE
                      q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                    END ||
                  q'[ INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
                    CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                     q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                    END ||
                  q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ WHERE ]'
                    ELSE
                      q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                    END ||
                     CASE WHEN V_HOST_COL IS NOT NULL AND NVL(P_HOST_ID,'All') <> 'All' THEN q'[ PARMS.P_HOST_ID = PSI.HOST_ID AND ]' END ||
                     CASE WHEN V_ENTITY_COL IS NOT NULL AND NVL(P_ENTITY_ID,'All') <> 'All' THEN q'[ PARMS.P_ENTITY_ID = PSI.ENTITY_ID AND ]' END ||
                     CASE WHEN V_CURRENCY_COL IS NOT NULL AND NVL(P_CURRENCY_CODE,'All') <> 'All' THEN q'[ PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE AND ]' END ||
                     CASE WHEN V_ACCOUNT_ID_COL IS NOT NULL AND NVL(P_ACCOUNT_ID,'All') <> 'All' THEN q'[ NVL(PSI.ACCOUNT_ID,PARMS.P_ACCOUNT_ID) = PARMS.P_ACCOUNT_ID AND ]' END ||
                     CASE WHEN V_VALUE_DATE_COL IS NOT NULL AND P_VALUE_DATE IS NOT NULL THEN q'[ NVL(PSI.VALUE_DATE, PARMS.P_VALUE_DATE) = PARMS.P_VALUE_DATE AND ]' END ||
                     CASE WHEN V_MOVEMENT_ID_COL IS NOT NULL AND NVL(P_MOVEMENT_ID,-1) <> -1 THEN q'[ PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID AND ]' END ||
                     CASE WHEN V_MATCH_ID_COL IS NOT NULL AND NVL(P_MATCH_ID,-1) <> -1 THEN q'[ PSI.MATCH_ID = PARMS.P_MATCH_ID AND ]' END ||
                     CASE WHEN V_OTHER_ID_COL IS NOT NULL AND NVL(P_ILM_GROUP_ID,'All') <> 'All' THEN q'[ PSI.OTHER_ID = PARMS.P_ILM_GROUP_ID AND ]' END ||
                     CASE WHEN V_SWEEP_ID_COL IS NOT NULL AND NVL(P_SWEEP_ID,-1) <> -1 THEN q'[ PSI.SWEEP_ID = PARMS.P_SWEEP_ID AND ]' END ||
                     CASE WHEN V_PAYMENT_ID_COL IS NOT NULL AND NVL(P_PAYMENT_ID,-1) <> -1 THEN q'[ PSI.PAYMENT_ID = PARMS.P_PAYMENT_ID  AND ]' END ||
                     q'[ PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                     AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                     AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
                     ]' ||
                     CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                       q'[ AND PSI.STATUS IN ('A','P','O') ]'
                     END ||
                     q'[
                     AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                     CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                        q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                           THEN 'Y'
                           ELSE 'N' END = 'Y']'
                     END ||
                     CASE WHEN PV_INS_STATUS IS NULL THEN
                        q'[ AND PSI.STATUS IN ('A','P','O') ]'
                      ELSE
                     q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
                   END ||
                     CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                        q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
                     END ||
                   q'[ GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_CCY_THRESHOLD,P_SCENARIO.CATEGORY_ID,
                       CAT.TITLE, CAT.DISPLAY_ORDER, CAT.DESCRIPTION, P_SCENARIO.DISPLAY_ORDER, P_SCENARIO.TITLE,
                       P_SCENARIO.DESCRIPTION, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_SCENARIO.START_TIME,P_SCENARIO.END_TIME,PSI.SCENARIO_ID, PSI.HOST_ID, PSI.ENTITY_ID,PARMS.P_ROLE_ID
                       ]' ||
                   V_GROUP_COLUMNS ||
                   q'[ HAVING CASE WHEN P_CCY_THRESHOLD = 'Y'
                                   THEN SUM(CASE WHEN NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' THEN 1 ELSE 0 END)
                              ELSE COUNT(*)
                         END > 0 ORDER BY 1,2,3,4]';

              V_SQL_INSTANCE := V_SQL_PARMS || q'[
              SELECT PSI.ID
                FROM PARMS CROSS JOIN ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ P_SCENARIO ]'
                    ELSE
                      q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                    END ||
                  q'[ INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID) ]' ||
                    CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                     q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                    END ||
                  q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ WHERE ]'
                    ELSE
                      q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                    END ||
                     CASE WHEN V_HOST_COL IS NOT NULL AND NVL(P_HOST_ID,'All') <> 'All' THEN q'[ PARMS.P_HOST_ID = PSI.HOST_ID AND ]' END ||
                     CASE WHEN V_ENTITY_COL IS NOT NULL AND NVL(P_ENTITY_ID,'All') <> 'All' THEN q'[ PARMS.P_ENTITY_ID = PSI.ENTITY_ID AND ]' END ||
                     CASE WHEN V_CURRENCY_COL IS NOT NULL AND NVL(P_CURRENCY_CODE,'All') <> 'All' THEN q'[ PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE AND ]' END ||
                     CASE WHEN V_ACCOUNT_ID_COL IS NOT NULL AND NVL(P_ACCOUNT_ID,'All') <> 'All' THEN q'[ NVL(PSI.ACCOUNT_ID,PARMS.P_ACCOUNT_ID) = PARMS.P_ACCOUNT_ID AND ]' END ||
                     CASE WHEN V_VALUE_DATE_COL IS NOT NULL AND P_VALUE_DATE IS NOT NULL THEN q'[ NVL(PSI.VALUE_DATE, PARMS.P_VALUE_DATE) = PARMS.P_VALUE_DATE AND ]' END ||
                     CASE WHEN V_MOVEMENT_ID_COL IS NOT NULL AND NVL(P_MOVEMENT_ID,-1) <> -1 THEN q'[ PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID AND ]' END ||
                     CASE WHEN V_MATCH_ID_COL IS NOT NULL AND NVL(P_MATCH_ID,-1) <> -1 THEN q'[ PSI.MATCH_ID = PARMS.P_MATCH_ID AND ]' END ||
                     CASE WHEN V_SWEEP_ID_COL IS NOT NULL AND NVL(P_SWEEP_ID,-1) <> -1 THEN q'[ PSI.SWEEP_ID = PARMS.P_SWEEP_ID AND ]' END ||
                     CASE WHEN V_PAYMENT_ID_COL IS NOT NULL AND NVL(P_PAYMENT_ID,-1) <> -1 THEN q'[ PSI.PAYMENT_ID = PARMS.P_PAYMENT_ID  AND ]' END ||
                     q'[ PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                     AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                     AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
                     ]' ||
                     CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                       q'[ AND PSI.STATUS IN ('A','P','O') ]'
                     END ||
                     q'[
                     AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                     CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                        q'[ AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                           THEN 'Y'
                           ELSE 'N' END = 'Y']'
                     END ||
                     CASE WHEN PV_INS_STATUS IS NULL THEN
                        q'[ AND PSI.STATUS IN ('A','P','O') ]'
                      ELSE
                     q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
                   END ||
                     CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                        q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
                     END;
            ELSE

                 V_SQL := V_SQL_PARMS || q'[
                 SELECT P_SCENARIO.SCENARIO_ID,
                        CAT.TITLE TITLE_CAT,
                        CAT.DISPLAY_ORDER DISP_ORDER_CAT,
                        CAT.DESCRIPTION DESC_CAT,
                        P_SCENARIO.DISPLAY_ORDER DISP_ORDER_SCEN,
                        P_SCENARIO.TITLE TITLE_SCEN,
                        PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
                        (CASE WHEN
                             (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,P_SCENARIO.START_TIME, P_SCENARIO.END_TIME)='Y')
                             AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                             AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')
                             THEN
                                 ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                             ELSE
                                     GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END) AS TIME_TO_CUT_OFF,
                         CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END AS IsScenarioAlertable,
                         P_SCENARIO.DESCRIPTION DESC_SCEN,
                        P_SCENARIO.RECORD_SCENARIO_INSTANCES,
                        P_SCENARIO.CATEGORY_ID
                       ]' ||
                  q'[, NULL AS GROUP_COLUMNS ]' ||
                  q'[, CASE WHEN P_CCY_THRESHOLD = 'Y'
                                 THEN SUM(PSC.SCENARIO_COUNT_OVER_T)
                            ELSE SUM (PSC.SCENARIO_COUNT)
                       END COUNT
                  FROM PARMS
                       CROSS JOIN ]' ||
                  CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                     q'[ P_SCENARIO ]'
                  ELSE
                     q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN  (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                  END ||
                  q'[
                       INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)
                       LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                  CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                    q'[ WHERE ]'
                  ELSE
                    q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                  END ||
                  CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                    q'[ NVL(GAF.REQUIRES_SCENARIO_INSTANCE, 'N') = 'N' AND]'
                  END ||
                  q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
                   AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
                   AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
                   -- Do not display zeroes
                   AND PSC.SCENARIO_COUNT > 0
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
                   AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                   AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
                   q'[ AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                   CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                      q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END = 'Y']'
                   END ||
                 q'[ GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_CCY_THRESHOLD,P_SCENARIO.CATEGORY_ID,
                      CAT.TITLE, CAT.DISPLAY_ORDER, CAT.DESCRIPTION, P_SCENARIO.DISPLAY_ORDER, P_SCENARIO.TITLE,
                      P_SCENARIO.DESCRIPTION,
                      P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_SCENARIO.START_TIME,P_SCENARIO.END_TIME,PSC.SCENARIO_ID, PSC.HOST_ID, PSC.ENTITY_ID,PARMS.P_ROLE_ID
                   HAVING CASE WHEN P_CCY_THRESHOLD = 'Y'
                                 THEN SUM(PSC.SCENARIO_COUNT_OVER_T)
                            ELSE SUM (PSC.SCENARIO_COUNT)
                       END > 0 ORDER BY 1,2,3,4
                   ]';
            END IF;

            V_SCEN_DETAILS_XML := V_SCEN_DETAILS_XML || FN_GET_QUERYRESULT_AS_XML(V_SQL, CONST_DATE_FORMAT, 'rowset', 'row', -1, -1, V_SQL_INSTANCE);

        END LOOP;

        V_SCEN_DETAILS_XML := '<details>' || V_SCEN_DETAILS_XML || '</details>';

        IF CUR_SCEN_HIGHLIGHT%ISOPEN THEN
            CLOSE CUR_SCEN_HIGHLIGHT;
        END IF;

        RETURN V_SCEN_DETAILS_XML;
    EXCEPTION
        WHEN OTHERS THEN
            IF CUR_SCEN_HIGHLIGHT%ISOPEN THEN
                CLOSE CUR_SCEN_HIGHLIGHT;
            END IF;
            RAISE;
    END;

    FUNCTION FN_GET_UNIQUE_IDENTIFIER_API (P_TAB      TAB_VARCHAR2,
                                           P_UNIQUE_EXPRESSION  P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE)
    RETURN VARCHAR2
    IS
      V_SQL                 VARCHAR2(1000);
      L_INDEX               VARCHAR2(30);
      V_UNIQUE_IDENTIFIER   P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
    BEGIN
      L_INDEX := P_TAB.FIRST;
      WHILE (L_INDEX IS NOT NULL)
      LOOP
        V_SQL := V_SQL || 'q''[' || P_TAB(L_INDEX) || ']'' as ' || L_INDEX || ',';
        L_INDEX := P_TAB.NEXT(L_INDEX);
      END LOOP;
      V_SQL := RTRIM(V_SQL, ',');
      V_SQL := 'SELECT ' || P_UNIQUE_EXPRESSION || ' FROM (SELECT '|| V_SQL||' FROM DUAL)';

      EXECUTE IMMEDIATE V_SQL INTO V_UNIQUE_IDENTIFIER;

      RETURN 'q''[' || V_UNIQUE_IDENTIFIER || ']''';

    END;

    FUNCTION FN_GET_TAB_VAL_IGNORE_QUOTE (P_TAB             TAB_VARCHAR2,
                                          P_INDEX           VARCHAR2)
    RETURN VARCHAR2
    IS
      V_VALUE   VARCHAR2(4000);
    BEGIN
      IF P_TAB.EXISTS(P_INDEX) THEN
        V_VALUE := P_TAB(P_INDEX);
      ELSIF P_TAB.EXISTS('"' || P_INDEX || '"') THEN
        V_VALUE := P_TAB('"' || P_INDEX || '"');
      ELSIF P_TAB.EXISTS(REPLACE(P_INDEX, '"')) THEN
        V_VALUE := P_TAB(REPLACE(P_INDEX, '"'));
      ELSE
        V_VALUE := '';
      END IF;

      RETURN V_VALUE;
    END;

    PROCEDURE SP_CREATE_INSTANCE_API (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                      P_TAB             TAB_VARCHAR2,
                                      P_USER_ID         VARCHAR2,
                                      P_LOG_TEXT    OUT VARCHAR2,
                                      P_INSTANCE_ID OUT P_SCENARIO_INSTANCE.ID%TYPE)
    IS
      TYPE T_ID IS TABLE OF P_SCENARIO_INSTANCE.ID%TYPE;
      V_ID                    T_ID;
      V_UNIQUE_IDENTIFIER     P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
      V_SQL_PART1             VARCHAR2(10000) := 'ID,SCENARIO_ID,STATUS,RAISED_DATETIME,LAST_RAISED_DATETIME,UNIQUE_IDENTIFIER,';
      V_SQL_PART2             VARCHAR2(10000) := 'SEQ_P_SCENARIO_INSTANCE.NEXTVAL,'''||P_SCENARIO_ID||''',''A'',GLOBAL_VAR.SYS_DATE,GLOBAL_VAR.SYS_DATE,';
      V_SQL                   VARCHAR2(10000);
      V_IDX                   VARCHAR2(50);
      V_DATA_TYPE             VARCHAR2(30);
      V_SQL_XML_ATTRIBUTES    VARCHAR2(10000);
      V_ATTRIBUTES_JSON       P_SCENARIO_INSTANCE.ATTRIBUTES_JSON%TYPE;
      V_API_REQUIRED_COLS     P_SCENARIO.API_REQUIRED_COLS%TYPE;
      V_MISSED_COL            P_SCENARIO.API_REQUIRED_COLS%TYPE;
      V_UNIQUE_EXPRESSION     P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
      V_EVENTS_LAUNCH_STATUS  P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE;
      V_COUNT                 NUMBER;
      V_AMT_THRESH_COL        P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
      V_HOST_COL              P_SCENARIO.SEC_HOST_COL%TYPE;
      V_ENTITY_COL            P_SCENARIO.SEC_ENTITY_COL%TYPE;
      V_CURRENCY_COL          P_SCENARIO.SEC_CURRENCY_COL%TYPE;
      V_CCY_THRESHOLD_VALUE   S_CURRENCY.THRESHOLD_PRODUCT%TYPE;
      V_OVER_THRESHOLD        P_SCENARIO_INSTANCE.OVER_THRESHOLD%TYPE;
      V_ACCOUNT_ID_COL        P_SCENARIO.ACCOUNT_ID_COL%TYPE;
      V_SIGN_COL              P_SCENARIO.SIGN_COL%TYPE;
      V_MOVEMENT_ID_COL       P_SCENARIO.MOVEMENT_ID_COL%TYPE;
      V_MATCH_ID_COL          P_SCENARIO.MATCH_ID_COL%TYPE;
      V_SWEEP_ID_COL          P_SCENARIO.SWEEP_ID_COL%TYPE;
      V_PAYMENT_ID_COL        P_SCENARIO.PAYMENT_ID_COL%TYPE;
      V_VALUE_DATE_COL        P_SCENARIO.VALUE_DATE_COL%TYPE;
      V_OTHER_ID_COL          P_SCENARIO.OTHER_ID_COL%TYPE;
      V_OTHER_ID_TYPE         P_SCENARIO.OTHER_ID_TYPE%TYPE;
    BEGIN
      -- Get the unique identifier based on instance_unique_expression in P_SCENARIO
      SELECT INSTANCE_UNIQUE_EXPRESSION, AMT_THRESHOLD_COL, SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL,
             ACCOUNT_ID_COL, SIGN_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL,
             VALUE_DATE_COL, OTHER_ID_COL, OTHER_ID_TYPE
        INTO V_UNIQUE_EXPRESSION, V_AMT_THRESH_COL, V_HOST_COL, V_ENTITY_COL, V_CURRENCY_COL,
             V_ACCOUNT_ID_COL, V_SIGN_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_SWEEP_ID_COL, V_PAYMENT_ID_COL,
             V_VALUE_DATE_COL, V_OTHER_ID_COL, V_OTHER_ID_TYPE
        FROM P_SCENARIO S
       WHERE SCENARIO_ID = P_SCENARIO_ID;

      IF V_UNIQUE_EXPRESSION IS NULL THEN
        P_LOG_TEXT := q'[P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION is mandatory to evaluate UNIQUE_IDENTIIFER]';
      END IF;

      V_UNIQUE_IDENTIFIER := FN_GET_UNIQUE_IDENTIFIER_API (P_TAB, V_UNIQUE_EXPRESSION);

      -- Add unique_identifier value in the part 2 as the column UNIQUE_IDENTIFIER is already initiated
      V_SQL_PART2 := V_SQL_PART2 || V_UNIQUE_IDENTIFIER || ',';

      -- Set LAUNCH_EVENT_STATUS: If no events defined then N else W
      SELECT COUNT(*)
        INTO V_COUNT
        FROM P_SCENARIO_EVENT_FACILITY PEF
             INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
       WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;

      IF V_COUNT = 0 THEN
        V_EVENTS_LAUNCH_STATUS := 'N';
      ELSE
        V_EVENTS_LAUNCH_STATUS := 'W';
      END IF;

      V_SQL_PART1 := V_SQL_PART1 || 'EVENTS_LAUNCH_STATUS,';
      V_SQL_PART2 := V_SQL_PART2 || q'[']' || V_EVENTS_LAUNCH_STATUS || q'[',]';

      V_IDX := P_TAB.FIRST;

      LOOP
        EXIT WHEN V_IDX IS NULL;
        -- If a given column_name is wrong then ignore it
        BEGIN
          -- if V_IDX is defined with quotes then remove them to get data type
          SELECT DATA_TYPE
            INTO V_DATA_TYPE
            FROM USER_TAB_COLUMNS
           WHERE TABLE_NAME = 'P_SCENARIO_INSTANCE'
             AND COLUMN_NAME = UPPER(REPLACE(V_IDX, '"'));

          IF V_DATA_TYPE ='NUMBER' THEN
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[TO_NUMBER(']' || REPLACE(REPLACE(P_TAB(V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)  || q'['),]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[TO_NUMBER(']' || REPLACE(REPLACE(P_TAB(V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL) || q'[')]' || ' AS ' || V_IDX || ',';

          ELSIF V_DATA_TYPE IN ('DATE', 'DATETIME') THEN
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[TO_DATE(']' || P_TAB(V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'['),]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[TO_DATE(']' || P_TAB(V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'[')]' || ' AS ' || V_IDX || ',';
          ELSE
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[']' || P_TAB(V_IDX)  || q'[',]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[']' || P_TAB(V_IDX) || q'[']' || ' AS ' || V_IDX || ',';
          END IF;


        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            -- If the given column is not a p_scenario_instance column but it corresponds to one of the
            -- columns HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID, AMOUNT, SIGN, MOVEMENT_ID, MATCH_ID, SWEEP_ID
            -- PAYMENT_ID, VALUE_DATE, OTHER_ID

            V_SQL_PART1 := V_SQL_PART1 ||
                      CASE REPLACE(V_IDX, '"')
                        WHEN REPLACE(V_HOST_COL, '"') THEN 'HOST_ID,'
                        WHEN REPLACE(V_ENTITY_COL, '"') THEN 'ENTITY_ID,'
                        WHEN REPLACE(V_CURRENCY_COL, '"') THEN 'CURRENCY_CODE,'
                        WHEN REPLACE(V_ACCOUNT_ID_COL, '"') THEN 'ACCOUNT_ID,'
                        WHEN REPLACE(V_AMT_THRESH_COL, '"') THEN 'AMOUNT,'
                        WHEN REPLACE(V_SIGN_COL, '"') THEN 'SIGN,'
                        WHEN REPLACE(V_MOVEMENT_ID_COL, '"') THEN 'MOVEMENT_ID,'
                        WHEN REPLACE(V_MATCH_ID_COL, '"') THEN 'MATCH_ID,'
                        WHEN REPLACE(V_SWEEP_ID_COL, '"') THEN 'SWEEP_ID,'
                        WHEN REPLACE(V_PAYMENT_ID_COL, '"') THEN 'PAYMENT_ID,'
                        WHEN REPLACE(V_VALUE_DATE_COL, '"') THEN 'VALUE_DATE,'
                        WHEN REPLACE(V_OTHER_ID_COL, '"') THEN 'OTHER_ID,'
                      END;

            V_SQL_PART2 := V_SQL_PART2 ||
                      CASE REPLACE(V_IDX, '"')
                        WHEN REPLACE(V_HOST_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_ENTITY_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_CURRENCY_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_ACCOUNT_ID_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_AMT_THRESH_COL, '"') THEN q'[TO_NUMBER(']' || REPLACE(REPLACE(FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)  || q'['),]'
                        WHEN REPLACE(V_SIGN_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_MOVEMENT_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_MATCH_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_SWEEP_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_PAYMENT_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_VALUE_DATE_COL, '"') THEN q'[TO_DATE(']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'['),]'
                        WHEN REPLACE(V_OTHER_ID_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                      END;

            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || q'[']' || ' AS ' || V_IDX || ',';
        END;

        -- Set over_threshold column if amount column exists otherwise set it to N
        -- to allow displaying instances in monitor
        IF REPLACE(V_IDX, '"') = REPLACE(V_AMT_THRESH_COL, '"') -- Ignore existing quotes between tab and P_SCENARIO
           AND FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) IS NOT NULL
           AND V_OVER_THRESHOLD IS NULL THEN
          IF V_HOST_COL IS NOT NULL AND V_ENTITY_COL IS NOT NULL AND V_CURRENCY_COL IS NOT NULL THEN
            BEGIN
              EXECUTE IMMEDIATE q'[SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_HOST_COL) || q'[' AND S.ENTITY_ID = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_ENTITY_COL) || q'[' AND S.CURRENCY_CODE = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_CURRENCY_COL) || q'[']' INTO V_CCY_THRESHOLD_VALUE;
            EXCEPTION
              WHEN OTHERS THEN
                sp_error_log ('', P_USER_ID, 'DBSERVER',
                'PKG_ALERT.SP_CREATE_INSTANCE_API -> Error when evaluating OVER_THRESHOLD for Scenario ID = ' || P_SCENARIO_ID,
                SQLCODE,
                SQLERRM);

            END;
            IF (TO_NUMBER(REPLACE(REPLACE(FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)) > V_CCY_THRESHOLD_VALUE) THEN
              V_OVER_THRESHOLD := 'Y';
            ELSE
              V_OVER_THRESHOLD := 'N';
            END IF;
          END IF;
        END IF;

        V_IDX := P_TAB.NEXT (V_IDX);
      END LOOP;

      -- If V_OVER_THRESHOLD is null then set it N in p_scenario_instance to allow displaying instances in monitor
      IF V_OVER_THRESHOLD IS NOT NULL THEN
        V_SQL_PART1 := V_SQL_PART1 || 'OVER_THRESHOLD,';
        V_SQL_PART2 := V_SQL_PART2 || q'[']' || V_OVER_THRESHOLD || q'[',]';
      END IF;

      -- Check if API_REQUIRED_COLS columns exist in V_TAB
      SELECT REPLACE(REPLACE(REPLACE(API_REQUIRED_COLS, '['), ']'), '"')
        INTO V_API_REQUIRED_COLS
        FROM P_SCENARIO
       WHERE SCENARIO_ID = P_SCENARIO_ID;

      WITH REQ_COLS AS (
        SELECT LTRIM(RTRIM(REGEXP_SUBSTR(V_API_REQUIRED_COLS, '[^,]+', 1, LEVEL))) COL, LEVEL IDX
          FROM DUAL
       CONNECT BY LEVEL <= LENGTH(REGEXP_REPLACE(V_API_REQUIRED_COLS,'[^,]+'))+1)
      SELECT LISTAGG(COL, ',') WITHIN GROUP (ORDER BY IDX)
        INTO V_MISSED_COL
        FROM REQ_COLS
       WHERE INSTR(V_SQL_PART1, COL) = 0;

      IF V_MISSED_COL IS NOT NULL THEN
        P_LOG_TEXT := q'[The following columns are required for this scenario: ]' || V_MISSED_COL;
        RETURN;
      END IF;

      V_SQL_XML_ATTRIBUTES := 'SELECT ' || RTRIM(V_SQL_XML_ATTRIBUTES, ',') || ' FROM DUAL';
      V_ATTRIBUTES_JSON := FN_GET_QUERYRESULT_AS_JSON(V_SQL_XML_ATTRIBUTES);

      V_SQL_PART1 := V_SQL_PART1 || 'ATTRIBUTES_JSON';
      V_SQL_PART2 := V_SQL_PART2 || 'q''[' || V_ATTRIBUTES_JSON || ']''';

      V_SQL := 'INSERT INTO P_SCENARIO_INSTANCE (' || RTRIM(V_SQL_PART1, ',') ||
      ') VALUES (' || RTRIM(V_SQL_PART2, ',') || ') RETURNING ID INTO :1';

      BEGIN
        EXECUTE IMMEDIATE V_SQL RETURNING BULK COLLECT INTO V_ID;
        P_INSTANCE_ID := V_ID(V_ID.FIRST);
        INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
        SELECT P_INSTANCE_ID
          FROM DUAL
         WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = P_INSTANCE_ID);
        P_LOG_TEXT := q'[Scenario Instance created using API: ID=]' || P_INSTANCE_ID;

        SP_LOG_SCENARIO_INSTANCE(P_INSTANCE_ID, q'[Scenario Instance created using API]', P_USER_ID);
      EXCEPTION
        WHEN OTHERS THEN
          P_LOG_TEXT := q'[Error in creating instance: ]' || SQLCODE || '(' || SQLERRM || ')' || CHR(10) || V_SQL;

      END;

    END;

    FUNCTION FN_GET_SCENARIO_INSTANCES (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                         P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                         P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                         P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                         P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                         P_USER_ID         S_USERS.USER_ID%TYPE,
                                         P_FILTER_TREE     VARCHAR2,
                                         P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                         PV_INS_STATUS     VARCHAR2 DEFAULT 'All',
                                         P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                         P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                         P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                         P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                         P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                         P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                         P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                         P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL,
                                         P_FILTER_GRID     VARCHAR2 DEFAULT NULL,
                                         P_SORT_GRID       VARCHAR2 DEFAULT NULL
                                         ) RETURN SYS_REFCURSOR
    IS
      V_SQL       VARCHAR2(10000);
      P_REF       SYS_REFCURSOR;
      V_ROLE_ID   S_USERS.ROLE_ID%TYPE;
    BEGIN
      SELECT ROLE_ID
        INTO V_ROLE_ID
        FROM S_USERS
       WHERE USER_ID = P_USER_ID;

      V_SQL := q'[
        WITH PARMS
        AS (SELECT :P_FACILITY_ID P_FACILITY_ID,
                   :P_HOST_ID P_HOST_ID,
                   :P_ENTITY_ID P_ENTITY_ID,
                   :P_CURRENCY_CODE P_CURRENCY_CODE,
                   :P_VALUE_DATE P_VALUE_DATE,
                   :P_ROLE_ID P_ROLE_ID,
                   :P_CCY_THRESHOLD P_CCY_THRESHOLD,
                   :P_ACCOUNT_ID P_ACCOUNT_ID,
                   :P_MOVEMENT_ID P_MOVEMENT_ID,
                   :P_MATCH_ID P_MATCH_ID,
                   :P_ILM_GROUP_ID P_ILM_GROUP_ID,
                   :P_SWEEP_ID P_SWEEP_ID,
                   :P_PAYMENT_ID P_PAYMENT_ID
              FROM DUAL)
            SELECT * FROM (
            SELECT PSI.*
              FROM PARMS
                   CROSS JOIN ]' ||
              CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                q'[   P_SCENARIO_GUI_ALERT_FACILITY GAF
                     INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                     INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
              ELSE
                q'[   P_SCENARIO ]'
              END ||
              q'[
                   INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
              CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                 q'[
                   INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
              END ||
              CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
              ELSE
                q'[ WHERE ]'
              END ||
              q'[ P_SCENARIO.ACTIVE_FLAG = 'Y' AND (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')
               AND PARMS.P_HOST_ID IN ('All', PSI.HOST_ID)
               AND PARMS.P_ENTITY_ID IN ('All', PSI.ENTITY_ID)
               AND PARMS.P_CURRENCY_CODE IN ('All', PSI.CURRENCY_CODE)
               AND (PSI.VALUE_DATE = PARMS.P_VALUE_DATE OR PARMS.P_VALUE_DATE IS NULL)
               AND (PSI.ACCOUNT_ID = PARMS.P_ACCOUNT_ID OR PARMS.P_ACCOUNT_ID IS NULL)
               AND (PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID OR PARMS.P_MOVEMENT_ID IS NULL)
               AND (PSI.MATCH_ID = P_MATCH_ID OR P_MATCH_ID IS NULL)
               --AND ((PSI.HOST_ID,PSI.ENTITY_ID,PSI.ACCOUNT_ID) IN (SELECT HOST_ID,ENTITY_ID,ACCOUNT_ID FROM TABLE(PKG_ILM.FN_GET_ILM_ACC_IN_GROUP(P_ILM_GROUP_ID))) OR P_ILM_GROUP_ID IS NULL)
               AND (PSI.OTHER_ID = P_ILM_GROUP_ID OR (PSI.OTHER_ID IS NULL OR P_ILM_GROUP_ID IS NULL))
               AND (PSI.SWEEP_ID = P_SWEEP_ID OR P_SWEEP_ID IS NULL)
               -- check user's access for entity/currency and scenario
               AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) != '2'
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSI.SCENARIO_ID, PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID), 1,1) = 'Y'
               ]' || P_FILTER_TREE ||
               CASE WHEN PV_INS_STATUS IS NULL THEN
                 q'[ AND PSI.STATUS IN ('A','P','O') ]'
               ELSE
                 q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
               END ||
               CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                 q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
               END ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                 q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                 THEN 'Y'
                 ELSE 'N' END = 'Y']'
               END ||
               ')' ||
               CASE WHEN P_FILTER_GRID IS NOT NULL THEN ' WHERE ' || P_FILTER_GRID END ||
               ' ORDER BY ' || NVL(P_SORT_GRID, ' ID DESC');

           OPEN P_REF FOR V_SQL
            USING P_FACILITY_ID,
                  P_HOST_ID,
                  P_ENTITY_ID,
                  P_CURRENCY_CODE,
                  P_VALUE_DATE,
                  V_ROLE_ID,
                  P_CCY_THRESHOLD,
                  P_ACCOUNT_ID,
                  P_MOVEMENT_ID,
                  P_MATCH_ID,
                  P_ILM_GROUP_ID,
                  P_SWEEP_ID,
                  P_PAYMENT_ID;

           RETURN P_REF;
    END;

    PROCEDURE SP_SET_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                      PV_NEW_STATUS               P_SCENARIO_INSTANCE.STATUS%TYPE,
                                      P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE)
    IS
        V_LOG_TEXT  P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE;
        V_COUNT     NUMBER;
    BEGIN
         IF PV_NEW_STATUS = 'A' THEN
          V_LOG_TEXT := q'[Set Status to Active]';
        ELSIF PV_NEW_STATUS = 'R' THEN
          V_LOG_TEXT := q'[Set Status to Resolved]';
        ELSE
          V_LOG_TEXT := q'[Set Status to ]' || PV_NEW_STATUS;
        END IF;
        -- Are there events to launch
        SELECT COUNT(*)
          INTO V_COUNT
          FROM P_SCENARIO_EVENT_FACILITY PEF
               INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
               INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
         WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

        IF V_COUNT > 0 THEN
          SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, PV_NEW_STATUS, V_LOG_TEXT, P_USER_ID, 'W');
        ELSE
          SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, PV_NEW_STATUS, V_LOG_TEXT, P_USER_ID, 'N');
        END IF;
    END;

    PROCEDURE SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID        P_SCENARIO.SCENARIO_ID%TYPE)
    IS
    BEGIN
        -- Calculate scenario counts
        DELETE P_SCENARIO_COUNTS
         WHERE SCENARIO_ID = P_SCENARIO_ID;

        INSERT INTO P_SCENARIO_COUNTS (SCENARIO_ID, HOST_ID, ENTITY_ID, CURRENCY_CODE, SCENARIO_COUNT, SCENARIO_COUNT_OVER_T, EMAIL_FLAG, EMAILED_SC_COUNT, EMAILED_SC_COUNT_OVER_T)
        SELECT SCENARIO_ID, NVL (HOST_ID, GLOBAL_VAR.FN_GET_HOST), NVL (ENTITY_ID, 'All'), NVL (CURRENCY_CODE, 'All'), COUNT (*) SCENARIO_COUNT,
               SUM (CASE WHEN PSI.OVER_THRESHOLD = 'Y' THEN 1 ELSE 0 END) scenario_count_over_t, 'N' EMAIL_FLAG, 0 EMAILED_SC_COUNT, 0 EMAILED_SC_COUNT_OVER_T
          FROM P_SCENARIO_INSTANCE psi
         WHERE SCENARIO_ID = P_SCENARIO_ID
           AND STATUS IN ('A','P','O')
      GROUP BY SCENARIO_ID, HOST_ID, ENTITY_ID, CURRENCY_CODE;
    END;

    FUNCTION FN_EXIST_INST_FOR_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE)
    RETURN VARCHAR2
    IS
      V_EXIST_SCENARIO_INSTANCES  VARCHAR2(1);
    BEGIN
      BEGIN
        SELECT 'Y'
          INTO V_EXIST_SCENARIO_INSTANCES
          FROM DUAL
         WHERE EXISTS (SELECT NULL
                         FROM P_SCENARIO_INSTANCE
                        WHERE SCENARIO_ID = P_SCENARIO_ID
                          AND STATUS IN ('A','P','O'));
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          V_EXIST_SCENARIO_INSTANCES := 'N';
      END;

      RETURN V_EXIST_SCENARIO_INSTANCES;
    END;

   FUNCTION FN_GET_RESULT_RESOL_QUERY (P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
   RETURN NUMBER
   IS
      V_RESOLUTION_REF_COLS     P_SCENARIO.RESOLUTION_REF_COLS%TYPE;
      V_PEND_RESOL_QUERY_TEXT   P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT%TYPE;
      V_COLUMN_VAL_NBR          NUMBER;
      V_COLUMN_VAL_DATE         DATE;
      V_COLUMN_VAL              VARCHAR2(4000);
      V_COLUMN_TYPE             VARCHAR2(50);
      cursor_name               INTEGER;
      V_COUNT                   NUMBER;
      v_nbr_rows                NUMBER;
      V_BIND_VARIABLE           VARCHAR2(50);
      V_SCENARIO_ID             P_SCENARIO_INSTANCE.SCENARIO_ID%TYPE;
   BEGIN
      -- Get the correspondent scenario
      SELECT SCENARIO_ID
        INTO V_SCENARIO_ID
        FROM P_SCENARIO_INSTANCE
       WHERE ID = P_SCENARIO_INSTANCE_ID;

      -- Get RESOLUTION_REF_COLS from p_scenario
      SELECT RESOLUTION_REF_COLS, PENDING_RESOLUTION_QUERY_TEXT--, REGEXP_COUNT (PENDING_RESOLUTION_QUERY_TEXT, '(:[^: $]+)'), REGEXP_COUNT (RESOLUTION_REF_COLS, '[^,]+')
        INTO V_RESOLUTION_REF_COLS, V_PEND_RESOL_QUERY_TEXT--, V_NBR_BIND_VARIABLES, V_NBR_RESOL_REF_COLS
        FROM P_SCENARIO
       WHERE SCENARIO_ID = V_SCENARIO_ID;

      V_PEND_RESOL_QUERY_TEXT := 'SELECT COUNT(*) cnt FROM ( ' || V_PEND_RESOL_QUERY_TEXT || ' )';
      -- Check if RESOLUTION_REF_COLS contains the same number of bind variables needed in PENDING_RESOLUTION_QUERY_TEXT
      /*IF V_NBR_BIND_VARIABLES != V_NBR_RESOL_REF_COLS THEN
        sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_RESULT_RESOL_QUERY -> Error for ' || P_SCENARIO_INSTANCE_ID,
                       '-20001',
                       'Number of bind variables defined in PENDING_RESOLUTION_QUERY_TEXT query is not equivalent to those defined in RESOLUTION_REF_COLS'
                     );
      END IF;*/
      cursor_name := dbms_sql.open_cursor;
      dbms_sql.parse(cursor_name, V_PEND_RESOL_QUERY_TEXT, dbms_sql.native);
      FOR rec IN (
          SELECT REPLACE(T.PARAMETER_NAME, '"') BindColName, ROWNUM IDX
            FROM JSON_TABLE (V_RESOLUTION_REF_COLS
            COLUMNS (NESTED PATH '$[*]' COLUMNS(PARAMETER_NAME VARCHAR2(50) PATH '$.content'))) T
           WHERE T.PARAMETER_NAME IS NOT NULL
      )
      LOOP
        -- Get correspondent values/types of given ref_columns from P_SCENARIO_INSTANCE.ATTRIBUTES_XML column
       V_COLUMN_VAL := FN_GET_SCN_ATTR_VAL (rec.BindColName, P_SCENARIO_INSTANCE_ID);
       BEGIN
        V_COLUMN_TYPE := FN_GET_SCN_ATTR_TYPE (rec.BindColName, P_SCENARIO_INSTANCE_ID);
       EXCEPTION
        WHEN OTHERS THEN
          V_COLUMN_TYPE := 'TEXT';
       END;

        -- Get i-th  bind variable names in P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT query
        SELECT REGEXP_SUBSTR (V_PEND_RESOL_QUERY_TEXT, '(:[^: $]+)', 1, rec.idx)
          INTO V_BIND_VARIABLE
          FROM DUAL
         WHERE REGEXP_SUBSTR (V_PEND_RESOL_QUERY_TEXT, '(:[^: $]+)', 1, rec.idx) IS NOT NULL;

        -- Bind variables dynamically
        IF V_COLUMN_TYPE = 'NUMBER' THEN
          V_COLUMN_VAL_NBR := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL));
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL_NBR);
        ELSIF V_COLUMN_TYPE IN ('DATE', 'DATETIME') THEN
          V_COLUMN_VAL_DATE := TO_DATE(V_COLUMN_VAL, CONST_DATE_FORMAT);
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL_DATE);
        ELSE
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL);
        END IF;


      END LOOP;

      DBMS_SQL.DEFINE_COLUMN (cursor_name, 1, V_COUNT);

      v_nbr_rows := dbms_sql.execute(cursor_name);
      LOOP
        IF DBMS_SQL.FETCH_ROWS(cursor_name) > 0 THEN
          DBMS_SQL.COLUMN_VALUE(cursor_name, 1, V_COUNT);
        ELSE
          -- No more rows to copy:
          EXIT;
        END IF;
      END LOOP;
      dbms_sql.close_cursor(cursor_name);

      RETURN V_COUNT;

   EXCEPTION
     WHEN OTHERS THEN
        sp_error_log ('',
            'SYSTEM',
            'DBSERVER',
            'PKG_ALERT.FN_GET_RESULT_RESOL_QUERY -> Error for Instance ID = ' || P_SCENARIO_INSTANCE_ID || ' (SCENARIO_ID = ' || V_SCENARIO_ID || ')',
            SQLCODE,
            SQLERRM
          );
         dbms_sql.close_cursor(cursor_name);
         RETURN -1;
   END;

    FUNCTION FN_GET_SCN_ATTR_VAL (P_ATTRIBUTE_NAME          VARCHAR2,
                                  P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2
    IS
      V_ATTRIBUTE_VALUE     VARCHAR2(4000);
      V_SQL                 VARCHAR2(4000);
    BEGIN
      IF P_ATTRIBUTE_NAME = CONST_INSTANCE_ID_ATTR THEN
        V_ATTRIBUTE_VALUE := TO_CHAR(P_SCENARIO_INSTANCE_ID);

      ELSIF P_ATTRIBUTE_NAME IN ('HOST_ID','ENTITY_ID','CURRENCY_CODE','SIGN','AMOUNT','ACCOUNT_ID','MOVEMENT_ID','SWEEP_ID','PAYMENT_ID','OTHER_ID') THEN
        EXECUTE IMMEDIATE 'SELECT ' || P_ATTRIBUTE_NAME || ' FROM P_SCENARIO_INSTANCE WHERE ID = :P_SCENARIO_INSTANCE_ID'
           INTO V_ATTRIBUTE_VALUE USING P_SCENARIO_INSTANCE_ID;
      ELSE
        V_SQL := q'[SELECT JSON_VALUE(ATTRIBUTES_JSON, '$.rowset.row."]' || LOWER(P_ATTRIBUTE_NAME) || q'[".content')
          FROM P_SCENARIO_INSTANCE PSI
         WHERE PSI.ID = :P_SCENARIO_INSTANCE_ID]';

         EXECUTE IMMEDIATE V_SQL INTO V_ATTRIBUTE_VALUE USING P_SCENARIO_INSTANCE_ID;
      END IF;

       RETURN V_ATTRIBUTE_VALUE;
    END;

    FUNCTION FN_GET_SCN_ATTR_TYPE (P_ATTRIBUTE_NAME         VARCHAR2,
                                   P_SCENARIO_INSTANCE_ID   P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2
    IS
      V_ATTRIBUTE_TYPE    VARCHAR2(50);
      V_SQL               VARCHAR2(4000);
    BEGIN
      IF P_ATTRIBUTE_NAME = CONST_INSTANCE_ID_ATTR THEN
        V_ATTRIBUTE_TYPE := 'NUMBER';
      ELSE
        V_SQL := q'[SELECT JSON_VALUE(ATTRIBUTES_JSON, '$.rowset.row."]' || LOWER(P_ATTRIBUTE_NAME) || q'[".type')
          FROM P_SCENARIO_INSTANCE PSI
         WHERE PSI.ID = :P_SCENARIO_INSTANCE_ID]';

        EXECUTE IMMEDIATE V_SQL INTO V_ATTRIBUTE_TYPE USING P_SCENARIO_INSTANCE_ID;
      END IF;

      RETURN V_ATTRIBUTE_TYPE;
    END;

    -- =====================================================
    -- PERFORMANCE OPTIMIZED PROCEDURES FOR PARALLEL PROCESSING
    -- =====================================================

    -- Main optimized procedure that separates instance creation from event processing
    PROCEDURE SP_PROCESS_SCENARIO_OPTIMIZED (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                            P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                            P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                            P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
                                            P_PARALLEL_DEGREE  NUMBER DEFAULT 4)
    IS
        V_INSTANCE_IDS                  SYS.ODCINUMBERLIST;
        V_START_TIMESTAMP               TIMESTAMP;
        V_END_TIMESTAMP                 TIMESTAMP;
        V_PROCESS_DURATION              INTERVAL DAY (2) TO SECOND (4);
        V_START_TEST_TIME               DATE;
        V_ERROR_LOCATION                VARCHAR2(10);
        V_NBR_FAILED_INST_EVENT         NUMBER := 0;
    BEGIN
        V_ERROR_LOCATION := '10';
        V_START_TIMESTAMP := SYSTIMESTAMP;
        V_START_TEST_TIME := GLOBAL_VAR.SYS_DATE;

        -- Step 1: Bulk insert all instances first
        V_ERROR_LOCATION := '20';
        SP_BULK_INSERT_INSTANCES(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, V_INSTANCE_IDS);

        -- Step 2: Process events in parallel if there are instances with events
        V_ERROR_LOCATION := '30';
        IF V_INSTANCE_IDS IS NOT NULL AND V_INSTANCE_IDS.COUNT > 0 THEN
            SP_PROCESS_EVENTS_PARALLEL(V_INSTANCE_IDS, P_USER_ID, P_PARALLEL_DEGREE);
        END IF;

        -- Step 3: Update scenario counts and cleanup
        V_ERROR_LOCATION := '40';
        SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);

        -- Step 4: Update existing instances
        V_ERROR_LOCATION := '50';
        SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);

        -- Step 5: Update timing information
        V_ERROR_LOCATION := '60';
        V_END_TIMESTAMP := SYSTIMESTAMP;
        V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;

        UPDATE P_SCENARIO_SYSTEM
        SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
            LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION)
        WHERE SCENARIO_ID = P_SCENARIO_ID;

        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
            SELECT P_SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION)
            FROM DUAL
            WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_SYSTEM WHERE SCENARIO_ID = P_SCENARIO_ID);
        END IF;

        -- Step 6: Update schedule information if applicable
        V_ERROR_LOCATION := '70';
        IF P_SCENARIO_SCHEDULE_ID IS NOT NULL THEN
            UPDATE P_SCENARIO_SCHEDULE
            SET LAST_RUN_STARTED = V_START_TEST_TIME,
                LAST_RUN_ENDED = GLOBAL_VAR.SYS_DATE,
                LAST_RUN_STATUS = CASE WHEN V_NBR_FAILED_INST_EVENT > 0 THEN 'F' ELSE 'S' END
            WHERE SCENARIO_ID = P_SCENARIO_ID
              AND SCENARIO_SCHEDULE_ID = P_SCENARIO_SCHEDULE_ID;
        END IF;

        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', 'SYSTEM', 'DBSERVER',
                        'PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED: Error when running scenario ' || P_SCENARIO_ID ||
                        ' at location ' || V_ERROR_LOCATION || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                        SQLCODE, SQLERRM);
            RAISE;
    END;

    -- Bulk insert instances procedure - optimized for performance
    PROCEDURE SP_BULK_INSERT_INSTANCES (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                       P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                       P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                       P_INSTANCE_IDS     OUT SYS.ODCINUMBERLIST)
    IS
        TYPE T_INSTANCE_BULK IS TABLE OF P_SCENARIO_INSTANCE%ROWTYPE;
        V_INSTANCES                     T_INSTANCE_BULK := T_INSTANCE_BULK();
        V_INSTANCE_IDS_TEMP             SYS.ODCINUMBERLIST := SYS.ODCINUMBERLIST();
        V_COUNT_EVENT_MAP               NUMBER;
        V_INSTANCE_ID                   NUMBER;
        V_ERROR_LOCATION                VARCHAR2(10);

        CURSOR CUR_INSTANCES IS
        SELECT INSTANCE_UNIQUE_EXPRESSION, UNIQUE_IDENTIFIER, HOST_ID, ENTITY_ID, CURRENCY_CODE,
               ACCOUNT_ID, AMOUNT, SIGN, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID,
               OVER_THRESHOLD, VALUE_DATE, OTHER_ID, JSON_ATTRIBUTES, IS_UNIQUE_ROW
        FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_QUERY_TEXT, P_USER_ID));

    BEGIN
        V_ERROR_LOCATION := '10';

        -- Check if there are events to launch for this scenario
        SELECT COUNT(*)
        INTO V_COUNT_EVENT_MAP
        FROM P_SCENARIO_EVENT_FACILITY PEF
        INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
        WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;

        V_ERROR_LOCATION := '20';

        -- Process all instances from the scenario query
        FOR rec IN CUR_INSTANCES LOOP
            V_ERROR_LOCATION := '30';

            -- Check if this unique identifier is unique
            IF rec.IS_UNIQUE_ROW = 'Y' THEN
                DECLARE
                    V_COUNT_INSTANCES NUMBER;
                BEGIN
                    -- Check if instance already exists
                    SELECT COUNT(*)
                    INTO V_COUNT_INSTANCES
                    FROM P_SCENARIO_INSTANCE
                    WHERE SCENARIO_ID = P_SCENARIO_ID
                      AND UNIQUE_IDENTIFIER = rec.UNIQUE_IDENTIFIER;

                    V_ERROR_LOCATION := '40';

                    -- Only create new instance if it doesn't exist
                    IF V_COUNT_INSTANCES = 0 THEN
                        V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;

                        -- Prepare instance record for bulk insert
                        V_INSTANCES.EXTEND;
                        V_INSTANCES(V_INSTANCES.COUNT).ID := V_INSTANCE_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).SCENARIO_ID := P_SCENARIO_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).UNIQUE_IDENTIFIER := rec.UNIQUE_IDENTIFIER;
                        V_INSTANCES(V_INSTANCES.COUNT).STATUS := 'A';
                        V_INSTANCES(V_INSTANCES.COUNT).RAISED_DATETIME := GLOBAL_VAR.SYS_DATE;
                        V_INSTANCES(V_INSTANCES.COUNT).LAST_RAISED_DATETIME := GLOBAL_VAR.SYS_DATE;
                        V_INSTANCES(V_INSTANCES.COUNT).RESOLVED_DATETIME := NULL;
                        V_INSTANCES(V_INSTANCES.COUNT).RESOLVED_BY_USER := NULL;
                        V_INSTANCES(V_INSTANCES.COUNT).EVENTS_LAUNCH_STATUS := CASE WHEN V_COUNT_EVENT_MAP > 0 THEN 'W' ELSE 'N' END;
                        V_INSTANCES(V_INSTANCES.COUNT).HOST_ID := rec.HOST_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).ENTITY_ID := rec.ENTITY_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).CURRENCY_CODE := rec.CURRENCY_CODE;
                        V_INSTANCES(V_INSTANCES.COUNT).ACCOUNT_ID := rec.ACCOUNT_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).AMOUNT := rec.AMOUNT;
                        V_INSTANCES(V_INSTANCES.COUNT).SIGN := rec.SIGN;
                        V_INSTANCES(V_INSTANCES.COUNT).OVER_THRESHOLD := rec.OVER_THRESHOLD;
                        V_INSTANCES(V_INSTANCES.COUNT).MOVEMENT_ID := rec.MOVEMENT_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).MATCH_ID := rec.MATCH_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).SWEEP_ID := rec.SWEEP_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).PAYMENT_ID := rec.PAYMENT_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).OTHER_ID := rec.OTHER_ID;
                        V_INSTANCES(V_INSTANCES.COUNT).VALUE_DATE := rec.VALUE_DATE;
                        V_INSTANCES(V_INSTANCES.COUNT).ATTRIBUTES_JSON := rec.JSON_ATTRIBUTES;

                        -- Add to instance IDs list if events need to be processed
                        IF V_COUNT_EVENT_MAP > 0 THEN
                            V_INSTANCE_IDS_TEMP.EXTEND;
                            V_INSTANCE_IDS_TEMP(V_INSTANCE_IDS_TEMP.COUNT) := V_INSTANCE_ID;
                        END IF;
                    END IF;
                END;
            END IF;
        END LOOP;

        V_ERROR_LOCATION := '50';

        -- Bulk insert all instances at once
        IF V_INSTANCES.COUNT > 0 THEN
            FORALL i IN 1..V_INSTANCES.COUNT
                INSERT INTO P_SCENARIO_INSTANCE VALUES V_INSTANCES(i);

            -- Bulk insert into active instances table
            FORALL i IN 1..V_INSTANCES.COUNT
                INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
                VALUES (V_INSTANCES(i).ID);
        END IF;

        V_ERROR_LOCATION := '60';

        -- Return the instance IDs that need event processing
        P_INSTANCE_IDS := V_INSTANCE_IDS_TEMP;

        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', 'SYSTEM', 'DBSERVER',
                        'PKG_ALERT.SP_BULK_INSERT_INSTANCES: Error at location ' || V_ERROR_LOCATION ||
                        ' for scenario ' || P_SCENARIO_ID || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                        SQLCODE, SQLERRM);
            RAISE;
    END;

    -- Parallel event processing procedure
    PROCEDURE SP_PROCESS_EVENTS_PARALLEL (P_INSTANCE_IDS     SYS.ODCINUMBERLIST,
                                          P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                          P_PARALLEL_DEGREE  NUMBER DEFAULT 4)
    IS
        V_BATCH_SIZE                    NUMBER := GREATEST(1, CEIL(P_INSTANCE_IDS.COUNT / P_PARALLEL_DEGREE));
        V_BATCH_START                   NUMBER;
        V_BATCH_END                     NUMBER;
        V_BATCH_IDS                     SYS.ODCINUMBERLIST;
        V_ERROR_LOCATION                VARCHAR2(10);
    BEGIN
        V_ERROR_LOCATION := '10';

        -- Process instances in parallel batches
        FOR i IN 1..P_PARALLEL_DEGREE LOOP
            V_BATCH_START := ((i - 1) * V_BATCH_SIZE) + 1;
            V_BATCH_END := LEAST(i * V_BATCH_SIZE, P_INSTANCE_IDS.COUNT);

            -- Skip if no instances in this batch
            IF V_BATCH_START <= P_INSTANCE_IDS.COUNT THEN
                V_ERROR_LOCATION := '20';

                -- Create batch of instance IDs
                V_BATCH_IDS := SYS.ODCINUMBERLIST();
                FOR j IN V_BATCH_START..V_BATCH_END LOOP
                    V_BATCH_IDS.EXTEND;
                    V_BATCH_IDS(V_BATCH_IDS.COUNT) := P_INSTANCE_IDS(j);
                END LOOP;

                V_ERROR_LOCATION := '30';

                -- Process this batch (in the current implementation, we'll process sequentially
                -- but this structure allows for future parallel processing using DBMS_PARALLEL_EXECUTE)
                SP_PROCESS_EVENT_BATCH(V_BATCH_IDS, P_USER_ID);
            END IF;
        END LOOP;

        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', 'SYSTEM', 'DBSERVER',
                        'PKG_ALERT.SP_PROCESS_EVENTS_PARALLEL: Error at location ' || V_ERROR_LOCATION ||
                        CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                        SQLCODE, SQLERRM);
            RAISE;
    END;

    -- Process a batch of events
    PROCEDURE SP_PROCESS_EVENT_BATCH (P_INSTANCE_IDS     SYS.ODCINUMBERLIST,
                                     P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
    IS
        V_RESULT                        NUMBER;
        V_PROCESSED_COUNT               NUMBER := 0;
        V_FAILED_COUNT                  NUMBER := 0;
        V_ERROR_LOCATION                VARCHAR2(10);
    BEGIN
        V_ERROR_LOCATION := '10';

        -- Process each instance in the batch
        FOR i IN 1..P_INSTANCE_IDS.COUNT LOOP
            BEGIN
                V_ERROR_LOCATION := '20';

                -- Use optimized event launcher
                V_RESULT := FN_LAUNCH_EVENT_OPTIMIZED(P_INSTANCE_IDS(i), P_USER_ID);

                V_ERROR_LOCATION := '30';

                IF V_RESULT = 1 THEN
                    V_PROCESSED_COUNT := V_PROCESSED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE
                    SET EVENTS_LAUNCH_STATUS = 'L'
                    WHERE ID = P_INSTANCE_IDS(i);
                ELSE
                    V_FAILED_COUNT := V_FAILED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE
                    SET EVENTS_LAUNCH_STATUS = 'F'
                    WHERE ID = P_INSTANCE_IDS(i);
                END IF;

                -- Commit every 50 instances to avoid long transactions
                IF MOD(i, 50) = 0 THEN
                    COMMIT;
                END IF;

            EXCEPTION
                WHEN OTHERS THEN
                    V_FAILED_COUNT := V_FAILED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE
                    SET EVENTS_LAUNCH_STATUS = 'F'
                    WHERE ID = P_INSTANCE_IDS(i);

                    sp_error_log('', P_USER_ID, 'DBSERVER',
                                'Error processing instance ' || P_INSTANCE_IDS(i) || ': ' || SQLERRM,
                                SQLCODE, SQLERRM);
            END;
        END LOOP;

        -- Final commit
        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', 'SYSTEM', 'DBSERVER',
                        'PKG_ALERT.SP_PROCESS_EVENT_BATCH: Error at location ' || V_ERROR_LOCATION ||
                        CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                        SQLCODE, SQLERRM);
            RAISE;
    END;

    -- Optimized event launcher with reduced overhead
    FUNCTION FN_LAUNCH_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                       P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
    RETURN NUMBER
    IS
        V_RETURN_RESULT                 NUMBER := 1;
        V_NBR_FAILED_EVENTS             NUMBER := 0;
        V_SQL                           CLOB;
        V_PROGRAM_ID                    P_SCENARIO_EVENT_FACILITY.PROGRAM_ID%TYPE;
        V_EVENT_FACILITY_ID             P_SCENARIO_EVENT_FACILITY.ID%TYPE;
        V_EXECUTE_WHEN                  P_SCENARIO_EVENT_MAPPING.EXECUTE_WHEN%TYPE;
        V_REPEAT_ON_RERAISE             P_SCENARIO_EVENT_MAPPING.REPEAT_ON_RERAISE%TYPE;
        V_RESOLVED_DATETIME             P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE;
        V_MOVEMENT_ID                   P_MOVEMENT.MOVEMENT_ID%TYPE;
        V_ERROR_LOCATION                VARCHAR2(10);

        -- Optimized cursor for events
        CURSOR CUR_EVENTS_OPT IS
        SELECT PEF.ID, PEF.PROGRAM_ID, MAP.EXECUTE_WHEN, MAP.REPEAT_ON_RERAISE,
               PSI.RESOLVED_DATETIME, PSI.MOVEMENT_ID
        FROM P_SCENARIO_EVENT_FACILITY PEF
        INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
        INNER JOIN P_SCENARIO_INSTANCE PSI ON (PSI.SCENARIO_ID = MAP.SCENARIO_ID)
        WHERE PSI.ID = P_SCENARIO_INSTANCE_ID
          AND PSI.STATUS = 'A'
          AND PSI.EVENTS_LAUNCH_STATUS = 'W'
        ORDER BY MAP.ORDINAL;

    BEGIN
        V_ERROR_LOCATION := '10';

        -- Process events for this instance
        FOR rec IN CUR_EVENTS_OPT LOOP
            BEGIN
                V_ERROR_LOCATION := '20';

                V_EVENT_FACILITY_ID := rec.ID;
                V_PROGRAM_ID := rec.PROGRAM_ID;
                V_EXECUTE_WHEN := rec.EXECUTE_WHEN;
                V_REPEAT_ON_RERAISE := rec.REPEAT_ON_RERAISE;
                V_RESOLVED_DATETIME := rec.RESOLVED_DATETIME;
                V_MOVEMENT_ID := rec.MOVEMENT_ID;

                -- Check execution conditions
                IF (        (V_NBR_FAILED_EVENTS > 0 AND V_EXECUTE_WHEN = 'E')
                         OR (V_NBR_FAILED_EVENTS = 0 AND V_EXECUTE_WHEN = 'S')
                         OR (NVL(V_EXECUTE_WHEN, 'A') = 'A'))
                    AND (   V_RESOLVED_DATETIME IS NULL
                        OR (V_REPEAT_ON_RERAISE = 'Y' AND V_RESOLVED_DATETIME IS NOT NULL))
                THEN
                    V_ERROR_LOCATION := '30';

                    -- Optimized event processing for movement updates (Program ID 6)
                    IF V_PROGRAM_ID = 6 AND V_MOVEMENT_ID IS NOT NULL THEN
                        -- Fast movement update
                        V_SQL := 'BEGIN UPDATE P_MOVEMENT SET LAST_UPDATE_DATE = SYSDATE, UPDATE_USER = ''' ||
                                P_USER_ID || ''' WHERE MOVEMENT_ID = ' || V_MOVEMENT_ID ||
                                '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(' || P_SCENARIO_INSTANCE_ID ||
                                ', ''Movement updated: '' || ' || V_MOVEMENT_ID || ', ''' || P_USER_ID || '''); END;';
                    ELSE
                        -- Fall back to original event launcher for other event types
                        V_RETURN_RESULT := PKG_ALERT.FN_LAUNCH_SCEN_EVENT(P_SCENARIO_INSTANCE_ID, P_USER_ID);
                        CONTINUE;
                    END IF;

                    V_ERROR_LOCATION := '40';

                    -- Execute the optimized SQL
                    IF V_SQL IS NOT NULL THEN
                        BEGIN
                            EXECUTE IMMEDIATE V_SQL;
                            V_RETURN_RESULT := 1;
                        EXCEPTION
                            WHEN OTHERS THEN
                                V_RETURN_RESULT := -1;
                                V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;

                                SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
                                    'Error in optimized event ' || V_EVENT_FACILITY_ID || ': ' || SQLERRM, P_USER_ID);

                                sp_error_log('', P_USER_ID, 'DBSERVER',
                                            'PKG_ALERT.FN_LAUNCH_EVENT_OPTIMIZED -> Error for instance=' || P_SCENARIO_INSTANCE_ID,
                                            SQLCODE, SQLERRM);
                        END;
                    END IF;
                END IF;

            EXCEPTION
                WHEN OTHERS THEN
                    V_RETURN_RESULT := -1;
                    V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;

                    SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
                        'Error processing event ' || V_EVENT_FACILITY_ID || ': ' || SQLERRM, P_USER_ID);
            END;
        END LOOP;

        RETURN V_RETURN_RESULT;

    EXCEPTION
        WHEN OTHERS THEN
            sp_error_log('', P_USER_ID, 'DBSERVER',
                        'PKG_ALERT.FN_LAUNCH_EVENT_OPTIMIZED: Error at location ' || V_ERROR_LOCATION ||
                        ' for instance ' || P_SCENARIO_INSTANCE_ID || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                        SQLCODE, SQLERRM);
            RETURN -1;
    END;

END;
EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_QUERY_TEXT -> Error for ' || P_ID,
                       SQLCODE,
                       SQLERRM
                     );
         RAISE;
RETURN NULL;

END;

   -- Take text string for SQL that selects host_id, entity_id, movement_id and make it into a ref cursor
   FUNCTION FN_GET_CUR (P_SQLTEXT CLOB)
      RETURN SYS_REFCURSOR
   IS
      R_CURSOR   SYS_REFCURSOR;
BEGIN
OPEN R_CURSOR FOR P_SQLTEXT;
RETURN R_CURSOR;
END;

   PROCEDURE PROC_GET_CUR
IS
BEGIN
      -- Sole purpose of procedure is to allow java code to access p_cursor as output of procedure
NULL;
END;

   /* Return count of records for a scenario subquery */
   FUNCTION FN_GET_COUNT_FOR_QUERY (P_ID             P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_EXTRA_CONDS    VARCHAR2 DEFAULT NULL)
      RETURN NUMBER
   IS
      V_CUR          SYS_REFCURSOR;
      V_COUNT        NUMBER;
      V_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE;
BEGIN
      -- Select count from scenario subquery
      V_QUERY_TEXT := 'SELECT COUNT(*) "CNT" FROM ('
                      || CHR(10)
                      || PKG_ALERT.FN_GET_QUERY_TEXT (P_ID)
                      || CHR(10)
                      || ')';

      -- Append conditions if supplied
      IF P_EXTRA_CONDS IS NOT NULL
      THEN
         V_QUERY_TEXT := V_QUERY_TEXT || ' ' || P_EXTRA_CONDS;
END IF;

      -- Get ref cursor from function
      V_CUR := PKG_ALERT.FN_GET_CUR (V_QUERY_TEXT);

FETCH V_CUR INTO V_COUNT;

IF V_CUR%ISOPEN
      THEN
         CLOSE V_CUR;
END IF;

RETURN V_COUNT;
END;

   -- WHOLE ROWS: USE BULK COLLECT - FASTER...
   -- Take ref cursor and and pipe output to plsql table
   -- whose record structure matches p_movement row
   -- it can be treated in SQL as a table having exactly same structure as p_movement

   FUNCTION FN_GET_MOVEMENT_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MOVEMENT_TAB
      PIPELINED
   AS
      L_TAB   T_MOVEMENT_TAB;
BEGIN
      LOOP
FETCH P_CURSOR
          BULK COLLECT INTO L_TAB LIMIT vLimit;
          EXIT WHEN L_TAB.COUNT = 0;

FOR I IN 1 .. L_TAB.COUNT
          LOOP
             PIPE ROW (L_TAB (I));
END LOOP;

END LOOP;
CLOSE P_CURSOR;
END;

   -- Take ref cursor and and pipe output to plsql table
   -- whose record structure matches P_MATCH row
   -- it can be treated in SQL as a table having exactly same structure as P_MATCH

   FUNCTION FN_GET_MATCH_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_MATCH_TAB
      PIPELINED
   AS
      L_TAB   T_MATCH_TAB;
BEGIN
FETCH P_CURSOR
    BULK COLLECT INTO L_TAB;

FOR I IN 1 .. L_TAB.COUNT
      LOOP
         PIPE ROW (L_TAB (I));
END LOOP;

CLOSE P_CURSOR;
END;

   FUNCTION FN_GET_P_SCENARIO_COUNT_ROWS (P_CURSOR IN SYS_REFCURSOR)
      RETURN T_P_SCENARIO_COUNTS_TAB
      PIPELINED
   AS
      L_TAB   T_P_SCENARIO_COUNTS_TAB;
BEGIN
FETCH P_CURSOR
    BULK COLLECT INTO L_TAB;

FOR I IN 1 .. L_TAB.COUNT
      LOOP
         PIPE ROW (L_TAB (I));
END LOOP;

CLOSE P_CURSOR;
END;

   --Determine scenario presentation/notification
   -- This function returns the P_NOTIFY_SCENARIO non-key column values
   -- (ACCESS_REQUIRED, DELIVER_POPUPS, FLASH_ICON, SEND_EMAIL) concatenated together:

   FUNCTION FN_GET_SCENARIO_ACCESS (P_SCENARIO_ID    P_NOTIFY_SCENARIO.SCENARIO_ID%TYPE,
                                    P_HOST_ID        P_NOTIFY_SCENARIO.HOST_ID%TYPE,
                                    P_ROLE_ID        P_NOTIFY_SCENARIO.ROLE_ID%TYPE,
                                    P_ENTITY_ID      P_NOTIFY_SCENARIO.ENTITY_ID%TYPE)
      RETURN VARCHAR
   IS
      R_NOTIFY_SCENARIO          P_NOTIFY_SCENARIO%ROWTYPE;
      V_PERFORM_GENERAL_LOOKUP   VARCHAR2 (1) := NULL;
      V_RETURN_VALS              VARCHAR2 (10);
      V_SEC_ENTITY_COL           P_SCENARIO.SEC_ENTITY_COL%TYPE;
BEGIN
      -- GET INFO FROM SCENARIO TABLE
BEGIN
SELECT SEC_ENTITY_COL
INTO V_SEC_ENTITY_COL
FROM P_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID;
END;

      -- For a scenario where SEC_ENTITY_COL is not null:
      -- First look for the P_NOTIFY_SCENARIO record having the relevant
      -- scenario, host and role, and entity_id=<supplied entity_id>.
      IF V_SEC_ENTITY_COL IS NOT NULL -- GET INFO FROM NOTIFY_SCENARIO TABLE for specific entity
      THEN
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = P_ROLE_ID
  AND ENTITY_ID = P_ENTITY_ID;
EXCEPTION
            WHEN NO_DATA_FOUND
            THEN
               V_PERFORM_GENERAL_LOOKUP := 'Y';
END;
ELSE
         V_PERFORM_GENERAL_LOOKUP := 'Y';
END IF;

      -- If no record is found then look for the P_NOTIFY_SCENARIO record
      -- having the relevant scenario, host and role, and entity_id='All'
      -- This also covers the case where R_SCENARIO.SEC_ENTITY_COL IS NULL
      -- If no record found then assume no access
      IF V_PERFORM_GENERAL_LOOKUP = 'Y'
      THEN
         -- GET INFO FROM NOTIFY_SCENARIO TABLE
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = P_ROLE_ID
  AND ENTITY_ID = 'All';
EXCEPTION
            WHEN NO_DATA_FOUND THEN
BEGIN
SELECT *
INTO R_NOTIFY_SCENARIO
FROM P_NOTIFY_SCENARIO
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND HOST_ID = P_HOST_ID
  AND ROLE_ID = 'All'
  AND ENTITY_ID = 'All';
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        NULL;
END;
END;
END IF;

      IF R_NOTIFY_SCENARIO.ACCESS_REQUIRED IS NOT NULL
      THEN
         V_RETURN_VALS :=
               NVL (R_NOTIFY_SCENARIO.ACCESS_REQUIRED, 'N')
            || NVL (R_NOTIFY_SCENARIO.DELIVER_POPUPS, 'N')
            || NVL (R_NOTIFY_SCENARIO.FLASH_ICON, 'N')
            || NVL (R_NOTIFY_SCENARIO.SEND_EMAIL, 'N')
            || NVL (R_NOTIFY_SCENARIO.FULL_INSTANCE_ACCESS, 'N');
ELSE
         V_RETURN_VALS := 'NNNNN';
END IF;

RETURN V_RETURN_VALS;
EXCEPTION
      WHEN OTHERS
      THEN
         -- most likely exception is no data_found
         -- returning 'no access' ensures calling code can operate
         V_RETURN_VALS := 'NNNNN';

RETURN V_RETURN_VALS;
END;

   PROCEDURE PROC_POPULATE_SCENARIO_COUNTS(vSystemFlag VARCHAR2)
IS
      /*
      Define selection of alert queries for which pop must run
      Will need extra conditions to ensure only the required (active) queries are processed
      */
      v_err_loc VARCHAR2(10); -- Error location
      vSCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE;
CURSOR C_QUERY_TO_RUN
      IS
SELECT S.*
FROM    P_SCENARIO S
            LEFT OUTER JOIN
        P_SCENARIO_SYSTEM SS
        ON (SS.SCENARIO_ID = S.SCENARIO_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND S.SYSTEM_FLAG = vSystemFlag
  AND NVL(S.RECORD_SCENARIO_INSTANCES, 'N') = 'N'
    /* start/end boundaries are satisfied */
  AND PKG_ALERT.FN_TEST_START_END_TIME (GLOBAL_VAR.SYS_DATE, S.START_TIME, S.END_TIME) = 'Y'
  AND (SS.LAST_RUN_DATE IS NULL -- scenario never checked before
    OR SS.LAST_RUN_DATE > GLOBAL_VAR.SYS_DATE -- Last checked in future (test date anomaly)
    OR -- or scenario was last checked 'run every' time ago
       (SS.LAST_RUN_DATE <
        (  GLOBAL_VAR.SYS_DATE
            - (  TO_NUMBER (
                         TO_CHAR (
                                 TO_DATE (NVL (S.RUN_EVERY, '00:00:00'), 'HH24:MI:SS'),
                                 'SSSSS'))
                / 86400))));

/*
Cursor C_GET_COUNTS_ETY_CCY  defines how to get counts grouped by entity
and currency for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id, currency_code
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY_CCY_THRESH (
         P_SCENARIO_ID         P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL        P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL      P_SCENARIO.SEC_ENTITY_COL%TYPE,
         P_SEC_CURRENCY_COL    P_SCENARIO.SEC_CURRENCY_COL%TYPE,
         P_AMT_THRESH_COL      P_SCENARIO.AMT_THRESHOLD_COL%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || P_SEC_CURRENCY_COL
                         || ' "CURRENCY_CODE", '
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         --> this bit compares amount to ccy threshold only above t are counted
                         || 'COUNT (CASE WHEN '
                         || P_AMT_THRESH_COL
                         || ' >= (SELECT STLCCYTAB.THRESHOLD_PRODUCT FROM S_CURRENCY STLCCYTAB '
                         || 'WHERE STLCCYTAB.HOST_ID = STLSCENQUERY.' || P_SEC_HOST_COL
                         || ' AND STLCCYTAB.ENTITY_ID = STLSCENQUERY.' || P_SEC_ENTITY_COL
                         || ' AND STLCCYTAB.CURRENCY_CODE = STLSCENQUERY.' || P_SEC_CURRENCY_COL ||') '
                         || ' THEN 1 ELSE NULL END ) "SCENARIO_COUNT_OVER_T" ' --above t count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         --|| fn_scenario_with_rownum(
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL
                         || ', '
                         || P_SEC_CURRENCY_COL)));

/*
Cursor C_GET_COUNTS_ETY_CCY  defines how to get counts grouped by entity
and currency for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id, currency_code
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY_CCY (
         P_SCENARIO_ID         P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL        P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL      P_SCENARIO.SEC_ENTITY_COL%TYPE,
         P_SEC_CURRENCY_COL    P_SCENARIO.SEC_CURRENCY_COL%TYPE)
      IS --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || P_SEC_CURRENCY_COL
                         || ' "CURRENCY_CODE", '
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         || 'COUNT(*) "SCENARIO_COUNT_OVER_T" ' -- Total count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL
                         || ', '
                         || P_SEC_CURRENCY_COL)));

/*
Cursor C_GET_COUNTS_ETY defines how to get counts grouped by host-entity
for a given query id aliased as 'source_data'.
The source data query must return at least host_id, entity_id
as mentioned in P_SCENARIO.sec_host_col etc

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
*/
CURSOR C_GET_COUNTS_ETY (
         P_SCENARIO_ID       P_SCENARIO.SCENARIO_ID%TYPE,
         P_SEC_HOST_COL      P_SCENARIO.SEC_HOST_COL%TYPE,
         P_SEC_ENTITY_COL    P_SCENARIO.SEC_ENTITY_COL%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!
SELECT *
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR (
                    --> build query SELECT list
                        q'[SELECT ']'
                         || P_SCENARIO_ID
                         || q'[' "SCENARIO_ID", ]'
                         || P_SEC_HOST_COL
                         || ' "HOST_ID", '
                         || P_SEC_ENTITY_COL
                         || ' "ENTITY_ID", '
                         || q'['All' "CURRENCY_CODE", ]'
                         || 'COUNT(*) "SCENARIO_COUNT", ' -- Total count
                         || 'COUNT(*) "SCENARIO_COUNT_OVER_T" ' -- Total count
                         || ',NULL "EMAIL_FLAG", 0 "EMAILED_SC_COUNT", 0 "EMAILED_SC_COUNT_OVER_T"'
                         --> make scenario subquery and alias it to STLSCENQUERY
                         || 'FROM ('
                         || CHR(10)
                         || PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID)
                         || CHR(10)
                         || ') STLSCENQUERY '
                         --< end build of query SELECT list
                         --> Add grouping
                         || ' GROUP BY '
                         || P_SEC_HOST_COL
                         || ', '
                         || P_SEC_ENTITY_COL)));

/*
 Cursor C_GET_COUNTS_ETY defines how to get simple count from
 a given query id aliased as 'source_data'.

!!This cursor is for a query where amount threshold comparison IS NOT SUPPORTED
so SCENARIO_COUNT_OVER_T will be same as SCENARIO_COUNT!!
 */
CURSOR C_GET_COUNT_ONLY (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE)
      IS
         --SELECT LIST MATCHES RECORD OF P_SCENARIO_COUNTS!!!

SELECT P_SCENARIO_ID SCENARIO_ID,
       GLOBAL_VAR.FN_GET_HOST HOST_ID,
       'All' ENTITY_ID,
       'All' CURRENCY_CODE,
       CNT SCENARIO_COUNT,
       CNT SCENARIO_COUNT_OVER_T,
       'N' EMAIL_FLAG,
       0 EMAILED_SC_COUNT,
       0 EMAILED_SC_COUNT_OVER_T
FROM (SELECT PKG_ALERT.FN_GET_COUNT_FOR_QUERY (P_SCENARIO_ID) CNT FROM DUAL);

-- variables used in timing the processing of a scenario
V_START_TIMESTAMP    TIMESTAMP;
      V_END_TIMESTAMP    TIMESTAMP;
      V_PROCESS_DURATION   INTERVAL DAY (2) TO SECOND (4);

      -- Backup of table P_SCENARIO_COUNTS
      V_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB;
BEGIN
      v_err_loc := 1;
      -- SAFELY Backup the P_SCENARIO_COUNTS table
BEGIN
SELECT * BULK COLLECT INTO V_SCENARIO_COUNTS_TAB_BKP
FROM TABLE (
        PKG_ALERT.FN_GET_P_SCENARIO_COUNT_ROWS (
                PKG_ALERT.FN_GET_CUR ('SELECT * FROM P_SCENARIO_COUNTS')
        )
     );
EXCEPTION
        WHEN OTHERS THEN
          V_SCENARIO_COUNTS_TAB_BKP := NULL;
END;

      /* LOOP ON QUERIES */
      v_err_loc := 10;
FOR R_QUERY_TO_RUN IN C_QUERY_TO_RUN
      LOOP
        --BEGIN

         v_err_loc := '20';
         V_START_TIMESTAMP := SYSTIMESTAMP;
         vSCENARIO_ID := R_QUERY_TO_RUN.SCENARIO_ID;

         -- Delete any existing count records to zero for this scenario
         -- do not commit.  This ensure totals are zeroed if the query return no
         -- data for a entity/ccy...
         DELETE P_SCENARIO_COUNTS
          WHERE SCENARIO_ID = R_QUERY_TO_RUN.SCENARIO_ID;
         v_err_loc := '30';
         -- Determine how query result are to be handled for security
         -- calculation of totals for strage on temp table
CASE
            --->  Query is threshold enable AND returns HOST ENTITY CCY ...
            -- counts for SCENARIO_COUNT and SCENARIO_COUNT_OVER_T
            --will be grouped and stored by host/entity/Currency
            WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.AMT_THRESHOLD_COL IS NOT NULL)
            THEN
BEGIN
                   v_err_loc := '40';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                      v_err_loc := '41.1';
                            sp_error_log ('',
                                          'SYSTEM',
                                          'DBSERVER',
                                          'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                          || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                          || ' at Location: ' || v_err_loc,
                                          SQLCODE,
                                          'Scenario query text is missing'
                                        );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY_CCY_THRESH (
                                R_QUERY_TO_RUN.SCENARIO_ID,
                                TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL),
                                TRIM (R_QUERY_TO_RUN.SEC_CURRENCY_COL),
                                R_QUERY_TO_RUN.AMT_THRESHOLD_COL)
                       LOOP
                         v_err_loc := '41';
                         INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
            --->  Query returns HOST ENTITY CCY ...
            -- counts will be grouped and stored by host/entity/Currency
WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NOT NULL)
            THEN
BEGIN
                   v_err_loc := '42';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '42.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY_CCY (R_QUERY_TO_RUN.SCENARIO_ID,
                                                   TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                                   TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL),
                                                   TRIM (R_QUERY_TO_RUN.SEC_CURRENCY_COL))
                       LOOP
                            v_err_loc := '42.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc||CHR(10)
                                            || 'SCENARIO_ID='||R_QUERY_TO_RUN.SCENARIO_ID||CHR(10)
                                            || 'SEC_HOST_COL='||R_QUERY_TO_RUN.SEC_HOST_COL||CHR(10)
                                            || 'SEC_ENTITY_COL='||R_QUERY_TO_RUN.SEC_ENTITY_COL||CHR(10)
                                            || 'SEC_CURRENCY_COL='||R_QUERY_TO_RUN.SEC_CURRENCY_COL||CHR(10)
                                            ,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
            --->  Query supports HOST ENTITY  ...
            -- counts will be grouped and stored by host/entity/[Currency='All']
WHEN (R_QUERY_TO_RUN.SEC_HOST_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_ENTITY_COL IS NOT NULL
              AND R_QUERY_TO_RUN.SEC_CURRENCY_COL IS NULL)
            THEN
BEGIN
                   v_err_loc := '43';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '43.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT
                          IN C_GET_COUNTS_ETY (R_QUERY_TO_RUN.SCENARIO_ID,
                                               TRIM (R_QUERY_TO_RUN.SEC_HOST_COL),
                                               TRIM (R_QUERY_TO_RUN.SEC_ENTITY_COL))
                       LOOP
                            v_err_loc := '43.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
ELSE
BEGIN
                   --->  Query does not return entity or ccy ...
                   -- counts will be stored by host/[entity=All]/[Currency=All]
                   v_err_loc := '44';
                   IF R_QUERY_TO_RUN.QUERY_TEXT IS NULL THEN
                        v_err_loc := '44.1';
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            'Scenario query text is missing'
                                          );
ELSE
                       FOR R_COUNT IN C_GET_COUNT_ONLY (R_QUERY_TO_RUN.SCENARIO_ID)
                       LOOP

                            v_err_loc := '44.2';
                            INS_UPD_P_SCENARIO_COUNTS (R_COUNT, V_SCENARIO_COUNTS_TAB_BKP);
END LOOP;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                              sp_error_log ('',
                                            'SYSTEM',
                                            'DBSERVER',
                                            'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                                            || ' Error for ' || R_QUERY_TO_RUN.SCENARIO_ID
                                            || ' at Location: ' || v_err_loc,
                                            SQLCODE,
                                            SQLERRM
                                          );
END;
END CASE;

         -- calculate run duration for this scenario
         V_END_TIMESTAMP := SYSTIMESTAMP;
         V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;

         v_err_loc := '60';
         -- Update scenario record with last RUN time = now AND DURATION OF RUN
UPDATE P_SCENARIO_SYSTEM
SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
WHERE SCENARIO_ID = R_QUERY_TO_RUN.SCENARIO_ID;

v_err_loc := '70';
          IF SQL%ROWCOUNT = 0 THEN
            v_err_loc := '80';
INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
SELECT R_QUERY_TO_RUN.SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
FROM DUAL
WHERE NOT EXISTS (SELECT NULL
                  FROM P_SCENARIO_SYSTEM
                  WHERE SCENARIO_ID =  R_QUERY_TO_RUN.SCENARIO_ID);
END IF;

         v_err_loc := '90';
COMMIT;
/*EXCEPTION
       WHEN OTHERS
       THEN
          sp_error_log ('',
                        'SYSTEM',
                        'DBSERVER',
                        'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                        || ' Error for ' || vSCENARIO_ID
                        || ' at Location: ' || v_err_loc,
                        SQLCODE,
                        SQLERRM
                      );
END;*/
END LOOP;
   --< end of query loop
EXCEPTION
   WHEN OTHERS
   THEN
      sp_error_log ('',
                    'SYSTEM',
                    'DBSERVER',
                    'PKG_ALERT.PROC_POPULATE_SCENARIO_COUNTS ->'
                    || ' Error for ' || vSCENARIO_ID
                    || ' at Location: ' || v_err_loc,
                    SQLCODE,
                    SQLERRM
                  );
END;

   PROCEDURE INS_UPD_P_SCENARIO_COUNTS (P_SCENARIO_COUNT_REC P_SCENARIO_COUNTS%ROWTYPE, P_SCENARIO_COUNTS_TAB_BKP T_P_SCENARIO_COUNTS_TAB DEFAULT NULL)
IS
    V_BCKP_ROW P_SCENARIO_COUNTS%ROWTYPE;
    V_FOUND BOOLEAN := FALSE;
BEGIN
BEGIN
INSERT INTO P_SCENARIO_COUNTS (SCENARIO_ID,
                               HOST_ID,
                               ENTITY_ID,
                               CURRENCY_CODE,
                               SCENARIO_COUNT,
                               SCENARIO_COUNT_OVER_T)
VALUES (P_SCENARIO_COUNT_REC.SCENARIO_ID,
        P_SCENARIO_COUNT_REC.HOST_ID,
        P_SCENARIO_COUNT_REC.ENTITY_ID,
        P_SCENARIO_COUNT_REC.CURRENCY_CODE,
        P_SCENARIO_COUNT_REC.SCENARIO_COUNT,
        P_SCENARIO_COUNT_REC.SCENARIO_COUNT_OVER_T);
EXCEPTION
      WHEN DUP_VAL_ON_INDEX
      THEN
UPDATE P_SCENARIO_COUNTS
SET SCENARIO_COUNT = P_SCENARIO_COUNT_REC.SCENARIO_COUNT,
    SCENARIO_COUNT_OVER_T = P_SCENARIO_COUNT_REC.SCENARIO_COUNT_OVER_T
WHERE SCENARIO_ID = P_SCENARIO_COUNT_REC.SCENARIO_ID
  AND HOST_ID = P_SCENARIO_COUNT_REC.HOST_ID
  AND ENTITY_ID = P_SCENARIO_COUNT_REC.ENTITY_ID
  AND CURRENCY_CODE = P_SCENARIO_COUNT_REC.CURRENCY_CODE;
WHEN OTHERS
       THEN
          sp_error_log ('',
                        'SYSTEM',
                        'DBSERVER',
                        'PKG_ALERT.INS_UPD_P_SCENARIO_COUNTS',
                        SQLCODE,
                        SQLERRM
                      );
END;

       -- SAFELY Update the email-flag to 'N' if actual scenario count increased in percentage EMAIL_PCT_DIFF compared to EMAILED_SC_COUNT
BEGIN
           IF P_SCENARIO_COUNTS_TAB_BKP IS NOT NULL THEN
                -- Get the backed up row from backed up table. If not already backed up, then assign current row
BEGIN
FOR i IN 1 .. P_SCENARIO_COUNTS_TAB_BKP.COUNT LOOP
                        IF P_SCENARIO_COUNTS_TAB_BKP(i).SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).HOST_ID=P_SCENARIO_COUNT_REC.HOST_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).ENTITY_ID=P_SCENARIO_COUNT_REC.ENTITY_ID
                            AND P_SCENARIO_COUNTS_TAB_BKP(i).CURRENCY_CODE=P_SCENARIO_COUNT_REC.CURRENCY_CODE
                        THEN
                            V_BCKP_ROW := P_SCENARIO_COUNTS_TAB_BKP(i);
                            V_FOUND := TRUE;
END IF;
                        EXIT WHEN V_FOUND;
END LOOP;

                     IF NOT V_FOUND THEN
                        V_BCKP_ROW := P_SCENARIO_COUNT_REC;
END IF;
EXCEPTION
                    WHEN OTHERS THEN
                        V_BCKP_ROW := P_SCENARIO_COUNT_REC;
END;

                -- Update V_BCKP_ROW.EMAIL_FLAG according to the business
SELECT CASE WHEN ABS(NVL(P_SCENARIO_COUNT_REC.SCENARIO_COUNT,0)-NVL(V_BCKP_ROW.EMAILED_SC_COUNT,0))*100/DECODE(V_BCKP_ROW.EMAILED_SC_COUNT,NULL, 1, 0 ,1, V_BCKP_ROW.EMAILED_SC_COUNT)>=NVL(S.EMAIL_PCT_DIFF,0)
                THEN 'N' -- New
            ELSE V_BCKP_ROW.EMAIL_FLAG
           END INTO V_BCKP_ROW.EMAIL_FLAG
FROM P_SCENARIO S
WHERE S.SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID;

-- Update EMAIL columns
UPDATE P_SCENARIO_COUNTS SET EMAIL_FLAG=V_BCKP_ROW.EMAIL_FLAG,
                             EMAILED_SC_COUNT=V_BCKP_ROW.EMAILED_SC_COUNT,
                             EMAILED_SC_COUNT_OVER_T=V_BCKP_ROW.EMAILED_SC_COUNT_OVER_T
WHERE SCENARIO_ID=P_SCENARIO_COUNT_REC.SCENARIO_ID
  AND HOST_ID=P_SCENARIO_COUNT_REC.HOST_ID
  AND ENTITY_ID=P_SCENARIO_COUNT_REC.ENTITY_ID
  AND CURRENCY_CODE=P_SCENARIO_COUNT_REC.CURRENCY_CODE;
END IF;
EXCEPTION
           WHEN OTHERS
           THEN
            sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.INS_UPD_P_SCENARIO_COUNTS: Error occurred when setting the email flags',
                            SQLCODE,
                            SQLERRM
        );
END;
END;

   /* Accept a timestamp interval and convert it to number of seconds */

   FUNCTION FN_CONV_INTERVAL_TO_SECS (P_IN_INTERVAL INTERVAL DAY TO SECOND)
      RETURN NUMBER
   IS
      V_DURATION_DAYS     NUMBER;
      V_DURATION_HOURS    NUMBER;
      V_DURATION_MINS     NUMBER;
      V_DURATION_SECS     NUMBER;
      V_NUMBER_SECS_OUT   VARCHAR2 (50);
BEGIN
      V_DURATION_DAYS := NVL (EXTRACT (DAY FROM P_IN_INTERVAL), 0);
      V_DURATION_HOURS := NVL (EXTRACT (HOUR FROM P_IN_INTERVAL), 0);
      V_DURATION_MINS := NVL (EXTRACT (MINUTE FROM P_IN_INTERVAL), 0);
      V_DURATION_SECS := NVL (EXTRACT (SECOND FROM P_IN_INTERVAL), 0);
      V_NUMBER_SECS_OUT :=
           V_DURATION_DAYS * 86400
         + V_DURATION_HOURS * 3600
         + V_DURATION_MINS * 60
         + V_DURATION_SECS;
RETURN V_NUMBER_SECS_OUT;
END;

   /*  Accept number of seconds and format it like '1d 15h 46m 39.4832s' */

   FUNCTION FN_CONV_SECS_TO_DUR_STRING (P_IN_SECS NUMBER)
      RETURN VARCHAR2
   IS
      V_DURATION_DAYS    NUMBER;
      V_DURATION_HOURS   NUMBER;
      V_DURATION_MINS    NUMBER;
      V_DURATION_SECS    NUMBER;
      V_TEXT_OUT         VARCHAR2 (50);
BEGIN
      V_DURATION_DAYS := FLOOR (P_IN_SECS / 86400);
      V_DURATION_HOURS := FLOOR (MOD (P_IN_SECS, 86400) / 3600);
      V_DURATION_MINS := FLOOR (MOD (P_IN_SECS, 3600) / 60);
      V_DURATION_SECS := MOD (P_IN_SECS, 60);

      V_TEXT_OUT :=
            TO_CHAR (V_DURATION_DAYS, '90')
         || 'd '
         || TO_CHAR (V_DURATION_HOURS, 'FM90')
         || 'h '
         || TO_CHAR (V_DURATION_MINS, 'FM90')
         || 'm '
         || TO_CHAR (V_DURATION_SECS, 'FM90.0000')
         || 's';
RETURN V_TEXT_OUT;
END;

   /* RETURN 'Y' IF SUPPLIED TIME IS BETWEEN START AND END TIME*/
   /* IF ANY OF THE PARAMETERS IS NULL RETURN 'Y' ANYWAY*/

   FUNCTION FN_TEST_START_END_TIME (P_TEST_DATETIME    DATE,
                                    P_START_TIME       VARCHAR2,
                                    P_END_TIME         VARCHAR2)
      RETURN VARCHAR
   IS
      V_RETURN_VAL   VARCHAR2 (1) := 'Y';
      V_START_TIME   VARCHAR2 (4);
      V_END_TIME     VARCHAR2 (4);
      V_TEST_TIME    VARCHAR2 (4);
BEGIN
      IF P_TEST_DATETIME IS NOT NULL
     AND P_START_TIME IS NOT NULL
     AND P_END_TIME IS NOT NULL
      THEN
         /* Strip colons out of times */
         V_START_TIME := REPLACE (P_START_TIME, ':', '');
         V_END_TIME := REPLACE (P_END_TIME, ':', '');

         /* get time component of test date time supplied */
         V_TEST_TIME := TO_CHAR (P_TEST_DATETIME, 'HH24MI');

         /*DO TESTS*/
         /* normal case e.g. start:0900  end 1700...*/
         IF V_START_TIME < V_END_TIME
        AND (V_TEST_TIME >= V_START_TIME
         AND V_TEST_TIME < V_END_TIME)
         THEN
            V_RETURN_VAL := 'Y';
         /* reverse case e.g. start:2300 end:0100....*/
         ELSIF V_START_TIME >= V_END_TIME
           AND (V_TEST_TIME >= V_START_TIME
             OR V_TEST_TIME < V_END_TIME)
         THEN
            V_RETURN_VAL := 'Y';
ELSE
            /* times not satisfied - return N */
            V_RETURN_VAL := 'N';
END IF;
END IF;

RETURN V_RETURN_VAL;
END;

   -- Accept a query text, a filter, an order and the two bounds and get the refcursor and the count of rows
   PROCEDURE PRC_EXEC_QUERY (p_QUERY_TEXT           VARCHAR2,
                             p_FILTER               VARCHAR2,
                             p_ORDER                VARCHAR2,
                             p_ROW_BEGIN            NUMBER,
                             p_ROW_END              NUMBER,
                             p_ASC_DESC             VARCHAR2,
                             p_CUR_RES       IN OUT SYS_REFCURSOR,
                             p_Count_Rows      OUT NUMBER,
                             p_Query_String    OUT VARCHAR2)
IS
       vFILTER   VARCHAR2 (32000) := '';
       vSQL      CLOB;
       curRefCursor     sys_refcursor;
       CurRefCursorFilter sys_refcursor;
       vDateFormat      VARCHAR2(30);
       VINDVAL          TINDVAL;
       VINDVALUE        VARCHAR2(4000);
       VCOLIDX          NUMBER(5);
       VCOLI            TCOLI;
BEGIN
       -- get date format from s_system_parameters
       vDateFormat := pk_utility.fn_get_date_format('Y');
       -- transform the filter given from java side to a WHERE clause to build the query
       PKG_ALERT.spSplitter(p_FILTER, '|', CurRefCursorFilter);
       LOOP
FETCH CurRefCursorFilter INTO VINDVALUE, VCOLIDX;
            EXIT WHEN CurRefCursorFilter%NOTFOUND;

            IF VINDVALUE != 'All' THEN
                PKG_ALERT.spSplitter(VINDVALUE, '$#$', CurRefCursor);
FETCH CurRefCursor BULK COLLECT INTO VINDVAL, VCOLI;
IF vFILTER IS NOT NULL THEN
                    vFILTER := vFILTER || ' AND ';
END IF;
                IF (LOWER(VINDVAL(3)) = 'date') THEN
                    vFILTER := vFILTER || 'TO_DATE(TO_CHAR(SCENQUERY.' || VINDVAL(1) || ',''' || vDateFormat ||''')' || ','''|| vDateFormat ||''')' || '= TO_DATE(''' || VINDVAL(2) || ''','''|| vDateFormat ||''')';
ELSE
                    vFILTER := vFILTER || ' SCENQUERY.' || VINDVAL(1) || CASE   WHEN VINDVAL(2)='(EMPTY)' THEN ' IS NULL'
                                                                                WHEN VINDVAL(2)='(NOT EMPTY)' THEN ' IS NOT NULL'
                                                                                ELSE '=''' || VINDVAL(2) || ''''
END;
END IF;
END IF;
END LOOP;

       -- Build the query text
SELECT 'SELECT *
             FROM (SELECT ROW_NUMBER () OVER (ORDER BY '
           || DECODE (p_ORDER, '', '1', 'SCENQUERY.' || p_ORDER || ' ' || p_ASC_DESC)
           || ') ROW_,SCENQUERY.* FROM ('
           || CHR(10)
           || p_QUERY_TEXT
           || CHR(10)
           || ') SCENQUERY'
           || DECODE (vFILTER, '', '', ' WHERE ' || vFILTER)
           || DECODE (p_ROW_BEGIN || p_ROW_END,'00', ')',') WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ' || p_ROW_END)
INTO vSQL
FROM DUAL;

OPEN p_cur_res FOR CAST (vSQL AS VARCHAR2);
p_Query_String := SUBSTR(CAST (vSQL AS VARCHAR2), 1, INSTR(vSQL, ' WHERE ROW_ BETWEEN ') - 1) || ' WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ';

       -- get also the count of rows for the query text after applying conditions
EXECUTE IMMEDIATE 'SELECT COUNT(*) FROM ('
    || CHR(10)
    || p_QUERY_TEXT
    || CHR(10)
    || ') SCENQUERY' || CASE WHEN vFILTER IS NULL THEN '' ELSE ' WHERE ' || vFILTER END
    INTO p_Count_Rows;

EXCEPTION
           WHEN OTHERS
           THEN
              sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.PRC_EXEC_QUERY',
                            SQLCODE,
                            SQLERRM
                          );
END;


   PROCEDURE PROC_SCENARIO_COUNT_BY_ENT_CUR (p_SCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE,
                                             p_ROLE_ID S_ROLE.ROLE_ID%TYPE,
                                             p_THRESHOLD IN CHAR,
                                             p_GroupBy VARCHAR,
                                             p_EntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                             p_SortColumn IN VARCHAR2 DEFAULT NULL,
                                             p_SortDirection IN VARCHAR2 DEFAULT NULL,
                                             p_CcyGroup   IN VARCHAR2,
                                             p_IsAlertable   IN VARCHAR2,
                                             pFilter IN VARCHAR2,
                                             p_CurScenarioMaster IN OUT sys_refcursor,
                                             p_CurScenarioSlave IN OUT sys_refcursor) IS
    vCurScenarioMaster VARCHAR2(4000);
    vCurScenarioSlave VARCHAR2(4000);
    curRefCursor        SYS_REFCURSOR;
    vv_filter_slave     VARCHAR2(4000);
    vv_filter_master1   VARCHAR2(4000);
    vv_filter_master2   VARCHAR2(4000);
    vIndValue           VARCHAR2(4000);
    vv_sql              VARCHAR2(4000);
    vColIdx             PLS_INTEGER;
    vv_param_thresh     VARCHAR2(1);
BEGIN
        -- split filter
        pk_utility.spSplitter(pFilter, '|', curRefCursor);
        LOOP
FETCH curRefCursor INTO vIndValue, vColIdx;

            EXIT WHEN curRefCursor%NOTFOUND;

            IF vColIdx = 1 AND vIndValue != 'All' THEN
                vv_filter_slave := vv_filter_slave || ' AND ENTITY_ID = ''' || vIndValue || '''';
                vv_filter_master1 := vv_filter_master1 || ' AND ENTITY_ID = ''' || vIndValue || '''';
            ELSIF vColIdx = 2 AND vIndValue != 'All' THEN
                vv_filter_slave := vv_filter_slave || ' AND CURRENCY_CODE = ''' || vIndValue || '''';
                vv_filter_master1 := vv_filter_master1 || ' AND CURRENCY_CODE = ''' || vIndValue || '''';
            ELSIF vColIdx = 3 AND vIndValue != 'All' THEN
                vv_param_thresh := 'Y';
                --vv_filter_master2 := ' AND SUM(C.SCENARIO_COUNT) = ' || vIndValue;
                vv_filter_slave := vv_filter_slave || ' AND CASE WHEN :pTHRESHOLD = ''Y'' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END = ' || vIndValue;
                vv_filter_master1 := vv_filter_slave;
END IF;

END LOOP;

        IF p_GroupBy IN ('E','C') THEN
            vCurScenarioMaster := q'[SELECT ENTITY_ID,'All' CURRENCY_CODE, SUM(CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) SCENARIO_COUNT
              FROM P_SCENARIO_COUNTS C, P_SCENARIO S
             WHERE C.SCENARIO_ID = :pSCENARIO_ID
               AND C.SCENARIO_ID = S.SCENARIO_ID
               AND (:pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
               AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                       :pROLE_ID,
                                                       C.ENTITY_ID,
                                                       C.CURRENCY_CODE) < 2
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                             C.HOST_ID,
                                                             :pROLE_ID,
                                                             C.ENTITY_ID),1,1)='Y'
               AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE (:pCcyGroup IN ('All', CURRENCY_GROUP_ID) OR CURRENCY_GROUP_ID = 'All') UNION SELECT :pCcyGroup FROM DUAL))
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                            C.HOST_ID,
                                                            :pROLE_ID,
                                                            C.ENTITY_ID),2,3) LIKE DECODE(:pIsAlertable,'Y','%Y%','NNN')]';
            vCurScenarioMaster := vCurScenarioMaster || vv_filter_master1;
            vCurScenarioMaster := vCurScenarioMaster || q'[
            GROUP BY C.ENTITY_ID
            HAVING SUM(CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0 ]' || vv_filter_master2;
SELECT vCurScenarioMaster || DECODE(p_SortColumn, '-1', ' ORDER BY C.ENTITY_ID ',
                                    '2', ' ORDER BY C.ENTITY_ID,CURRENCY_CODE ' || p_SortDirection,
                                    '3', ' ORDER BY C.ENTITY_ID, SUM(DECODE(:pTHRESHOLD, ''Y'', C.SCENARIO_COUNT_OVER_T, C.SCENARIO_COUNT)) ' || p_SortDirection)
INTO vCurScenarioMaster
FROM DUAL;

IF p_SortColumn = '3' THEN
                IF vv_param_thresh = 'Y' THEN
                    OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD, p_THRESHOLD;
ELSE
                    OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
END IF;
ELSE
                IF vv_param_thresh = 'Y' THEN
                OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
ELSE
                OPEN p_CurScenarioMaster FOR vCurScenarioMaster
                        USING p_THRESHOLD, p_SCENARIO_ID, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
END IF;
END IF;

           vCurScenarioSlave := q'[SELECT ENTITY_ID, CURRENCY_CODE, CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END
          FROM P_SCENARIO_COUNTS C, P_SCENARIO S
         WHERE C.SCENARIO_ID = :pSCENARIO_ID
           AND C.SCENARIO_ID = S.SCENARIO_ID
           AND CASE WHEN :pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END != 0
           AND (:pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
           AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                   :pROLE_ID,
                                                   C.ENTITY_ID,
                                                   C.CURRENCY_CODE) < 2
           AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                      C.HOST_ID,
                                                      :pROLE_ID,
                                                      C.ENTITY_ID),
                                                      1,
                                                      1)='Y'
           AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE (:pCcyGroup IN ('All', CURRENCY_GROUP_ID) OR CURRENCY_GROUP_ID = 'All') UNION SELECT :pCcyGroup FROM DUAL))
           AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                        C.HOST_ID,
                                                        :pROLE_ID,
                                                        C.ENTITY_ID),2,3) LIKE DECODE(:pIsAlertable,'Y','%Y%','NNN')]';
            vCurScenarioSlave := vCurScenarioSlave || vv_filter_slave;
SELECT vCurScenarioSlave || DECODE(p_SortColumn, '-1', ' ORDER BY ENTITY_ID, CURRENCY_CODE ',
                                   '2', ' ORDER BY ENTITY_ID,CURRENCY_CODE ' || p_SortDirection,
                                   '3', ' ORDER BY ENTITY_ID, DECODE(:pTHRESHOLD, ''Y'', C.SCENARIO_COUNT_OVER_T, C.SCENARIO_COUNT) ' || p_SortDirection)
INTO vCurScenarioSlave
FROM DUAL;
IF p_SortColumn = '3' THEN
                IF vv_param_thresh = 'Y' THEN
                    OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                            USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD, p_THRESHOLD;
ELSE
                    OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                            USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
END IF;
ELSE
                IF vv_param_thresh = 'Y' THEN
                OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                        USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable, p_THRESHOLD;
ELSE
                OPEN p_CurScenarioSlave FOR vCurScenarioSlave
                        USING p_THRESHOLD, p_SCENARIO_ID, p_THRESHOLD, p_EntityID, p_ROLE_ID, p_ROLE_ID, p_CcyGroup, p_CcyGroup, p_ROLE_ID, p_IsAlertable;
END IF;
END IF;
        ELSIF p_GroupBy = 'N' THEN
            --OPEN p_CurScenarioMaster FOR
            vv_sql := q'[
            SELECT 'All' ENTITY_ID, 'All' CURRENCY_CODE, SUM(CASE WHEN :p_THRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) SCENARIO_COUNT
              FROM P_SCENARIO_COUNTS C, P_SCENARIO S
             WHERE C.SCENARIO_ID = :p_SCENARIO_ID
               AND C.SCENARIO_ID = S.SCENARIO_ID
               AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                       :p_ROLE_ID,
                                                       C.ENTITY_ID,
                                                       C.CURRENCY_CODE) < 2
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                          C.HOST_ID,
                                                          :p_ROLE_ID,
                                                          C.ENTITY_ID),1,1)='Y'
               AND (:p_EntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All') ]' || vv_filter_master1 || q'[
               HAVING SUM(CASE WHEN :p_THRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0
               ]';

OPEN p_CurScenarioMaster FOR vv_sql USING p_THRESHOLD,p_SCENARIO_ID, p_ROLE_ID, p_ROLE_ID, p_EntityID, p_THRESHOLD;
END IF;

        IF curRefCursor%ISOPEN
        THEN
           CLOSE curRefCursor;
END IF;
EXCEPTION
          WHEN OTHERS
          THEN
             IF curRefCursor%ISOPEN
             THEN
                CLOSE curRefCursor;
END IF;
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_SCENARIO_COUNT_BY_ENT_CUR:'||DBMS_UTILITY.format_error_backtrace,
                           SQLCODE,
                           SQLERRM
                          );
END;


   -- Return the counts for each category/scenario for which the user's role has access
   PROCEDURE PROC_POPULATE_SCENARIO_CATEG (pRoleID IN S_ROLE.ROLE_ID%TYPE,
                                           pEntityID IN S_ENTITY.ENTITY_ID%TYPE,
                                           pTHRESHOLD IN VARCHAR,
                                           pAlertableOnly IN VARCHAR,
                                           pCcyGroup IN VARCHAR2,
                                           pCallOption IN VARCHAR2,
                                           pSelectedTab IN P_CATEGORY.DISPLAY_TAB%TYPE,
                                           CurScenCountByCateg IN OUT SYS_REFCURSOR,
                                           vCountAllCateg OUT NUMBER) IS
BEGIN
OPEN CurScenCountByCateg FOR
SELECT TITLE_CAT, DISP_ORDER_CAT, DESC_CAT, SCENARIO_ID, DISP_ORDER_SCEN, TITLE_SCEN, SUM(CNT) AS CNT, TIME_TO_CUT_OFF, IsScenarioAlertable, DESC_SCEN
FROM (SELECT NVL (CAT.TITLE, 'No Category') TITLE_CAT,
             CAT.DISPLAY_ORDER DISP_ORDER_CAT,
             CAT.DESCRIPTION DESC_CAT,
             C.SCENARIO_ID,
             S.DISPLAY_ORDER DISP_ORDER_SCEN,
             S.TITLE TITLE_SCEN,
             SUM(CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) CNT,
             PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
             (
                     CASE WHEN
                              (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,S.START_TIME, S.END_TIME)='Y')
                                  AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                                  AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(S.END_TIME,'HH24:MI')
                              THEN
                              ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(S.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                          ELSE
                              GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(S.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END
             ) AS TIME_TO_CUT_OFF,
             CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3) LIKE '%Y%' THEN 'Y'
                  ELSE 'N' END AS IsScenarioAlertable,
             S.DESCRIPTION DESC_SCEN
      FROM P_SCENARIO_COUNTS C
               INNER JOIN P_SCENARIO S
                          ON (S.SCENARIO_ID = C.SCENARIO_ID)
               LEFT OUTER JOIN P_CATEGORY CAT
                               ON (CAT.CATEGORY_ID = S.CATEGORY_ID)
      WHERE     S.ACTIVE_FLAG = 'Y'
        AND (pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
        AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                                pRoleID,
                                                C.ENTITY_ID,
                                                C.CURRENCY_CODE) < 2
        AND SUBSTR (PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                      C.HOST_ID,
                                                      pRoleID,
                                                      C.ENTITY_ID), 1, 1) = 'Y'
        AND DECODE(pAlertableOnly, 'Y', SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3),'Y') LIKE '%Y%'
        AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE pCcyGroup IN (CURRENCY_GROUP_ID, 'All') UNION SELECT pCcyGroup FROM DUAL))
        AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID) LIKE 'Y' || pCallOption || '_'
        AND CAT.DISPLAY_TAB = nvl(pSelectedTab, CAT.DISPLAY_TAB)
      GROUP BY CAT.TITLE,
               CAT.DISPLAY_ORDER,
               CAT.DISPLAY_TAB,
               CAT.DESCRIPTION,
               C.SCENARIO_ID,
               S.DISPLAY_ORDER,
               S.TITLE,
               S.START_TIME,
               S.END_TIME,
               C.HOST_ID,
               C.ENTITY_ID,
               S.DESCRIPTION
      HAVING SUM(CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END) > 0
      ORDER BY CAT.DISPLAY_ORDER ASC NULLS LAST,
               CAT.TITLE,
               S.DISPLAY_ORDER ASC NULLS LAST,
               S.TITLE,
               PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID))
GROUP BY TITLE_CAT, DISP_ORDER_CAT, DESC_CAT, SCENARIO_ID, DISP_ORDER_SCEN, TITLE_SCEN, TIME_TO_CUT_OFF, IsScenarioAlertable, DESC_SCEN
ORDER BY DISP_ORDER_CAT ASC NULLS LAST,
         TITLE_CAT,
         DISP_ORDER_SCEN ASC NULLS LAST,
         TITLE_SCEN;

-- Return also the sum of counts for all category
SELECT SUM(CASE WHEN pTHRESHOLD = 'Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END)
INTO vCountAllCateg
FROM P_SCENARIO_COUNTS C
         INNER JOIN P_SCENARIO S
                    ON (S.SCENARIO_ID = C.SCENARIO_ID)
         LEFT OUTER JOIN P_CATEGORY CAT
                         ON (CAT.CATEGORY_ID = S.CATEGORY_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND PK_APPLICATION.FNGETCURRENCYACCESS (C.HOST_ID,
                                          pRoleID,
                                          C.ENTITY_ID,
                                          C.CURRENCY_CODE) < 2
  AND SUBSTR (PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,
                                                C.HOST_ID,
                                                pRoleID,
                                                C.ENTITY_ID), 1, 1) = 'Y'
  AND DECODE(pAlertableOnly, 'Y', SUBSTR(PKG_ALERT.fn_get_scenario_access(C.SCENARIO_ID, C.HOST_ID, pRoleID, C.ENTITY_ID),2,3),'Y') LIKE '%Y%'
  AND CASE WHEN pTHRESHOLD='Y' AND S.AMT_THRESHOLD_COL IS NOT NULL THEN C.SCENARIO_COUNT_OVER_T ELSE C.SCENARIO_COUNT END > 0
  AND (C.CURRENCY_CODE = 'All' OR C.CURRENCY_CODE IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE pCcyGroup IN ('All', CURRENCY_GROUP_ID) UNION SELECT pCcyGroup FROM DUAL))
  AND (pEntityID IN ('All', C.ENTITY_ID) OR C.ENTITY_ID = 'All')
  AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (C.SCENARIO_ID,C.HOST_ID,pRoleID,C.ENTITY_ID) LIKE 'Y' || pCallOption || '_'
  AND CAT.DISPLAY_TAB = nvl(pSelectedTab, CAT.DISPLAY_TAB);
EXCEPTION
          WHEN OTHERS
          THEN
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_POPULATE_SCENARIO_CATEG',
                           SQLCODE,
                           SQLERRM
                          );
END;

   -- Return last run date and duration execution for a given scenario
   FUNCTION fn_get_last_run_duration (pSCENARIO_ID IN P_SCENARIO.SCENARIO_ID%TYPE) RETURN VARCHAR2
   IS
        vLastRunDuration VARCHAR2(200);
BEGIN
SELECT TO_CHAR(LAST_RUN_DATE,'DD/MM/YYYY HH24:MI:SS') || ', taking ' ||
       CASE WHEN LAST_RUN_DURATION_SECS > 0 AND LAST_RUN_DURATION_SECS < 1 THEN '0' ELSE '' END || LAST_RUN_DURATION_SECS || ' seconds'
INTO vLastRunDuration
FROM P_SCENARIO_SYSTEM
WHERE SCENARIO_ID = pSCENARIO_ID;
RETURN vLastRunDuration;
EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN '';
END;

   -- This procedure defines whether any scenario is accessible to the user having non zero count
   -- and returns the count of Flash/Popup and Email flagged scenarios: Querying table P_SCENARIO_COUNTS in conjunction with P_NOTIFY_SCENARIO
   -- Added by Saber Chebka, SwallowTech Tunisia
   PROCEDURE PROC_ALERTABLE_SCENARIO_COUNT(
                                   pRoleID IN S_ROLE.ROLE_ID%TYPE,
                                   pTHRESHOLD IN VARCHAR,
                                   vCountPopUp OUT NUMBER,
                                   vCountFlash OUT NUMBER,
                                   vCountEmail OUT NUMBER) IS
BEGIN
WITH cnts
         AS (  SELECT c.scenario_id,
                      c.host_id,
                      c.entity_id,
                      NVL (SUM (DECODE (pthreshold, 'Y', c.scenario_count_over_t, c.scenario_count)), 0) AS cnt
               --Count over threshold or not
               FROM p_scenario_counts c
                        INNER JOIN p_scenario s ON (s.scenario_id = c.scenario_id)
               WHERE     s.active_flag = 'Y'
                 AND pk_application.fngetcurrencyaccess (c.host_id, pRoleId, c.entity_id, c.currency_code) < 2 --Having access to currency
                 AND pkg_alert.fn_test_start_end_time (GLOBAL_VAR.SYS_DATE, s.start_time, s.end_time) = 'Y'
               GROUP BY c.scenario_id, c.host_id, c.entity_id)
SELECT SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'YY___'
                    THEN NVL (cnt, 0)
                ELSE 0
    END) vcountpopup,
       SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'Y_Y__'
                    THEN NVL (cnt, 0)
                ELSE 0
           END) vcountflash,
       SUM (CASE
                WHEN pkg_alert.fn_get_scenario_access (c.scenario_id, c.host_id, pRoleId, c.entity_id) LIKE 'Y__Y_'
                    THEN NVL (cnt, 0)
                ELSE 0
           END) vcountemail
INTO vcountpopup, vcountflash, vcountemail
FROM cnts c;
EXCEPTION
          WHEN OTHERS
          THEN
             sp_error_log ('',
                           'SYSTEM',
                           'DBSERVER',
                           'PKG_ALERT.PROC_ALERTABLE_SCENARIO_COUNT',
                           SQLCODE,
                           SQLERRM
                          );
END;

   -- Return the facility access for a given role
   FUNCTION fn_Get_Facility_Access(p_host_id in VARCHAR2,
                                 p_role_id in VARCHAR2,
                                 p_entity_id in VARCHAR2,
                                 p_currency_code in VARCHAR2,
                                 p_facility_id in VARCHAR2) return VARCHAR2
   IS
      vAccessFacility P_MENU_ACCESS.ACCESS_ID%TYPE;
BEGIN
SELECT GREATEST(P.ACCESS_ID, PK_APPLICATION.fnGetCurrencyAccess(p_host_id, p_role_id, p_entity_id, p_currency_code))
INTO vAccessFacility
FROM P_MENU_ACCESS P, P_FACILITY F
WHERE P.ROLE_ID = p_role_id
  AND P.MENU_ITEM_ID = F.SEC_MENU_ITEM_ID
  AND F.FACILITY_ID = p_facility_id;

RETURN vAccessFacility;
EXCEPTION
     WHEN NO_DATA_FOUND THEN
        RETURN '2';
END;

   -- return the match list for a given scenario
   PROCEDURE proc_get_list_match (p_scenario_id      in VARCHAR2,
                                  p_entity_id        in VARCHAR2,
                                  p_currency_code    in VARCHAR2,
                                  p_threshold        in VARCHAR2,
                                  p_CcyGroup         in VARCHAR2,
                                  p_ref              in out sys_refcursor) is
     vQuery VARCHAR2(2000);
     vColScenario VARCHAR2(100);
     MATCH_DISP_MOV_ROW VARCHAR2(100) := 'MATCH_DISPLAY_MOVEMENT_ROW';
BEGIN
      vQuery := 'SELECT p.match_id, PKG_ALERT.FN_IS_ROW_HIGHLIGHTED(''' || MATCH_DISP_MOV_ROW || ''', p.host_id, p.entity_id, p.currency_code, p.match_hi_value_date, NULL, '''||p_threshold||''', null, null, p.match_id) scenario_highlighting
      FROM TABLE (PKG_ALERT.FN_GET_MATCH_ROWS (PKG_ALERT.FN_GET_CUR (PKG_ALERT.FN_GET_QUERY_TEXT(''' || p_scenario_id || ''')))) p, s_currency s
      WHERE p.currency_code = s.currency_code
        and p.entity_id = s.entity_id
        and p.host_id = s.host_id';

BEGIN
SELECT replace(AMT_THRESHOLD_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
EXCEPTION
          WHEN OTHERS THEN
              NULL;
END;
        IF p_threshold = 'Y' AND vColScenario IS NOT NULL THEN
            vQuery := vQuery || ' AND p.' || vColScenario || ' >= s.threshold_product ';
END IF;

        IF NVL(p_CcyGroup,'All') != 'All' AND vColScenario IS NOT NULL THEN
            vQuery := vQuery || ' AND s.currency_group_id = ''' || p_CcyGroup || '''';
END IF;

        IF p_currency_code != 'All' THEN
            vQuery := vQuery || ' AND s.currency_code = ''' || p_currency_code || '''';
END IF;

       IF p_entity_id != 'All' THEN
BEGIN
SELECT replace(SEC_ENTITY_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
IF vColScenario IS NOT NULL THEN
                vQuery := vQuery || ' AND p.' || vColScenario || ' = ''' || p_entity_id || '''';
END IF;
EXCEPTION
            WHEN OTHERS THEN
                NULL;
END;
END IF;

      IF p_currency_code != 'All' THEN
BEGIN
SELECT replace(SEC_CURRENCY_COL, '"', '')
INTO vColScenario
FROM P_SCENARIO
WHERE SCENARIO_ID = p_scenario_id;
IF vColScenario IS NOT NULL THEN
                vQuery := vQuery || ' AND p.' || vColScenario || ' = ''' || p_currency_code || '''';
END IF;
EXCEPTION
            WHEN OTHERS THEN
                NULL;
END;
END IF;
OPEN p_ref FOR vQuery;
EXCEPTION
      WHEN OTHERS
      THEN
         sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.proc_get_list_match',
                       SQLCODE,
                       SQLERRM
                      );
END;

   -- Return access for facility menu for a given role
   FUNCTION fn_get_facility_menu_access ( pHost_id     in VARCHAR2,
                                          pFacility_id in VARCHAR2,
                                          pRole_id     in VARCHAR2) return VARCHAR2 is
      vaccessfacility   p_menu_access.access_id%TYPE;
BEGIN
SELECT PK_APPLICATION.fnGetMenuAccess(pHost_id, pRole_ID, NVL(SEC_MENU_ITEM_ID,-1))
INTO vAccessFacility
FROM P_FACILITY
WHERE FACILITY_ID = pFacility_id;
RETURN vAccessFacility;
EXCEPTION
      WHEN NO_DATA_FOUND THEN
          RETURN '2';
END;

   /*
   Execute the generic scenario query and return refcursor of records for a given page
   and return the number of all records for all pages.
   */
   PROCEDURE PRC_EXEC_GENERIC_SCENARIO ( p_Scenario_Id          VARCHAR2,
                                         p_FILTER               VARCHAR2,
                                         p_ORDER                VARCHAR2,
                                         p_ROW_BEGIN            NUMBER,
                                         p_ROW_END              NUMBER,
                                         p_ASC_DESC             VARCHAR2,
                                         p_RoleID               VARCHAR2,
                                         p_CcyGroup             VARCHAR2 DEFAULT NULL,
                                         p_Threshold            VARCHAR2 DEFAULT 'N',
                                         p_CUR_RES       IN OUT SYS_REFCURSOR,
                                         p_Count_Rows      OUT  NUMBER,
                                         p_Query_String    OUT VARCHAR2)
IS
       vFILTER          VARCHAR2 (32000) := '';
       vSQL             CLOB;
       vSqlCounts       CLOB;
       vSecEntityCol    VARCHAR2 (100);
       vSecCurrencyCol  VARCHAR2 (100);
       vSecHostCol      VARCHAR2 (100);
       vAmtThreshold    VARCHAR2 (100);
       vSelect          CLOB;
       vFrom            CLOB;
       vWhere           CLOB;
       curRefCursor     sys_refcursor;
       CurRefCursorFilter sys_refcursor;
       vDateFormat      VARCHAR2(30);
       VINDVAL          TINDVAL;
       VINDVALUE        VARCHAR2(4000);
       VCOLIDX          NUMBER(5);
       VCOLI            TCOLI;
BEGIN
       -- get date format from s_system_parameters
       vDateFormat := pk_utility.fn_get_date_format('Y');
       -- transform the filter given from java side to a WHERE clause to build the query
       PKG_ALERT.spSplitter(p_FILTER, '|', CurRefCursorFilter);
       LOOP
FETCH CurRefCursorFilter INTO VINDVALUE, VCOLIDX;
            EXIT WHEN CurRefCursorFilter%NOTFOUND;

            IF VINDVALUE != 'All' THEN
                PKG_ALERT.spSplitter(VINDVALUE, '$#$', CurRefCursor);
FETCH CurRefCursor BULK COLLECT INTO VINDVAL, VCOLI;
IF vFILTER IS NOT NULL THEN
                    vFILTER := vFILTER || ' AND ';
END IF;
                IF (LOWER(VINDVAL(3)) = 'date') THEN
                    vFILTER := vFILTER || 'TO_DATE(TO_CHAR(SCENQUERY.' || VINDVAL(1) || ',''' || vDateFormat ||''')' || ','''|| vDateFormat ||''')' || '= TO_DATE(''' || VINDVAL(2) || ''','''|| vDateFormat ||''')';
ELSE
                    vFILTER := vFILTER || ' SCENQUERY.' || VINDVAL(1) || CASE   WHEN VINDVAL(2)='(EMPTY)' THEN ' IS NULL'
                                                                                WHEN VINDVAL(2)='(NOT EMPTY)' THEN ' IS NOT NULL'
                                                                                ELSE '=''' || VINDVAL(2) || ''''
END;
END IF;
END IF;
END LOOP;

BEGIN
SELECT SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, AMT_THRESHOLD_COL
INTO vSecHostCol, vSecEntityCol, vSecCurrencyCol, vAmtThreshold
FROM P_SCENARIO
WHERE SCENARIO_ID = p_Scenario_id;
EXCEPTION
            WHEN NO_DATA_FOUND THEN
                NULL;
END;

       -- build the query
       vSelect := 'SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY ';
       IF p_ORDER IS NULL THEN
            vSelect := vSelect || '1';
ELSE
            vSelect := vSelect || 'SCENQUERY.' || p_ORDER || ' ' || p_ASC_DESC;
END IF;
       vSelect := vSelect || ')ROW_, SCENQUERY.*';

       vFrom := ' FROM ('
                || CHR(10)
                || PKG_ALERT.FN_GET_QUERY_TEXT(p_Scenario_id)
                || CHR(10)
                || ') SCENQUERY';
       IF p_Threshold = 'Y' AND vAmtThreshold IS NOT NULL THEN
            vFrom := vFrom || ',S_CURRENCY S';
END IF;

       IF vFILTER IS NOT NULL THEN
            vWhere := ' WHERE ' || vFILTER;
END IF;

       IF   vSecCurrencyCol IS NOT NULL
        AND vSecEntityCol IS NOT NULL
        AND vSecHostCol IS NOT NULL THEN
            IF INSTR(vWhere, 'WHERE') > 0 THEN
                vWhere := vWhere || ' AND ';
ELSE
                vWhere := vWhere || ' WHERE ';
END IF;
            vWhere := vWhere || ' (SCENQUERY.' || vSecCurrencyCol ||' IN (SELECT CURRENCY_CODE FROM S_CURRENCY WHERE CURRENCY_GROUP_ID = ''' || p_CcyGroup
              || ''') OR NVL(''' || p_CcyGroup || ''', ''All'') = ''All'') AND PK_APPLICATION.FNGETCURRENCYACCESS(SCENQUERY.' || vSecHostCol ||', '''|| p_RoleID ||''', SCENQUERY.'|| vSecEntityCol
              ||' ,SCENQUERY.'|| vSecCurrencyCol || ') < 2';
END IF;

       IF p_Threshold = 'Y' AND vAmtThreshold IS NOT NULL THEN
            IF INSTR(vWhere, 'WHERE') > 0 THEN
                vWhere := vWhere || ' AND ';
ELSE
                vWhere := vWhere || ' WHERE ';
END IF;
            vWhere := vWhere || ' SCENQUERY.' || vAmtThreshold || '>= S.THRESHOLD_PRODUCT AND SCENQUERY.' || vSecCurrencyCol || '=S.CURRENCY_CODE AND S.ENTITY_ID = ' ||'SCENQUERY.' || vSecEntityCol;
END IF;

     -- Get the number of records
       vSqlCounts := 'SELECT COUNT(*) ' || vFrom || ' ' || vWhere;

       -- Apply the filter to give only records for a given page
       IF p_ROW_BEGIN || p_ROW_END  != '00' THEN
            vWhere := vWhere || ') WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ' || p_ROW_END;
ELSE
            vWhere := vWhere || ')';
END IF;

       vSQL := vSelect || ' ' || vFrom || ' ' || vWhere;

       p_Query_String := SUBSTR(CAST (vSQL AS VARCHAR2), 1, INSTR(vSQL, ' WHERE ROW_ BETWEEN ') - 1) || ' WHERE ROW_ BETWEEN ' || p_ROW_BEGIN || ' AND ';

Execute immediate cast(vSqlCounts as VARCHAR2) INTO p_Count_Rows;

OPEN p_cur_res FOR cast(vSQL as VARCHAR2);

EXCEPTION
           WHEN OTHERS
           THEN
              sp_error_log ('',
                            'SYSTEM',
                            'DBSERVER',
                            'PKG_ALERT.PRC_EXEC_GENERIC_SCENARIO',
                            SQLCODE,
                            SQLERRM
                          );
END;

   /*
    This procedure is used to split a string by using a delimiter
    IT is used instead of PK_UTILITY.SPSPLITTER procedure because this last one don't return
    the last sub-string (after the last delimiter).
   */
   PROCEDURE spSplitter(
                        pSplitString  IN  VARCHAR2,
                        pDeLimiter    IN  VARCHAR2,
                        pMyCur        OUT SYS_REFCURSOR
                       )
   -- -------------------------------------------------------------------------------
   -- Summary : This procedure is used to split the parameter(pSplitString) by using delimiter(pDeLimiter).
   --           pSplitString = 'XXXXpDeLimiterYYYYpDeLimiterZZZZpDeLimiter'.
   --           pDeLimiter   =  | # @ $ ,
   --           This is used in package PK_MOVEMENT_SUMMARY and procedure PARTY_LIST.
   -- -------------------------------------------------------------------------------
IS
BEGIN
      -- Split the supplied parameter(pDeLimiter) string as individual value when pDeLimiter is NOT NULL.
      IF pDeLimiter IS NOT NULL THEN
         OPEN pMyCur
          FOR SELECT REGEXP_SUBSTR (pSplitString, '[^' || pDeLimiter || ']+', 1, LEVEL) ColValue, LEVEL idx
              FROM DUAL
                  CONNECT BY LEVEL - 1 <= LENGTH (REGEXP_REPLACE (pSplitString, '[^' || pDeLimiter || ']+'));
ELSE
         -- Return the cursor with NULL value When pDeLimiter is NULL.
         OPEN pMyCur
          FOR SELECT NULL ColValue, NULL idx
              FROM DUAL;
END IF;
END spSplitter;
   /*

      fn_get_cur_curr_entity()

      A new function in the PKG_ALERT.fn_get_cur_curr_entity which wraps two filters of entity_id
    and currency_code around scenario sql text, if required. (105157)

      PARAMETERS
       p_sqltext         IN VARCHAR2  SQL text (Manditory)
     p_currency_code   IN VARCHAR2  Currency Code or All for ALL currencies  .
     p_entity_id       IN VARCHAR2  p_entity_id entity number  (Expected)
   */

   FUNCTION fn_get_cur_curr_entity (
            p_sqltext         IN VARCHAR2,
            p_currency_code   IN VARCHAR2 DEFAULT NULL,
            p_entity_id       IN VARCHAR2 )
   RETURN SYS_REFCURSOR
   IS
     r_cursor      SYS_REFCURSOR;
     v_sql         VARCHAR2 (32000) := NULL;
     v_where_and   VARCHAR2 (5) := 'WHERE';
     v_curr_ind    BOOLEAN := FALSE;
BEGIN
     -- This Should never happen.
     IF p_sqltext IS NULL THEN
        RETURN NULL;
END IF;

     IF (UPPER (NVL(p_currency_code,'ALL')) = 'ALL' AND UPPER(NVL(p_entity_id,'ALL')) = 'ALL')
        THEN

        -- Both Currency_code and entity_id is ALL So therfore NO filter is needed.
        OPEN r_cursor FOR p_sqltext;
ELSE
        -- Wrap around SQL
        v_sql := 'SELECT fn_get_cur.* FROM ( ' || p_sqltext || ' ) fn_get_cur ';

        --
        -- Optional currency_code filter.
        --
        IF (UPPER (NVL(p_currency_code,'ALL')) <> 'ALL')
        THEN
           v_sql := v_sql
                 || v_where_and
                 || ' fn_get_cur.currency_code = :p_currency_code ';
           v_where_and := ' AND';
           v_curr_ind := TRUE;
END IF;
        --
        -- Manditory p_entity_id filter.
        --
        v_sql := v_sql
              || v_where_and
              || ' fn_get_cur.entity_id = :p_entity_id ';

         --v_where_and := 'AND';

        --
        -- Open cursor for sql text
        --
        IF v_curr_ind THEN
           OPEN r_cursor FOR v_sql USING p_currency_code, p_entity_id;
ELSE
           OPEN r_cursor FOR v_sql USING p_entity_id;
END IF;

END IF;

RETURN r_cursor;

EXCEPTION

      WHEN OTHERS
      THEN

         sp_error_log('',
                      'SYSTEM',
                      'DBSERVER',
                      'PKG_ALERT.fn_get_cur_curr_entity',
                      SQLCODE,
                      SQLERRM
                     );
         RAISE;
RETURN NULL;
END;

   FUNCTION FN_GET_SCENARIO_INSTANCE_ROW (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                          P_SCHEDULED_QUERY P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                          P_USER_ID         VARCHAR2  DEFAULT CONST_SYS_USER)
   RETURN TAB_SCENARIO_INSTANCE PIPELINED
   IS
        V_REF               SYS_REFCURSOR;
        V_ROW               REC_SCENARIO_INSTANCE;
        V_INST_UNIQUE_EXPR  P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
        V_SEC_HOST_COL      P_SCENARIO.SEC_HOST_COL%TYPE;
        V_SEC_ENTITY_COL    P_SCENARIO.SEC_ENTITY_COL%TYPE;
        V_SEC_CURRENCY_COL  P_SCENARIO.SEC_CURRENCY_COL%TYPE;
        V_ACCOUNT_ID_COL    P_SCENARIO.ACCOUNT_ID_COL%TYPE;
        V_SIGN_COL          P_SCENARIO.SIGN_COL%TYPE;
        V_AMOUNT_COL        P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
        V_MOVEMENT_ID_COL   P_SCENARIO.MOVEMENT_ID_COL%TYPE;
        V_MATCH_ID_COL      P_SCENARIO.MATCH_ID_COL%TYPE;
        V_PAYMENT_ID_COL    P_SCENARIO.PAYMENT_ID_COL%TYPE;
        V_SWEEP_ID_COL      P_SCENARIO.SWEEP_ID_COL%TYPE;
        V_OTHER_ID_COL      P_SCENARIO.OTHER_ID_COL%TYPE;
        V_AMT_THRESH_COL    P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
        V_VALUE_DATE_COL    P_SCENARIO.VALUE_DATE_COL%TYPE;
        V_LIST_UNIQUE_IDENT TAB_UNIQUE_IDENT;
        V_IS_UNIQUE_ROW     VARCHAR2(1) := 'Y';
        V_ERROR_LOC         VARCHAR2(5);
        V_SCENARIO_QUERY_TEXT   P_SCENARIO.QUERY_TEXT%TYPE;

        V_SCENARIO_ID           TAB_VARCHAR;
        V_UNIQUE_IDENT          TAB_VARCHAR;
        V_HOST_ID               TAB_VARCHAR;
        V_ENTITY_ID             TAB_VARCHAR;
        V_CURRENCY_CODE         TAB_VARCHAR;
        V_ACCOUNT_ID            TAB_VARCHAR;
        V_AMOUNT                TAB_NUMBER;
        V_SIGN                  TAB_VARCHAR;
        V_MOVEMENT_ID           TAB_NUMBER;
        V_MATCH_ID              TAB_NUMBER;
        V_PAYMENT_ID            TAB_NUMBER;
        V_SWEEP_ID              TAB_NUMBER;
        V_OTHER_ID              TAB_VARCHAR;
        V_VALUE_DATE            TAB_DATE;
        V_OVER_THRESHOLD        TAB_VARCHAR;
        V_JSON_ATTRIBUTES       TAB_CLOB;
        V_TAB_LIMIT             NUMBER := 500;
        V_REF_SQL               CLOB;
BEGIN
        V_ERROR_LOC := '10';
SELECT NVL(INSTANCE_UNIQUE_EXPRESSION,'NULL'), NVL(SEC_HOST_COL, 'NULL'), NVL(SEC_ENTITY_COL,'NULL'), NVL(SEC_CURRENCY_COL,'NULL'), NVL(ACCOUNT_ID_COL,'NULL'), NVL(AMT_THRESHOLD_COL,'NULL'), NVL(SIGN_COL,'NULL'), NVL(MOVEMENT_ID_COL,'NULL'), NVL(MATCH_ID_COL,'NULL'), NVL(PAYMENT_ID_COL,'NULL'), NVL(SWEEP_ID_COL,'NULL'), NVL(OTHER_ID_COL, 'NULL'), NVL(AMT_THRESHOLD_COL,'NULL'), NVL(VALUE_DATE_COL, 'NULL')
INTO V_INST_UNIQUE_EXPR, V_SEC_HOST_COL, V_SEC_ENTITY_COL, V_SEC_CURRENCY_COL, V_ACCOUNT_ID_COL, V_AMOUNT_COL, V_SIGN_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_PAYMENT_ID_COL, V_SWEEP_ID_COL, V_OTHER_ID_COL, V_AMT_THRESH_COL, V_VALUE_DATE_COL
FROM P_SCENARIO P
WHERE SCENARIO_ID = P_SCENARIO_ID;

V_ERROR_LOC := '20';
        IF P_SCHEDULED_QUERY IS NOT NULL THEN
            V_ERROR_LOC := '30';
            V_REF_SQL := q'[SELECT ']'
                             || P_SCENARIO_ID      || q'[' "SCENARIO_ID", ]'
                             || V_INST_UNIQUE_EXPR || ' "UNIQUE_EXPRESSION", '
                             || V_SEC_HOST_COL     || ' "HOST_ID", '
                             || V_SEC_ENTITY_COL   || ' "ENTITY_ID", '
                             || V_SEC_CURRENCY_COL || ' "CURRENCY_CODE", '
                             || V_ACCOUNT_ID_COL   || ' "ACCOUNT_ID", '
                             || V_AMOUNT_COL       || ' "AMOUNT", '
                             || V_SIGN_COL         || ' "SIGN", '
                             || V_MOVEMENT_ID_COL  || ' "MOVEMENT_ID", '
                             || V_MATCH_ID_COL     || ' "MATCH_ID", '
                             || V_PAYMENT_ID_COL   || ' "PAYMENT_ID", '
                             || V_SWEEP_ID_COL     || ' "SWEEP_ID", '
                             || V_OTHER_ID_COL     || ' "OTHER_ID", '
                             || V_VALUE_DATE_COL   || ' "VALUE_DATE", '
                             || 'CASE WHEN ' || NVL(V_AMT_THRESH_COL,'NULL') || ' > (SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID='||CASE WHEN V_SEC_HOST_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_HOST_COL END||' AND S.ENTITY_ID='||CASE WHEN V_SEC_ENTITY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_ENTITY_COL END ||' AND S.CURRENCY_CODE='||CASE WHEN V_SEC_CURRENCY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_CURRENCY_COL END ||') THEN ''Y'' ELSE ''N'' END "OVER_THRESHOLD", ';

            DBMS_LOB.APPEND (V_REF_SQL, 'PKG_ALERT.FN_GET_QUERYRESULT_AS_JSON (q''[SELECT * FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCHEDULED_QUERY);
            DBMS_LOB.APPEND (V_REF_SQL, ') WHERE ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' = '']'' || ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' || q''['']'') "JSON_ATTRIBUTES" FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCHEDULED_QUERY);
            DBMS_LOB.APPEND (V_REF_SQL, ') SCENQUERY ');

            V_REF := PKG_ALERT.FN_GET_CUR (V_REF_SQL);
ELSE
          -- Get scenario query text
          V_SCENARIO_QUERY_TEXT := PKG_ALERT.FN_GET_QUERY_TEXT (P_SCENARIO_ID);
          IF V_SCENARIO_QUERY_TEXT IS NOT NULL THEN
            V_ERROR_LOC := '40';
            V_REF_SQL := q'[SELECT ']'
                             || P_SCENARIO_ID      || q'[' "SCENARIO_ID", ]'
                             || V_INST_UNIQUE_EXPR || ' "UNIQUE_EXPRESSION", '
                             || V_SEC_HOST_COL     || ' "HOST_ID", '
                             || V_SEC_ENTITY_COL   || ' "ENTITY_ID", '
                             || V_SEC_CURRENCY_COL || ' "CURRENCY_CODE", '
                             || V_ACCOUNT_ID_COL   || ' "ACCOUNT_ID", '
                             || V_AMOUNT_COL       || ' "AMOUNT", '
                             || V_SIGN_COL         || ' "SIGN", '
                             || V_MOVEMENT_ID_COL  || ' "MOVEMENT_ID", '
                             || V_MATCH_ID_COL     || ' "MATCH_ID", '
                             || V_PAYMENT_ID_COL   || ' "PAYMENT_ID", '
                             || V_SWEEP_ID_COL     || ' "SWEEP_ID", '
                             || V_OTHER_ID_COL     || ' "OTHER_ID", '
                             || V_VALUE_DATE_COL   || ' "VALUE_DATE", '
                             || 'CASE WHEN ' || NVL(V_AMT_THRESH_COL,'NULL') || ' > (SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID='||CASE WHEN V_SEC_HOST_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_HOST_COL END||' AND S.ENTITY_ID='||CASE WHEN V_SEC_ENTITY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_ENTITY_COL END ||' AND S.CURRENCY_CODE='||CASE WHEN V_SEC_CURRENCY_COL = 'NULL' THEN 'NULL' ELSE 'SCENQUERY.'||V_SEC_CURRENCY_COL END ||') THEN ''Y'' ELSE ''N'' END "OVER_THRESHOLD", ';

            DBMS_LOB.APPEND (V_REF_SQL, 'PKG_ALERT.FN_GET_QUERYRESULT_AS_JSON (q''[SELECT * FROM (]''');
            DBMS_LOB.APPEND (V_REF_SQL, ' || PKG_ALERT.FN_GET_QUERY_TEXT (''');
            DBMS_LOB.APPEND (V_REF_SQL, P_SCENARIO_ID);
            DBMS_LOB.APPEND (V_REF_SQL, ''') || ');
            DBMS_LOB.APPEND (V_REF_SQL, 'q''[) WHERE ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' = '']'' || ');
            DBMS_LOB.APPEND (V_REF_SQL, V_INST_UNIQUE_EXPR);
            DBMS_LOB.APPEND (V_REF_SQL, ' || q''['']'') "JSON_ATTRIBUTES" FROM (');
            DBMS_LOB.APPEND (V_REF_SQL, V_SCENARIO_QUERY_TEXT);
            DBMS_LOB.APPEND (V_REF_SQL, ') SCENQUERY ');

            V_REF := PKG_ALERT.FN_GET_CUR (V_REF_SQL);

END IF;
END IF;

        IF V_REF IS NOT NULL THEN
          V_ERROR_LOC := '50';
          LOOP
FETCH V_REF BULK COLLECT INTO V_SCENARIO_ID, V_UNIQUE_IDENT, V_HOST_ID, V_ENTITY_ID, V_CURRENCY_CODE, V_ACCOUNT_ID, V_AMOUNT, V_SIGN, V_MOVEMENT_ID, V_MATCH_ID, V_PAYMENT_ID, V_SWEEP_ID, V_OTHER_ID, V_VALUE_DATE, V_OVER_THRESHOLD, V_JSON_ATTRIBUTES LIMIT V_TAB_LIMIT;
            EXIT WHEN (V_SCENARIO_ID.FIRST IS NULL OR V_SCENARIO_ID.LAST IS NULL);

FOR I IN V_SCENARIO_ID.FIRST..V_SCENARIO_ID.LAST
            LOOP
              V_ERROR_LOC := '60';
              V_ROW.SCENARIO_ID := V_SCENARIO_ID(I);
              V_ROW.INSTANCE_UNIQUE_EXPRESSION := V_INST_UNIQUE_EXPR;
              V_ROW.UNIQUE_IDENTIFIER := V_UNIQUE_IDENT(I);

              IF V_LIST_UNIQUE_IDENT.EXISTS(V_UNIQUE_IDENT(I)) THEN
                V_IS_UNIQUE_ROW := 'N';
ELSE
                V_LIST_UNIQUE_IDENT(V_UNIQUE_IDENT(I)) := V_UNIQUE_IDENT(I);
END IF;

              V_ROW.HOST_ID := V_HOST_ID(I);
              V_ROW.ENTITY_ID := V_ENTITY_ID(I);
              V_ROW.CURRENCY_CODE := V_CURRENCY_CODE(I);
              V_ROW.ACCOUNT_ID := V_ACCOUNT_ID(I);
              V_ROW.AMOUNT := V_AMOUNT(I);
              V_ROW.SIGN := V_SIGN(I);
              V_ROW.MOVEMENT_ID := V_MOVEMENT_ID(I);
              V_ROW.MATCH_ID := V_MATCH_ID(I);
              V_ROW.PAYMENT_ID := V_PAYMENT_ID(I);
              V_ROW.SWEEP_ID := V_SWEEP_ID(I);
              V_ROW.OTHER_ID := V_OTHER_ID(I);
              V_ROW.VALUE_DATE := V_VALUE_DATE(I);
              V_ROW.OVER_THRESHOLD := V_OVER_THRESHOLD(I);
              V_ROW.JSON_ATTRIBUTES := V_JSON_ATTRIBUTES(I);
              V_ROW.IS_UNIQUE_ROW := V_IS_UNIQUE_ROW;

PIPE ROW (V_ROW);
END LOOP;
END LOOP;

CLOSE V_REF;
ELSE
          V_ERROR_LOC := '70';
PIPE ROW (NULL);
END IF;
EXCEPTION
      WHEN NO_DATA_NEEDED THEN
        NULL;
WHEN OTHERS THEN
        IF V_REF%ISOPEN THEN
          CLOSE V_REF;
END IF;

        sp_error_log (GLOBAL_VAR.FN_GET_HOST,
                       P_USER_ID,
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_ID || ' at location: ' || v_error_loc || chr(10) || DBMS_UTILITY.format_error_backtrace ,
                        SQLCODE,
                        SQLERRM
                       );

END;

   PROCEDURE SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE,
                                       P_LOG_TEXT                   P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                       P_LOG_USER                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
IS
BEGIN
INSERT INTO P_SCENARIO_INSTANCE_LOG (ID,
                                     INSTANCE_ID,
                                     LOG_DATETIME,
                                     LOG_TEXT,
                                     LOG_USER)
VALUES (SEQ_P_SCENARIO_INSTANCE_LOG.NEXTVAL,
        P_SCENARIO_INSTANCE_ID,
        GLOBAL_VAR.SYS_DATE,
        SUBSTR(P_LOG_TEXT, 1, 200),
        P_LOG_USER);
END;

   FUNCTION FN_GET_COL_AUTO (P_PROGRAM_ID       P_SCENARIO_GUI_ALERT_FACILITY.PROGRAM_ID%TYPE)
   RETURN VARCHAR2 RESULT_CACHE
   IS
        V_COL_AUTO  VARCHAR2(4000);
BEGIN
        V_COL_AUTO :=
        CASE P_PROGRAM_ID
            -- Create a movement
            WHEN '5' THEN 'MOVEMENT_ID,INPUT_DATE,UPDATE_DATE,|P_MOVEMENT_SEQUENCE.NEXTVAL,GLOBAL_VAR.SYS_DATE,GLOBAL_VAR.SYS_DATE,'
            -- Update a movement
            WHEN '6' THEN 'UPDATE_DATE,|GLOBAL_VAR.SYS_DATE,'
            -- Insert/Update Account attribute
            WHEN '115' THEN q'[SEQUENCE_KEY,UPDATE_DATE,|SEQ_P_ACCOUNT_ATTRIBUTE.NEXTVAL,GLOBAL_VAR.SYS_DATE,]'
            -- Make a sweep
            WHEN '12' THEN 'ACCOUNT_LEVEL_FLAG,AUTO_SWEEP_SWITCH_MAIN,AUTO_SWEEP_SWITCH_SUB,|PGT,S,S'
            -- Insert/Update Balance
            WHEN '19' THEN 'UPDATE_DATE,|GLOBAL_VAR.SYS_DATE,'
            ELSE NULL
END;
RETURN V_COL_AUTO;
END;

   PROCEDURE SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID     P_SCENARIO_INSTANCE.ID%TYPE,
                                     PV_NEW_STATUS              P_SCENARIO_INSTANCE.STATUS%TYPE,
                                     P_LOG_TEXT                 P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE,
                                     P_LOG_USER                 P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE,
                                     P_EVENT_STATUS             P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE DEFAULT NULL
                                     )
IS
        V_SQL       VARCHAR2(4000);
BEGIN
        V_SQL := q'[
        UPDATE P_SCENARIO_INSTANCE
           SET STATUS = :PV_NEW_STATUS
        ]' ||
        CASE
            WHEN PV_NEW_STATUS = 'A' THEN ', LAST_RAISED_DATETIME = GLOBAL_VAR.SYS_DATE'
            WHEN PV_NEW_STATUS = 'R' THEN ', RESOLVED_DATETIME = GLOBAL_VAR.SYS_DATE' ||
                                          ', RESOLVED_BY_USER = ''' || P_LOG_USER || ''''
END ||
        CASE
            WHEN P_EVENT_STATUS IS NOT NULL THEN q'[, EVENTS_LAUNCH_STATUS = ']' || P_EVENT_STATUS || q'[']'
END ||
        q'[ WHERE ID = :P_SCENARIO_INSTANCE_ID
              AND STATUS <> ']' || PV_NEW_STATUS || q'[']';

        -- No need to update status to PV_NEW_STATUS if instance has already this status
EXECUTE IMMEDIATE V_SQL USING PV_NEW_STATUS, P_SCENARIO_INSTANCE_ID;

IF SQL%ROWCOUNT > 0 THEN
        -- If new status is different to Active then delete the record from P_SCENARIO_ACTIVE_INSTANCE
          IF PV_NEW_STATUS NOT IN ('A','P','O') THEN
            DELETE P_SCENARIO_ACTIVE_INSTANCE
             WHERE ID = P_SCENARIO_INSTANCE_ID;
ELSE
            INSERT INTO P_SCENARIO_ACTIVE_INSTANCE(ID)
SELECT P_SCENARIO_INSTANCE_ID
FROM DUAL
WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = P_SCENARIO_INSTANCE_ID);
END IF;

INSERT INTO P_SCENARIO_INSTANCE_LOG (ID, INSTANCE_ID, LOG_DATETIME, LOG_TEXT, LOG_USER)
VALUES ( SEQ_P_SCENARIO_INSTANCE_LOG.NEXTVAL, P_SCENARIO_INSTANCE_ID, GLOBAL_VAR.SYS_DATE, P_LOG_TEXT, P_LOG_USER);
END IF;
END;

   PROCEDURE SP_UPD_SCENARIO_INSTANCE (P_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE,
                                       P_USER_ID               P_SCENARIO_INSTANCE.RESOLVED_BY_USER%TYPE DEFAULT CONST_SYS_USER)
IS
        V_STATUS                        P_SCENARIO_INSTANCE.STATUS%TYPE;
        V_EVENTS_LAUNCH_STATUS          P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE;
        V_RAISED_DATETIME               P_SCENARIO_INSTANCE.RAISED_DATETIME%TYPE;
        V_PEND_RESOL_TIME_LIMIT         NUMBER;
        V_PEND_RESOL_QUERY_TEXT         P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT%TYPE;
        V_ALLOW_RERAISE_AFTER_EXPIRY    P_SCENARIO.ALLOW_RERAISE_AFTER_EXPIRY%TYPE;
        V_RERAISE_INTERVAL_MINS         NUMBER;
        V_LAST_RAISED_DATETIME          P_SCENARIO_INSTANCE.LAST_RAISED_DATETIME%TYPE;
        V_INSTANCE_EXPIRY_MINS          NUMBER;
        V_RESULT_PEND_RES_QRY_TXT       NUMBER;
        V_LOCK                          VARCHAR2(1);
        VCOUNT                          NUMBER;
        V_SCENARIO_INSTANCE_ID          P_SCENARIO_INSTANCE.ID%TYPE;

CURSOR CUR_INSTANCES
        IS
SELECT PSI.ID, PSI.STATUS, PSI.EVENTS_LAUNCH_STATUS, PSI.LAST_RAISED_DATETIME,
       PS.PENDING_RESOLUTION_TIME_LIMIT/(24*60), PS.PENDING_RESOLUTION_QUERY_TEXT,
       PS.ALLOW_RERAISE_AFTER_EXPIRY, PS.RERAISE_INTERVAL_MINS/(24*60), PSI.LAST_RAISED_DATETIME,
       PS.INSTANCE_EXPIRY_MINS/(24*60)
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO PS ON PSI.SCENARIO_ID = PS.SCENARIO_ID
WHERE PS.SCENARIO_ID = P_SCENARIO_ID
  AND (   (PSI.STATUS = 'A' AND PS.INSTANCE_EXPIRY_MINS > 0)
    OR (PSI.STATUS = 'P' AND (NVL(PS.PENDING_RESOLUTION_TIME_LIMIT, -1) > 0 OR LTRIM(RTRIM(PS.PENDING_RESOLUTION_QUERY_TEXT)) IS NOT NULL))
    OR (PSI.STATUS = 'R' AND (PS.ALLOW_RERAISE_AFTER_EXPIRY = 'I' OR (PS.ALLOW_RERAISE_AFTER_EXPIRY = 'A' AND PS.RERAISE_INTERVAL_MINS > 0)))
    );

BEGIN
OPEN CUR_INSTANCES;
LOOP
FETCH CUR_INSTANCES INTO V_SCENARIO_INSTANCE_ID, V_STATUS, V_EVENTS_LAUNCH_STATUS, V_RAISED_DATETIME,
                   V_PEND_RESOL_TIME_LIMIT, V_PEND_RESOL_QUERY_TEXT,
                   V_ALLOW_RERAISE_AFTER_EXPIRY, V_RERAISE_INTERVAL_MINS, V_LAST_RAISED_DATETIME,
                                   V_INSTANCE_EXPIRY_MINS;
          EXIT WHEN CUR_INSTANCES%NOTFOUND;

            -- Lock current instance to avoid issues that arise from a manual user interacting
            -- with the same instance at the same time
SELECT 'Y' INTO V_LOCK
FROM P_SCENARIO_INSTANCE
WHERE ID = V_SCENARIO_INSTANCE_ID FOR UPDATE NOWAIT;

-- Identifying existing Active instances where instance expiry time is reached/exceeded.
-- These should be Resolved
IF V_STATUS = 'A' THEN
            IF GLOBAL_VAR.SYS_DATE >= (V_LAST_RAISED_DATETIME + V_INSTANCE_EXPIRY_MINS)
               -- Do not resolve expired instances where event fails
               AND V_EVENTS_LAUNCH_STATUS <> 'F'
            THEN
               SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'R', q'[Status passed to Resolved (Instance expiry time reached)]', P_USER_ID);
END IF;
            -- If status Pending exceeds limit then set status to Overdue
        ELSIF V_STATUS = 'P' THEN
                IF (V_PEND_RESOL_TIME_LIMIT > 0 AND GLOBAL_VAR.SYS_DATE >= V_RAISED_DATETIME + V_PEND_RESOL_TIME_LIMIT) THEN
                    -- Set status as Overdue
                  SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'O', q'[Status passed to Overdue (Pending exceeds time limit)]', P_USER_ID);

ELSE
                    -- Check P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT
                    IF V_PEND_RESOL_QUERY_TEXT IS NOT NULL THEN
                      V_RESULT_PEND_RES_QRY_TXT := FN_GET_RESULT_RESOL_QUERY(V_SCENARIO_INSTANCE_ID);

                      IF V_RESULT_PEND_RES_QRY_TXT > 0 THEN
                            -- Set status as Resolved
                          SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'R', q'[Status passed to Resolved (Pending Resolution Query satisfied)]', P_USER_ID);
END IF;
END IF;
END IF;

            ELSIF V_STATUS = 'R' THEN
                -- Reraise immediately after expiry
                IF V_ALLOW_RERAISE_AFTER_EXPIRY = 'I' THEN
SELECT COUNT(*)
INTO VCOUNT
FROM P_SCENARIO_EVENT_FACILITY GAF
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON GAF.ID = GAM.EVENT_FACILITY_ID
         INNER JOIN P_SCENARIO_INSTANCE PSI ON GAM.SCENARIO_ID = PSI.SCENARIO_ID
WHERE PSI.ID = V_SCENARIO_INSTANCE_ID
  AND GAM.REPEAT_ON_RERAISE = 'Y';
IF VCOUNT > 0 THEN
                    SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (reraise immediately)]', P_USER_ID, 'W');
ELSE
                    SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (reraise immediately)]', P_USER_ID, 'N');
END IF;

                -- reraise after n minutes
                ELSIF V_ALLOW_RERAISE_AFTER_EXPIRY = 'A' THEN
                    IF GLOBAL_VAR.SYS_DATE >= (V_LAST_RAISED_DATETIME + V_INSTANCE_EXPIRY_MINS + V_RERAISE_INTERVAL_MINS) THEN
SELECT COUNT(*)
INTO VCOUNT
FROM P_SCENARIO_EVENT_FACILITY GAF
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON GAF.ID = GAM.EVENT_FACILITY_ID
         INNER JOIN P_SCENARIO_INSTANCE PSI ON GAM.SCENARIO_ID = PSI.SCENARIO_ID
WHERE PSI.ID = V_SCENARIO_INSTANCE_ID
  AND GAM.REPEAT_ON_RERAISE = 'Y';
IF VCOUNT > 0 THEN
                        SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (Reraise after n-minutes)]', P_USER_ID, 'W');
ELSE
                        SP_UPD_INSTANCE_STATUS (V_SCENARIO_INSTANCE_ID, 'A', q'[Status passed to Active (Reraise after n-minutes)]', P_USER_ID, 'N');
END IF;
END IF;
END IF;

END IF;

          -- COMMIT the current scenario instance to unlock this record
COMMIT;
END LOOP;

       IF CUR_INSTANCES%ISOPEN THEN
          CLOSE CUR_INSTANCES;
END IF;
END;

   -- This function launch the event for a given scenario instance
   -- and return 01 if event has run successfully otherwise (failed) return -1
   FUNCTION FN_LAUNCH_SCEN_EVENT (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                  P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER
   IS
        V_SQL                           CLOB;
        V_SQL_PART1                     CLOB;
        V_SQL_PART2                     CLOB;
        V_MAP_COL                       VARCHAR2(128);
        V_MAP_FROM                      VARCHAR2(128);
        V_DATA_TYPE                     VARCHAR2(30);
        V_IS_MANDATARY                  VARCHAR2(1);
        V_PROGRAM_ID                    P_SCENARIO_EVENT_FACILITY.PROGRAM_ID%TYPE;
        V_COL_AUTO_COL                  VARCHAR2(4000);
        V_COL_AUTO_VAL                  VARCHAR2(4000);
        V_HOST_ID                       S_HOST.HOST_ID%TYPE;
        V_STATUS_AFTER_EVENT_TRIGGER    P_SCENARIO.STATUS_AFTER_EVENT_TRIGGER%TYPE;
        V_RETURN_RESULT                 NUMBER := 1;
        V_ACCOUNT_ID_MAIN               P_SWEEP.ACCOUNT_ID_DR%TYPE;
        V_ACCOUNT_ID_SUB                P_SWEEP.ACCOUNT_ID_CR%TYPE;
        V_AMOUNT                        P_SWEEP.ORIGINAL_SWEEP_AMT%TYPE;
        V_ALIGN_ACC_ID                  P_SWEEP.ALIGN_ACCOUNT_ID%TYPE;
        V_VALUE_DATE                    P_SWEEP.VALUE_DATE%TYPE;
        V_ACCOUNT_LEVEL_FLAG            VARCHAR2(3);
        V_COLUMN_VALUE                  CLOB;
        --V_OUT_SUCCESS_STATUS            NUMBER;
        --V_OUT_SWEEP_ID                  P_SWEEP.SWEEP_ID%TYPE;
        V_AUTHORIZE_FLAG_SUB_IN         VARCHAR2(1);
        V_AUTHORIZE_FLAG_SUB_EX         VARCHAR2(1);
        V_AUTHORIZE_FLAG_MAIN_IN        VARCHAR2(1);
        V_AUTHORIZE_FLAG_MAIN_EX        VARCHAR2(1);
        V_AUTO_SWEEP_SWITCH_MAIN        VARCHAR2(1);
        V_AUTO_SWEEP_SWITCH_SUB         VARCHAR2(1);
        V_SWEEP_GROUP_ID                VARCHAR2(1);
        --V_DATE_FORMAT                   VARCHAR2(10) := 'DD/MM/YYYY';
        V_ENTITY_ID_M                   P_SWEEP.ENTITY_ID_DR%TYPE;
        V_ENTITY_ID_S                   P_SWEEP.ENTITY_ID_CR%TYPE;
        V_AUTO_MANUAL                   VARCHAR2(1);
        V_SETTLE_METHOD_CR              P_SWEEP.SETTL_METHOD_CR%TYPE;
        V_SETTLE_METHOD_DR              P_SWEEP.SETTL_METHOD_DR%TYPE;
        V_SWEEP_BOOKCODE_CR             P_SWEEP.BOOKCODE_CR%TYPE;
        V_SWEEP_BOOKCODE_DR             P_SWEEP.BOOKCODE_DR%TYPE;
        V_ACC_TARGET_BALANCE            P_ACCOUNT.TARGET_BALANCE%TYPE;
        V_ACC_TARGET_BALANCE_TYPE       P_ACC_SWEEP_SCHEDULE.TARGET_BALANCE_TYPE%TYPE;
        V_ACC_MIN_AMOUNT                P_ACC_SWEEP_SCHEDULE.MIN_AMOUNT%TYPE;
        V_ADDITIONAL_REF                P_SWEEP.ADDITIONAL_REFERENCE%TYPE;
        V_SWEEP_FROM_BAL_TYPE_CR        P_SWEEP.SWEEP_FROM_BALANCE_TYPE_CR%TYPE;
        V_SWEEP_FROM_BAL_TYPE_DR        P_SWEEP.SWEEP_FROM_BALANCE_TYPE_DR%TYPE;

        V_MOVEMENT_ID                   P_MOVEMENT.MOVEMENT_ID%TYPE;
        V_USE_TYPE                      VARCHAR2(20);
        V_LITERAL_VAL                   VARCHAR2(4000);
        V_MERGE_QUERY                   VARCHAR2(4000);
        V_MRG_SQL_INS_PART              VARCHAR2(4000);
        V_MRG_SQL_UPD_PART              VARCHAR2(4000);
        V_IS_PART_OF_PK                 VARCHAR2(1);
        V_EVENT_FACILITY_ID             P_SCENARIO_EVENT_FACILITY.ID%TYPE;
        V_KEY_VAL                       VARCHAR2(500);
        V_TABLE_NAME                    VARCHAR2(30);
        V_EXECUTE_WHEN                  P_SCENARIO_EVENT_MAPPING.EXECUTE_WHEN%TYPE;
        V_REPEAT_ON_RERAISE             P_SCENARIO_EVENT_MAPPING.REPEAT_ON_RERAISE%TYPE;
        V_RESOLVED_DATETIME             P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE;
        V_STATUS_AFTER_EVENT_LABEL      VARCHAR2(50);
        V_NBR_FAILED_EVENTS             NUMBER := 0;

        V_FORMAT_ID                     P_MESSAGE_FORMATS.FORMAT_ID%TYPE;
        V_MAP_KEY                       P_SCENARIO_EVENT_MAPPING.MAP_KEY%TYPE;
        V_PARAMETERS_XML                VARCHAR2(12);

        V_EXCEPTION_PROC        EXCEPTION;
        PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);

CURSOR CUR_EVENTS IS
SELECT PEF.ID, PEF.PROGRAM_ID, MAP.EXECUTE_WHEN, MAP.REPEAT_ON_RERAISE, PSI.RESOLVED_DATETIME, MAP.MAP_KEY,
       DBMS_LOB.SUBSTR(MAP.PARAMETERS_XML, 12) PARAMETERS_XML
FROM P_SCENARIO_EVENT_FACILITY PEF
         INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (PSI.SCENARIO_ID = MAP.SCENARIO_ID)
WHERE PSI.ID = P_SCENARIO_INSTANCE_ID
  AND PSI.STATUS = 'A'
  AND PSI.EVENTS_LAUNCH_STATUS = 'W'
ORDER BY MAP.ORDINAL;

CURSOR CUR_MAP (P_EVENT_FACILITY_ID IN P_SCENARIO_EVENT_FACILITY.ID%TYPE, P_MAP_KEY P_SCENARIO_EVENT_MAPPING.MAP_KEY%TYPE, P_TABLE_NAME VARCHAR2) IS
          WITH L_USER_COLS
            AS (SELECT COL.TABLE_NAME, COL.COLUMN_NAME, COL.CHAR_LENGTH
                  FROM USER_TAB_COLUMNS COL
                 WHERE TABLE_NAME = UPPER(P_TABLE_NAME))
SELECT EVENT.MAP_COL, REPLACE(EVENT.MAP_FROM, '"'), UPPER(EVENT.DATA_TYPE) DATA_TYPE, EVENT.ISMANDATORY,
       EVENT.USE_TYPE, EVENT.LITERAL_VAL,
       -- Ignore would indicate that no value is being passed for the parameter
       -- This is not the same as specifying an empty Literal value (null) Literal
       CASE WHEN REPLACE(LITERAL_VAL, '"') = CONST_INSTANCE_ID_ATTR
                THEN TO_CHAR(P_SCENARIO_INSTANCE_ID)
            ELSE
                CASE WHEN UPPER(EVENT.DATA_TYPE) = 'TEXT' AND COL.CHAR_LENGTH IS NOT NULL
                         THEN
                         SUBSTR(
                                 CASE WHEN EVENT.USE_TYPE = CONST_LITERAL_USE_TYPE
                                          THEN EVENT.LITERAL_VAL
                                      ELSE CASE WHEN UPPER(EVENT.USE_TYPE) = 'NULL' THEN ''
                                                ELSE PKG_ALERT.FN_GET_SCN_ATTR_VAL(lower(replace(literal_val,'"')), PSI.ID)
                                          END
                                     END,
                                 1, COL.CHAR_LENGTH)
                     ELSE
                         CASE WHEN EVENT.USE_TYPE = CONST_LITERAL_USE_TYPE THEN EVENT.LITERAL_VAL
                              ELSE CASE WHEN UPPER(EVENT.USE_TYPE) = 'NULL' THEN ''
                                        ELSE PKG_ALERT.FN_GET_SCN_ATTR_VAL(lower(replace(literal_val,'"')), PSI.ID)
                                  END
                             END
                    END
           END COLUMN_VAL
FROM P_SCENARIO_EVENT_MAPPING MAP
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
         CROSS JOIN
     XMLTABLE('/mappedParameters/parameter'
                  PASSING XMLTYPE (MAP.PARAMETERS_XML)
                            COLUMNS MAP_COL VARCHAR2(128) path 'name',
              MAP_FROM VARCHAR2(128) path 'map_from',
              DATA_TYPE VARCHAR2(30) path 'data_type',
              ISMANDATORY VARCHAR2(1) path 'isMandatory',
              USE_TYPE VARCHAR2(50) path 'useType',
              LITERAL_VAL VARCHAR2(128) path 'value'
     ) event
         LEFT JOIN L_USER_COLS COL ON (EVENT.MAP_COL = COL.COLUMN_NAME)
WHERE MAP.EVENT_FACILITY_ID = P_EVENT_FACILITY_ID
  AND MAP.MAP_KEY = P_MAP_KEY
  AND PSI.ID = P_SCENARIO_INSTANCE_ID
  AND NVL(USE_TYPE, ' ')  <> CONST_IGNORE_USE_TYPE
  AND (LITERAL_VAL IS NOT NULL OR UPPER(USE_TYPE) = 'NULL');
BEGIN
        -- Loop on event facilities
OPEN CUR_EVENTS;
LOOP
FETCH CUR_EVENTS INTO V_EVENT_FACILITY_ID, V_PROGRAM_ID, V_EXECUTE_WHEN, V_REPEAT_ON_RERAISE, V_RESOLVED_DATETIME, V_MAP_KEY, V_PARAMETERS_XML;
          EXIT WHEN CUR_EVENTS%NOTFOUND;

BEGIN

            IF (        (V_NBR_FAILED_EVENTS > 0 AND V_EXECUTE_WHEN = 'E')
                     OR (V_NBR_FAILED_EVENTS = 0 AND V_EXECUTE_WHEN = 'S')
                     OR (NVL(V_EXECUTE_WHEN, 'A') = 'A'))
                AND (   V_RESOLVED_DATETIME IS NULL
                    OR (V_REPEAT_ON_RERAISE = 'Y' AND V_RESOLVED_DATETIME IS NOT NULL)
                   )
            THEN
              V_SQL_PART1 := NULL;
              V_SQL_PART2 := NULL;
              V_MRG_SQL_INS_PART := NULL;
              V_MRG_SQL_UPD_PART := NULL;
              V_MERGE_QUERY := NULL;
              V_KEY_VAL := NULL;

              -- Get list columns to be populated automatically and their values
              V_COL_AUTO_COL := SUBSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), 1, INSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), '|')-1);
              V_COL_AUTO_VAL := SUBSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), INSTR(FN_GET_COL_AUTO (V_PROGRAM_ID), '|')+1);

              -- For SEND_MESSAGE event there are no mapping XML in P_SCENARIO_EVENT_MAPPING
              -- Only FORMAT_ID value is supplied
              IF V_PROGRAM_ID = 24 THEN
                V_FORMAT_ID := V_PARAMETERS_XML;
ELSE
                -- Set table_name for each program to be used in truncating data before insert/update data
                V_TABLE_NAME := CASE V_PROGRAM_ID
                                  WHEN 5 THEN 'P_MOVEMENT'
                                  WHEN 6 THEN 'P_MOVEMENT'
                                  WHEN 115 THEN 'P_ACCOUNT_ATTRIBUTE'
                                  WHEN 19 THEN 'P_BALANCE'
                                  WHEN 12 THEN 'P_SWEEP'
                                  ELSE NULL
END;

OPEN CUR_MAP (V_EVENT_FACILITY_ID, V_MAP_KEY, V_TABLE_NAME);
LOOP
FETCH CUR_MAP INTO V_MAP_COL, V_MAP_FROM, V_DATA_TYPE, V_IS_MANDATARY, V_USE_TYPE, V_LITERAL_VAL, V_COLUMN_VALUE;
                  EXIT WHEN CUR_MAP%NOTFOUND;

                  V_SQL_PART1 := V_SQL_PART1 || V_MAP_COL || ',';

                  -- V_COLUMN_VALUE is extracted as string, we need to convert it when its data type is date or numeric
                  IF V_DATA_TYPE IN ('DATE', 'DATETIME') THEN
                      V_SQL_PART2 := V_SQL_PART2 || 'TO_DATE (''' || V_COLUMN_VALUE || ''',''' || CONST_DATE_FORMAT_EVENT || '''),';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'TO_DATE (''' || V_COLUMN_VALUE || ''',''' || CONST_DATE_FORMAT_EVENT || ''') AS ' || V_MAP_COL || ',';
                  ELSIF V_DATA_TYPE IN ('INTEGER','NUMBER') THEN
                      V_SQL_PART2 := V_SQL_PART2 || 'TO_NUMBER(''' || REPLACE(REPLACE(V_COLUMN_VALUE, '.', CONST_DECIMAL), ',', CONST_DECIMAL) || '''),';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'TO_NUMBER(''' || REPLACE(REPLACE(V_COLUMN_VALUE, '.', CONST_DECIMAL), ',', CONST_DECIMAL)|| ''') AS ' || V_MAP_COL || ',';
ELSE
                      V_SQL_PART2 := V_SQL_PART2 || 'q''[' || V_COLUMN_VALUE || ']'',';
                      V_MERGE_QUERY := V_MERGE_QUERY || 'q''[' || V_COLUMN_VALUE || ']'' AS ' || V_MAP_COL || ',';
END IF;

                  -- Variables used in Merge query in Insert/Update Balance
                  V_MRG_SQL_INS_PART := V_MRG_SQL_INS_PART || 'B.'|| V_MAP_COL || ',';

                  IF V_PROGRAM_ID = 19 THEN
                    -- Do not update primary key columns to avoid exception:
                    -- ORA-38104: columns referenced in the on clause cannot be updated
SELECT DECODE(COUNT(*), 0, 'N', 'Y')
INTO V_IS_PART_OF_PK
FROM user_constraints cons
         INNER JOIN user_cons_columns cols
                    ON (cons.constraint_name = cols.constraint_name)
WHERE     cols.table_name = 'P_BALANCE'
  AND cons.constraint_type = 'P'
  AND cons.status = 'ENABLED'
  AND cols.column_name = UPPER(V_MAP_COL);

IF V_IS_PART_OF_PK = 'N' THEN
                      V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.' || V_MAP_COL || ' = B.'|| V_MAP_COL || ',';
ELSE
                      V_KEY_VAL := V_KEY_VAL || V_COLUMN_VALUE||'/';
END IF;

                  ELSIF V_PROGRAM_ID = 115 THEN

                    IF V_MAP_COL NOT IN ('HOST_ID','ENTITY_ID','ACCOUNT_ID','ATTRIBUTE_ID','EFFECTIVE_DATE') THEN
                      V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.' || V_MAP_COL || ' = B.'|| V_MAP_COL || ',';
ELSE
                      V_KEY_VAL := V_KEY_VAL || V_COLUMN_VALUE||'/';
END IF;
END IF;

                  -- V_ACCOUNT_LEVEL_FLAG='PGT',V_AUTHORIZE_FLAG_SUB_IN=NULL,V_AUTHORIZE_FLAG_SUB_EX=NULL,
                  -- V_AUTHORIZE_FLAG_MAIN_IN=NULL,V_AUTHORIZE_FLAG_MAIN_EX=NULL,
                  -- V_AUTO_SWEEP_SWITCH_MAIN='S',V_AUTO_SWEEP_SWITCH_SUB='S', V_SWEEP_GROUP_ID=' ',
                  -- V_USER_ID=P_USER_ID, ALIGN_ACC_ID,V_ENTITY_ID_M(account_id_dr),V_ENTITY_ID_S(accoutn_id_cr),
                  -- V_AUTO_MANUAL='M',VALUE_DATE,
                  IF V_MAP_COL = 'HOST_ID' THEN
                      V_HOST_ID := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ENTITY_ID_DR' THEN
                      V_ENTITY_ID_M := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACCOUNT_ID_DR' THEN
                      V_ACCOUNT_ID_MAIN := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ENTITY_ID_CR' THEN
                      V_ENTITY_ID_S := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACCOUNT_ID_CR' THEN
                      V_ACCOUNT_ID_SUB := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'AMOUNT' THEN
                      V_AMOUNT := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'VALUE_DATE' THEN
                      V_VALUE_DATE := TO_DATE (V_COLUMN_VALUE, CONST_DATE_FORMAT_EVENT);

                  ELSIF V_MAP_COL = 'ALIGN_ACCOUNT_ID' THEN
                      V_ALIGN_ACC_ID := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SETTL_METHOD_CR' THEN
                      V_SETTLE_METHOD_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SETTL_METHOD_DR' THEN
                      V_SETTLE_METHOD_DR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_BOOKCODE_CR' THEN
                      V_SWEEP_BOOKCODE_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_BOOKCODE_DR' THEN
                      V_SWEEP_BOOKCODE_DR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'ACC_TARGET_BALANCE' THEN
                      V_ACC_TARGET_BALANCE := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ACC_TARGET_BALANCE_TYPE' THEN
                      V_ACC_TARGET_BALANCE_TYPE := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ACC_MIN_AMOUNT' THEN
                      V_ACC_MIN_AMOUNT := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VALUE, ',', CONST_DECIMAL), '.', CONST_DECIMAL));
                  ELSIF V_MAP_COL = 'ADDITIONAL_REF' THEN
                      V_ADDITIONAL_REF := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_FROM_BAL_TYPE_CR' THEN
                      V_SWEEP_FROM_BAL_TYPE_CR := V_COLUMN_VALUE;
                  ELSIF V_MAP_COL = 'SWEEP_FROM_BAL_TYPE_DR' THEN
                      V_SWEEP_FROM_BAL_TYPE_DR := V_COLUMN_VALUE;

                  -- Columns for Update movement
                  ELSIF V_MAP_COL = 'MOVEMENT_ID' THEN
                      V_MOVEMENT_ID := TO_NUMBER(TRIM(V_COLUMN_VALUE));

END IF;


END LOOP;


                IF CUR_MAP%ISOPEN THEN
                    CLOSE CUR_MAP;
END IF;

END IF;

              -- Since HOST_ID only ever takes a single value in an installation of Predict,
              -- it need not be supplied to the facility
              IF V_HOST_ID IS NULL THEN
                V_HOST_ID := GLOBAL_VAR.FN_GET_HOST;
END IF;

              -- Add Update_user to V_COL_AUTO_COL
              V_COL_AUTO_COL := V_COL_AUTO_COL || 'UPDATE_USER,';
              V_COL_AUTO_VAL := V_COL_AUTO_VAL || '''' || P_USER_ID || ''',';
              V_MRG_SQL_UPD_PART := V_MRG_SQL_UPD_PART || 'A.UPDATE_USER = B.UPDATE_USER,A.UPDATE_DATE = GLOBAL_VAR.SYS_DATE,';
              V_MERGE_QUERY := V_MERGE_QUERY || 'q''[' || P_USER_ID || ']'' AS UPDATE_USER,';

              -- Determine the table from the program
              IF V_PROGRAM_ID = 5 THEN
                  -- Insert a movement
                  -- Columns that should be populated automatically are not given from scenario instance
                  -- For movement creation: MOVEMENT_ID=P_MOVEMENT_SEQUENCE.NEXTVAL, INPUT_DATE=GLOBAL_VAR.SYS_DATE
                  V_SQL := q'[BEGIN INSERT INTO P_MOVEMENT (]' ||
                    V_COL_AUTO_COL ||
                  RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[VALUES (]' ||
                    V_COL_AUTO_VAL ||
                  RTRIM(V_SQL_PART2, ',') || '); PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Movement added: '' || P_MOVEMENT_SEQUENCE.CURRVAL); END;';

              -- Update movement
              ELSIF V_PROGRAM_ID = 6 THEN
                  V_SQL := q'[BEGIN UPDATE P_MOVEMENT SET (]' ||
                    V_COL_AUTO_COL ||
                  RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[= (SELECT ]' ||
                    V_COL_AUTO_VAL ||
                  RTRIM(V_SQL_PART2, ',') || ' FROM DUAL) WHERE MOVEMENT_ID = ' || V_MOVEMENT_ID
                  || '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Movement updated: '' || ' || V_MOVEMENT_ID || '); END;';

              -- Make a sweep
              ELSIF V_PROGRAM_ID = 12 THEN

BEGIN
SELECT sweeping_process.get_format_authorize_flag(host_id,entity_id, new_internal_cr_format),
       sweeping_process.get_format_authorize_flag(host_id,entity_id, new_external_cr_format)
INTO V_AUTHORIZE_FLAG_SUB_IN, V_AUTHORIZE_FLAG_SUB_EX
FROM p_account
WHERE entity_id = V_ENTITY_ID_S
  AND account_id = V_ACCOUNT_ID_SUB;
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                      V_AUTHORIZE_FLAG_SUB_IN := NULL;
                      V_AUTHORIZE_FLAG_SUB_EX := NULL;
END;

BEGIN
SELECT sweeping_process.get_format_authorize_flag(host_id,entity_id, new_internal_cr_format),
       sweeping_process.get_format_authorize_flag(host_id,entity_id, new_external_cr_format)
INTO V_AUTHORIZE_FLAG_MAIN_IN, V_AUTHORIZE_FLAG_MAIN_EX
FROM p_account
WHERE entity_id = V_ENTITY_ID_M
  AND account_id = V_ACCOUNT_ID_MAIN;
EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                      V_AUTHORIZE_FLAG_MAIN_IN := NULL;
                      V_AUTHORIZE_FLAG_MAIN_EX := NULL;
END;

                  -- (V_ENTITY_ID_S, V_ACCOUNT_ID_SUB) = (Enity_id_cr, account_id_cr)
                  -- (V_ENTITY_ID_M, V_ACCOUNT_ID_MAIN) = (Enity_id_dr, account_id_dr)
                  V_ACCOUNT_LEVEL_FLAG := 'PGT';
                  V_AUTO_SWEEP_SWITCH_MAIN := 'S';
                  V_AUTO_SWEEP_SWITCH_SUB := 'S';
                  V_SWEEP_GROUP_ID := ' ';
                  V_AUTO_MANUAL := 'M';

                  V_SQL :=
                  q'[
                  DECLARE
                      V_OUT_SUCCESS_STATUS    NUMBER;
                      V_OUT_SWEEP_ID          P_SWEEP.SWEEP_ID%TYPE;
                      V_EXCEPTION_PROC        EXCEPTION;
                      PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
                  BEGIN
                  PKG_SWEEP_PROCESS.SWEEP_MOVEMENT_GENERATION (']' || V_HOST_ID || q'[',]' ||
                                                             q'[']' || V_ACCOUNT_ID_SUB || q'[',]' ||
                                                           q'[']' || V_ACCOUNT_ID_MAIN || q'[',]' ||
                                                           q'[']' || V_AMOUNT || q'[',]' ||
                                                           q'[']' || V_ACCOUNT_LEVEL_FLAG || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_SUB_IN || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_SUB_EX || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_MAIN_IN || q'[',]' ||
                                                           q'[']' || V_AUTHORIZE_FLAG_MAIN_EX || q'[',]' ||
                                                           q'[']' || V_AUTO_SWEEP_SWITCH_MAIN || q'[',]' ||
                                                           q'[']' || V_AUTO_SWEEP_SWITCH_SUB || q'[',]' ||
                                                           q'[']' || V_SWEEP_GROUP_ID || q'[',]' ||
                                                           q'[']' || P_USER_ID || q'[',]' ||
                                                           q'[']' || NVL(V_ALIGN_ACC_ID, V_ACCOUNT_ID_MAIN) || q'[',]' ||
                                                           q'[']' || V_ENTITY_ID_S || q'[',]' ||
                                                           q'[']' || V_ENTITY_ID_M || q'[',]' ||
                                                           q'[']' || V_AUTO_MANUAL || q'[',]' ||
                                                           q'[TO_DATE(']' || TO_CHAR(V_VALUE_DATE, CONST_DATE_FORMAT_EVENT) || q'[',']' || CONST_DATE_FORMAT_EVENT || q'['),]' ||
                                                           q'[V_OUT_SUCCESS_STATUS,]' ||
                                                           q'[V_OUT_SWEEP_ID,]' ||
                                                           q'[']' || V_SETTLE_METHOD_CR || q'[',]' ||
                                                           q'[']' || V_SETTLE_METHOD_DR || q'[',]' ||
                                                           q'[']' || V_SWEEP_BOOKCODE_CR || q'[',]' ||
                                                           q'[']' || V_SWEEP_BOOKCODE_DR || q'[',]' ||
                                                           q'[']' || V_ACC_TARGET_BALANCE || q'[',]' ||
                                                           q'[']' || V_ACC_TARGET_BALANCE_TYPE || q'[',]' ||
                                                           q'[']' || V_ACC_MIN_AMOUNT || q'[',]' ||
                                                           q'[']' || V_ADDITIONAL_REF || q'[',]' ||
                                                           q'[']' || V_SWEEP_FROM_BAL_TYPE_CR || q'[',]' ||
                                                           q'[']' || V_SWEEP_FROM_BAL_TYPE_DR || q'[']' ||
                                                             q'[);]' || CHR(10) ||
                                                             q'[IF V_OUT_SUCCESS_STATUS = 0 THEN
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Sweep_id created:' || V_OUT_SWEEP_ID);]' || CHR(10) ||
                                                             q'[ELSE]' || CHR(10) ||
                                                             q'[  PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in creating sweep (See error log)');]' ||
                                                             q'[  RAISE V_EXCEPTION_PROC;]' ||
                                                         q'[ END IF;]' || CHR(10) ||
                                                         q'[ EXCEPTION WHEN OTHERS THEN RAISE; END;]';



              -- Insert/Update Balance
              ELSIF V_PROGRAM_ID = 19 THEN
                    V_SQL := q'[BEGIN MERGE INTO P_BALANCE A USING (SELECT ]' ||
                  RTRIM(V_MERGE_QUERY, ',') || ' FROM DUAL) B ' ||
                  ' ON (A.HOST_ID = B.HOST_ID and A.ENTITY_ID = B.ENTITY_ID and A.BALANCE_DATE = B.BALANCE_DATE and A.BAL_TYPE_ID = B.BAL_TYPE_ID)
                  WHEN NOT MATCHED THEN
                  INSERT ('||
                  V_COL_AUTO_COL || RTRIM(V_SQL_PART1, ',')  ||
                  ')' ||
                  q'[VALUES (]' ||
                  V_COL_AUTO_VAL || RTRIM(V_MRG_SQL_INS_PART, ',')  || ')' ||
                  ' WHEN MATCHED THEN UPDATE SET ' ||
                    RTRIM(V_MRG_SQL_UPD_PART, ',') ||
                    '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Balance Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

              -- Add an account attribute
              ELSIF V_PROGRAM_ID = 115 THEN
                    V_SQL := q'[BEGIN MERGE INTO P_ACCOUNT_ATTRIBUTE A USING (SELECT ]' ||
                    RTRIM(V_MERGE_QUERY, ',') || ' FROM DUAL) B ' ||
                    ' ON (    A.HOST_ID = B.HOST_ID AND A.ENTITY_ID = B.ENTITY_ID AND A.ACCOUNT_ID = B.ACCOUNT_ID AND A.ATTRIBUTE_ID = B.ATTRIBUTE_ID AND A.EFFECTIVE_DATE = B.EFFECTIVE_DATE)
                    WHEN NOT MATCHED THEN
                    INSERT ('||
                    V_COL_AUTO_COL || RTRIM(V_SQL_PART1, ',')  ||
                    ')' ||
                    q'[VALUES (]' ||
                    V_COL_AUTO_VAL || RTRIM(V_MRG_SQL_INS_PART, ',')  || ')' ||
                    ' WHEN MATCHED THEN UPDATE SET ' ||
                    RTRIM(V_MRG_SQL_UPD_PART, ',') ||
                    '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE ('||P_SCENARIO_INSTANCE_ID||', ''Run successfully: Account attribute Inserted/updated: '' || ''' || V_KEY_VAL || '''); END;';

              -- Send Message
              ELSIF V_PROGRAM_ID = 24 THEN
                IF V_FORMAT_ID IS NOT NULL THEN
                    V_SQL :=
                    q'[
                    DECLARE
                        V_OUT_SUCCESS_STATUS    NUMBER;
                        V_OUT_SWEEP_ID          P_SWEEP.SWEEP_ID%TYPE;
                        V_EXCEPTION_PROC        EXCEPTION;
                        PRAGMA EXCEPTION_INIT (V_EXCEPTION_PROC, -200001);
                    BEGIN
                         pkg_message_format.message_generation (']' || NULL || q'[',]' ||   -- HostID
                                                             q'[']' || NULL || q'[',]' || -- EntityID
                                                             q'[']' || V_FORMAT_ID || q'[',]' || -- FormatID
                                                             q'[']' || NULL || q'[',]' || -- AccountID
                                                             q'[']' || NULL || q'[',]' || -- Amount
                                                             q'[']' || NULL || q'[',]' || -- MessageLetter
                                                             q'[V_OUT_SUCCESS_STATUS,]' ||
                                                             q'[']' || NULL || q'[',]' ||  -- IntermediaryReq
                                                             q'[']' || P_SCENARIO_INSTANCE_ID || q'[']' ||
                                                             q'[);
                                                             IF V_OUT_SUCCESS_STATUS = 0 THEN
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Run successfully: Message sent:]' || V_FORMAT_ID || q'[');
                                                             ELSE
                                                                PKG_ALERT.SP_LOG_SCENARIO_INSTANCE (]' || P_SCENARIO_INSTANCE_ID || q'[, 'Error in sending message (See error log)');]' ||
                                                         q'[    RAISE V_EXCEPTION_PROC;]' ||
                                                         q'[ END IF;
                                                         EXCEPTION WHEN OTHERS THEN RAISE; END;]';

END IF;

END IF;

BEGIN
                  -- Run event
                  IF V_SQL IS NOT NULL THEN
                      V_RETURN_RESULT := 0;
BEGIN
EXECUTE IMMEDIATE V_SQL;
COMMIT;
EXCEPTION
                        WHEN V_EXCEPTION_PROC THEN
                            V_RETURN_RESULT := -1;
                            V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
WHEN OTHERS THEN
                            -- If an exception is raised when running an event then log it
                            SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error when running event ]' || V_EVENT_FACILITY_ID ||': '||SQLCODE||' - '||SQLERRM, P_USER_ID);
                            sp_error_log (V_HOST_ID,
                                          P_USER_ID,
                                          'DBSERVER',
                                          'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || V_SQL,
                                          SQLCODE,
                                          SQLERRM
                                         );

                          V_RETURN_RESULT := -1;
                          V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
END;
END IF;

EXCEPTION
                  WHEN OTHERS THEN
                      -- If an exception is raised when running an event then log it
                      SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error when running event ]' || V_EVENT_FACILITY_ID ||': '||SQLCODE||' - '||SQLERRM, P_USER_ID);
                      sp_error_log (V_HOST_ID,
                                    P_USER_ID,
                                    'DBSERVER',
                                      'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                                    SQLCODE,
                                    SQLERRM
                                   );
                  -- Return an indication that event is completed with failure
                  V_RETURN_RESULT := -1;
                  V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
END;


END IF;

EXCEPTION
            WHEN OTHERS THEN
              sp_error_log (V_HOST_ID,
                            P_USER_ID,
                            'DBSERVER',
                              'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || ' Event ' || V_EVENT_FACILITY_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                            SQLCODE,
                            SQLERRM
                           );
END;
END LOOP;

        -- No event ran
        IF V_RETURN_RESULT <> 1 AND V_NBR_FAILED_EVENTS = 0 THEN
          -- Get STATUS_AFTER_EVENT_TRIGGER from related scenario
SELECT STATUS_AFTER_EVENT_TRIGGER, DECODE (STATUS_AFTER_EVENT_TRIGGER,'P','Pending','A','Active','O','Overdue','R','Resolved')
INTO V_STATUS_AFTER_EVENT_TRIGGER, V_STATUS_AFTER_EVENT_LABEL
FROM P_SCENARIO PS
         INNER JOIN P_SCENARIO_INSTANCE PSI ON (PS.SCENARIO_ID = PSI.SCENARIO_ID)
WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

PKG_ALERT.SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, V_STATUS_AFTER_EVENT_TRIGGER, q'[Status Passed to ]' || V_STATUS_AFTER_EVENT_LABEL || ' (Status after launching events)', P_USER_ID);
END IF;

        IF CUR_EVENTS%ISOPEN THEN
          CLOSE CUR_EVENTS;
END IF;

        -- Do not update event_launch_status only if at least one event has run
        IF V_RETURN_RESULT <> 1 THEN
          IF V_NBR_FAILED_EVENTS > 0 THEN
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;
V_RETURN_RESULT := -1;
ELSE
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = P_SCENARIO_INSTANCE_ID;
END IF;
END IF;

RETURN V_RETURN_RESULT;

EXCEPTION
      WHEN OTHERS THEN
          -- If an exception is raised when running an event then log it
          SP_LOG_SCENARIO_INSTANCE (P_SCENARIO_INSTANCE_ID, q'[Error: ]'||SQLCODE||':'||SQLERRM, P_USER_ID);
          sp_error_log (V_HOST_ID,
                        P_USER_ID,
                        'DBSERVER',
                          'PKG_ALERT.FN_LAUNCH_SCEN_EVENT -> Error for P_SCENARIO_INSTANCE.ID=' || P_SCENARIO_INSTANCE_ID || CHR(10) || DBMS_UTILITY.format_error_backtrace ,
                        SQLCODE,
                        SQLERRM
                       );
          V_RETURN_RESULT := -1;
UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_SCENARIO_INSTANCE_ID;


IF CUR_EVENTS%ISOPEN THEN
          CLOSE CUR_EVENTS;
END IF;

RETURN V_RETURN_RESULT;
END;

   FUNCTION FN_GET_SCHEDULED_QUERY (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                    P_SCHEDULED_ID    P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE)
   RETURN CLOB
   IS
      V_QUERY_TEXT_SCHEDULE   P_SCENARIO.QUERY_TEXT%TYPE;
BEGIN
      V_QUERY_TEXT_SCHEDULE := PKG_ALERT.FN_GET_QUERY_TEXT(P_SCENARIO_ID);
FOR PARM IN (
        SELECT param_name, param_value
          FROM P_SCENARIO_SCHEDULE SSCH
               CROSS JOIN
               XMLTABLE (
                  '/PARAMETERS/PARAMETER'
                  PASSING XMLTYPE (SSCH.parameter_xml)
                  COLUMNS PARAM_NAME VARCHAR2 (128) PATH 'NAME',
                          PARAM_VALUE VARCHAR2 (128) PATH 'VALUE') params
         WHERE SSCH.SCENARIO_SCHEDULE_ID = P_SCHEDULED_ID
           AND SSCH.parameter_xml IS NOT NULL)
       LOOP
          V_QUERY_TEXT_SCHEDULE := REGEXP_REPLACE(V_QUERY_TEXT_SCHEDULE, 'P\{'||PARM.PARAM_NAME||'\}', 'q''{' ||PARM.PARAM_VALUE || '}''', 1, 0, 'i');
END LOOP;


RETURN V_QUERY_TEXT_SCHEDULE;
END;

    PROCEDURE SP_PROCESS_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                   P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                   P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                   P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL)
IS
        V_TAB_INSTANCE_ID               TAB_INT;
CURSOR CUR_INSTANCES  IS
SELECT INSTANCE_UNIQUE_EXPRESSION, UNIQUE_IDENTIFIER, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID, AMOUNT, SIGN, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OVER_THRESHOLD, VALUE_DATE, OTHER_ID,
       JSON_ATTRIBUTES, IS_UNIQUE_ROW
FROM TABLE(PKG_ALERT.FN_GET_SCENARIO_INSTANCE_ROW(P_SCENARIO_ID, P_QUERY_TEXT, P_USER_ID));

CURSOR CUR_OLD_INSTANCES IS
SELECT PSI.ID
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
  AND PSI.ID NOT IN (SELECT * FROM TABLE(V_TAB_INSTANCE_ID));

V_UNIQUE_IDENTIFIER P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
        V_HOST_ID           P_SCENARIO_INSTANCE.HOST_ID%TYPE;
        V_ENTITY_ID         P_SCENARIO_INSTANCE.ENTITY_ID%TYPE;
        V_CCY_CODE          P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE;
        V_ACCOUNT_ID        P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE;
        V_AMOUNT            P_SCENARIO_INSTANCE.AMOUNT%TYPE;
        V_SIGN              P_SCENARIO_INSTANCE.SIGN%TYPE;
        V_MOVEMENT_ID       P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE;
        V_MATCH_ID          P_SCENARIO_INSTANCE.MATCH_ID%TYPE;
        V_SWEEP_ID          P_SCENARIO_INSTANCE.SWEEP_ID%TYPE;
        V_PAYMENT_ID        P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE;
        V_OTHER_ID          P_SCENARIO_INSTANCE.OTHER_ID%TYPE;
        V_VALUE_DATE        P_SCENARIO_INSTANCE.VALUE_DATE%TYPE;
        V_INSTANCE_ID       NUMBER;
        V_OVER_THRESHOLD    P_SCENARIO_INSTANCE.OVER_THRESHOLD%TYPE;
        V_INSTANCE_UNIQUE_EXPRESSION    P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
        --V_DATE_FORMAT       VARCHAR2(30);
        V_ATTRIBUTES_JSON               P_SCENARIO_INSTANCE.ATTRIBUTES_JSON%TYPE;
        V_COUNT_INSTANCES               NUMBER;
        V_COUNT_EVENT_MAP               NUMBER;
        V_START_TIMESTAMP               TIMESTAMP;
        V_END_TIMESTAMP                 TIMESTAMP;
        V_PROCESS_DURATION   INTERVAL DAY (2) TO SECOND (4);
        V_RESULT_LAUNCH_EVENT           NUMBER;
        V_START_TEST_TIME               DATE;
        I                               NUMBER := 1;
        V_IS_UNIQUE_ROW                 VARCHAR2(1);
        V_ERROR_LOCATION                VARCHAR2(10);
        V_NBR_FAILED_INST_EVENT         NUMBER := 0;

BEGIN
      V_ERROR_LOCATION := '10';
      V_START_TIMESTAMP := SYSTIMESTAMP;
      V_START_TEST_TIME := GLOBAL_VAR.SYS_DATE;
OPEN CUR_INSTANCES;
LOOP
FETCH CUR_INSTANCES INTO V_INSTANCE_UNIQUE_EXPRESSION, V_UNIQUE_IDENTIFIER, V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID, V_AMOUNT, V_SIGN, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OVER_THRESHOLD, V_VALUE_DATE, V_OTHER_ID, V_ATTRIBUTES_JSON, V_IS_UNIQUE_ROW;
        EXIT WHEN CUR_INSTANCES%NOTFOUND;
        V_ERROR_LOCATION := '20';

        -- Check if this unique identifier is unique
        IF V_IS_UNIQUE_ROW = 'Y' THEN
          V_ERROR_LOCATION := '30';
          -- Does instance exist already
SELECT COUNT(*)
INTO V_COUNT_INSTANCES
FROM P_SCENARIO_INSTANCE
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND UNIQUE_IDENTIFIER = V_UNIQUE_IDENTIFIER;

V_ERROR_LOCATION := '40';
          IF V_COUNT_INSTANCES = 0 THEN
            -- Are there events to launch
            V_ERROR_LOCATION := '50';
SELECT COUNT(*)
INTO V_COUNT_EVENT_MAP
FROM P_SCENARIO_EVENT_FACILITY PEF
         INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;

V_ERROR_LOCATION := '60';
            -- Make an instance scenario
            IF V_COUNT_EVENT_MAP = 0 THEN
              V_ERROR_LOCATION := '70';
              V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;
INSERT INTO P_SCENARIO_INSTANCE (ID,SCENARIO_ID, UNIQUE_IDENTIFIER, STATUS, RAISED_DATETIME, LAST_RAISED_DATETIME,
                                 RESOLVED_DATETIME, RESOLVED_BY_USER, EVENTS_LAUNCH_STATUS, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID,
                                 AMOUNT, SIGN, OVER_THRESHOLD, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OTHER_ID, VALUE_DATE, ATTRIBUTES_JSON)
VALUES (V_INSTANCE_ID, P_SCENARIO_ID, V_UNIQUE_IDENTIFIER, 'A', GLOBAL_VAR.SYS_DATE, GLOBAL_VAR.SYS_DATE,
        NULL, NULL, 'N', V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID,
        V_AMOUNT, V_SIGN, V_OVER_THRESHOLD, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OTHER_ID, V_VALUE_DATE, V_ATTRIBUTES_JSON);
ELSE
              V_ERROR_LOCATION := '80';
              V_INSTANCE_ID := SEQ_P_SCENARIO_INSTANCE.NEXTVAL;
INSERT INTO P_SCENARIO_INSTANCE (ID,SCENARIO_ID, UNIQUE_IDENTIFIER, STATUS, RAISED_DATETIME, LAST_RAISED_DATETIME,
                                 RESOLVED_DATETIME, RESOLVED_BY_USER, EVENTS_LAUNCH_STATUS, HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID,
                                 AMOUNT, SIGN, OVER_THRESHOLD, MOVEMENT_ID, MATCH_ID, SWEEP_ID, PAYMENT_ID, OTHER_ID, VALUE_DATE, ATTRIBUTES_JSON)
VALUES (V_INSTANCE_ID, P_SCENARIO_ID, V_UNIQUE_IDENTIFIER, 'A', GLOBAL_VAR.SYS_DATE, GLOBAL_VAR.SYS_DATE,
        NULL, NULL, 'W', V_HOST_ID, V_ENTITY_ID, V_CCY_CODE, V_ACCOUNT_ID,
        V_AMOUNT, V_SIGN, V_OVER_THRESHOLD, V_MOVEMENT_ID, V_MATCH_ID, V_SWEEP_ID, V_PAYMENT_ID, V_OTHER_ID, V_VALUE_DATE, V_ATTRIBUTES_JSON);
END IF;
            V_ERROR_LOCATION := '70';
              -- Duplicate this instance into P_SCENARIO_ACTIVE_INSTANCE to be used for performance
INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
SELECT V_INSTANCE_ID
FROM DUAL
WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = V_INSTANCE_ID);
V_ERROR_LOCATION := '80';
                  -- Insert logs
              SP_LOG_SCENARIO_INSTANCE (V_INSTANCE_ID, q'[Scenario instance inserted (]' || P_SCENARIO_ID || ')', P_USER_ID);


ELSE
            V_ERROR_LOCATION := '90';
            -- Checking existing status value
SELECT PSI.ID
INTO V_INSTANCE_ID
FROM P_SCENARIO_INSTANCE PSI
         INNER JOIN P_SCENARIO PS ON PSI.SCENARIO_ID = PS.SCENARIO_ID
WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
  AND PSI.UNIQUE_IDENTIFIER = V_UNIQUE_IDENTIFIER;

END IF;

        -- Instance is not unique
ELSE
          V_ERROR_LOCATION := '90';
          sp_error_log('',
                   'SYSTEM',
                   'DBSERVER',
                   'PKG_ALERT.SP_PROCESS_SCENARIO:  ''' || V_UNIQUE_IDENTIFIER || ''' Is not unique for scenario: ' || P_SCENARIO_ID,
                   SQLCODE,
                   SQLERRM);

END IF;

        V_ERROR_LOCATION := '100';
        -- Launch events for the given instance scenario
        IF V_COUNT_EVENT_MAP > 0 THEN
            V_RESULT_LAUNCH_EVENT := PKG_ALERT.FN_LAUNCH_SCEN_EVENT(V_INSTANCE_ID, P_USER_ID);
END IF;

        IF V_RESULT_LAUNCH_EVENT <> 0 THEN
          V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
END IF;

        -- Add the current instance id into this collection to avoid that it is treated twice
        V_TAB_INSTANCE_ID (I) := V_INSTANCE_ID;
        I := I + 1;
END LOOP;

      V_ERROR_LOCATION := '110';
      -- Calculate scenario counts
      SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID);

      V_ERROR_LOCATION := '120';
      IF CUR_INSTANCES%ISOPEN THEN
        CLOSE CUR_INSTANCES;
END IF;

      V_ERROR_LOCATION := '130';
      -- Check if we should use parallel processing for existing instances
      DECLARE
          V_INSTANCE_COUNT NUMBER;
          V_USE_PARALLEL BOOLEAN := FALSE;
      BEGIN
          -- Count instances that need event processing
          SELECT COUNT(*)
          INTO V_INSTANCE_COUNT
          FROM P_SCENARIO_INSTANCE PSI
               INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
          WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
            AND PSI.EVENTS_LAUNCH_STATUS = 'W';

          -- Determine if parallel processing should be used
          V_USE_PARALLEL := (V_INSTANCE_COUNT >= CONST_PARALLEL_THRESHOLD);

          IF V_USE_PARALLEL THEN
              -- Use parallel processing for large batches
              SP_LOG_SCENARIO_INSTANCE(NULL, 'Using parallel processing for ' || V_INSTANCE_COUNT || ' existing instances', P_USER_ID);
              SP_PROCESS_SCENARIO_PARALLEL(P_SCENARIO_ID, P_USER_ID, P_QUERY_TEXT, P_SCENARIO_SCHEDULE_ID);
              -- Skip the sequential loop below
              GOTO SKIP_SEQUENTIAL_PROCESSING;
          ELSE
              -- Use sequential processing for small batches
              SP_LOG_SCENARIO_INSTANCE(NULL, 'Using sequential processing for ' || V_INSTANCE_COUNT || ' existing instances', P_USER_ID);
          END IF;
      END;

      -- Sequential processing (only executed if parallel processing was not used)
      OPEN CUR_OLD_INSTANCES;
      LOOP
          FETCH CUR_OLD_INSTANCES INTO V_INSTANCE_ID;
          EXIT WHEN CUR_OLD_INSTANCES%NOTFOUND;

          V_ERROR_LOCATION := '140';
          -- Use optimized event launcher for better performance
          V_RESULT_LAUNCH_EVENT := FN_LAUNCH_SCEN_EVENT_OPTIMIZED(V_INSTANCE_ID, P_USER_ID, 'Y');

          IF V_RESULT_LAUNCH_EVENT <> 1 THEN
              V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
          END IF;

          -- Commit every 50 instances to avoid long transactions
          IF MOD(CUR_OLD_INSTANCES%ROWCOUNT, 50) = 0 THEN
              COMMIT;
          END IF;
      END LOOP;

      <<SKIP_SEQUENTIAL_PROCESSING>>
      -- Delete array to allow launching events for these instances in the next job execution
      V_TAB_INSTANCE_ID.DELETE;

      V_ERROR_LOCATION := '150';
      IF CUR_OLD_INSTANCES%ISOPEN THEN
        CLOSE CUR_OLD_INSTANCES;
END IF;

      V_ERROR_LOCATION := '160';
      -- Update all existing instances for the given scenario_id
      -- even if these instances are no longer retreived by scenario query
      SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);

      V_ERROR_LOCATION := '170';
      -- calculate run duration for this scenario
      V_END_TIMESTAMP := SYSTIMESTAMP;
      V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;
      -- Update scenario record with last RUN time = now AND DURATION OF RUN
      V_ERROR_LOCATION := '180';
UPDATE P_SCENARIO_SYSTEM
SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_DURATION_SECS = PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
WHERE SCENARIO_ID = P_SCENARIO_ID;

IF SQL%ROWCOUNT = 0 THEN
        V_ERROR_LOCATION := '190';
INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
SELECT P_SCENARIO_ID, GLOBAL_VAR.SYS_DATE, PKG_ALERT.FN_CONV_INTERVAL_TO_SECS (V_PROCESS_DURATION)
FROM DUAL
WHERE NOT EXISTS (SELECT NULL
                  FROM P_SCENARIO_SYSTEM
                  WHERE SCENARIO_ID =  P_SCENARIO_ID);
END IF;

      V_ERROR_LOCATION := '200';

      IF P_SCENARIO_SCHEDULE_ID IS NOT NULL THEN
UPDATE P_SCENARIO_SCHEDULE
SET LAST_RUN_STARTED = V_START_TEST_TIME,
    LAST_RUN_ENDED = GLOBAL_VAR.SYS_DATE,
    LAST_RUN_STATUS = CASE WHEN V_NBR_FAILED_INST_EVENT > 0 THEN 'F' ELSE 'S' END
WHERE SCENARIO_ID = P_SCENARIO_ID
  AND SCENARIO_SCHEDULE_ID = P_SCENARIO_SCHEDULE_ID;
END IF;

COMMIT;

EXCEPTION
     WHEN OTHERS THEN
        IF CUR_INSTANCES%ISOPEN THEN
          CLOSE CUR_INSTANCES;
END IF;
        sp_error_log('',
                     'SYSTEM',
                     'DBSERVER',
                     'PKG_ALERT.SP_PROCESS_SCENARIO: Error when running scenario ' || P_SCENARIO_ID || ' at location ' || V_ERROR_LOCATION || CHR(10) || DBMS_UTILITY.FORMAT_ERROR_BACKTRACE,
                     SQLCODE,
                     SQLERRM);
END;

  PROCEDURE SP_PROCESS_ALL_SCENARIOS (P_USER_ID     P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
IS
        CURSOR CUR_SCENARIO IS
SELECT S.SCENARIO_ID, S.GENERATION_BASIS
FROM P_SCENARIO S
         LEFT JOIN P_SCENARIO_SYSTEM SS ON (S.SCENARIO_ID = SS.SCENARIO_ID)
WHERE S.ACTIVE_FLAG = 'Y'
  AND S.RECORD_SCENARIO_INSTANCES = 'Y'
  --AND NVL(S.INSTANCE_EXPIRY_MINS, -1) <> 0
  -- Run only cyclic and Scheduled scenarios not API scenarios
  AND S.GENERATION_BASIS IN ('C','S')
  AND PKG_ALERT.FN_TEST_START_END_TIME (GLOBAL_VAR.SYS_DATE, S.START_TIME, S.END_TIME) = 'Y'
  AND (   SS.LAST_RUN_DATE IS NULL -- scenario never checked before
    OR SS.LAST_RUN_DATE > GLOBAL_VAR.SYS_DATE -- Last checked in future (test date anomaly)
    OR -- or scenario was last checked 'run every' time ago
          (SS.LAST_RUN_DATE <
           (  GLOBAL_VAR.SYS_DATE
               - (  TO_NUMBER (
                            TO_CHAR (
                                    TO_DATE (NVL (S.RUN_EVERY, '00:00:00'), 'HH24:MI:SS'),
                                    'SSSSS'))
                   / 86400))));

V_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE;
        V_QUERY_TEXT_SCHEDULE   P_SCENARIO.QUERY_TEXT%TYPE;
        V_GENERATION_BASIS      P_SCENARIO.GENERATION_BASIS%TYPE;
        V_ERROR_LOCATION        VARCHAR2(3);

BEGIN
        V_ERROR_LOCATION := '10';
        -- insert scenario instances
OPEN CUR_SCENARIO;
LOOP
FETCH CUR_SCENARIO INTO V_SCENARIO_ID, V_GENERATION_BASIS;
            EXIT WHEN CUR_SCENARIO%NOTFOUND;

            V_ERROR_LOCATION := '20';
            -- Scheduled scenario
            IF V_GENERATION_BASIS = 'S' THEN
BEGIN
                V_ERROR_LOCATION := '30';
                -- Get the records to be used from p_scenario_schedule
FOR SCHED IN (
                  SELECT SCH.SCENARIO_SCHEDULE_ID
                    FROM P_SCENARIO_SCHEDULE SCH
                         INNER JOIN P_SCENARIO S
                                 ON (SCH.SCENARIO_ID = S.SCENARIO_ID)
                         LEFT JOIN P_SCENARIO_SYSTEM SYS
                                 ON (SYS.SCENARIO_ID = S.SCENARIO_ID)
                  WHERE S.SCENARIO_ID = V_SCENARIO_ID
                    AND S.ACTIVE_FLAG = 'Y'
                    -- check criteria for running:
                    -- current time must be later than check_time
                    AND SCH.CHECK_TIME <= TO_CHAR (GLOBAL_VAR.SYS_DATE, 'HH24:MI')
                    -- last run must ...
                    AND (-- have been prior to check_time
                         TO_CHAR (NVL(SCH.LAST_RUN_STARTED,SYS.LAST_RUN_DATE), 'HH24:MI') < SCH.CHECK_TIME
                         OR
                         -- appear to have been run in the future - a test date situation
                         NVL(SCH.LAST_RUN_STARTED,SYS.LAST_RUN_DATE) > GLOBAL_VAR.SYS_DATE
                         OR
                         --First execution
                         SYS.LAST_RUN_DATE IS NULL
                         )
                )
                LOOP
                   V_ERROR_LOCATION := '40';
                   V_QUERY_TEXT_SCHEDULE := FN_GET_SCHEDULED_QUERY (V_SCENARIO_ID, SCHED.SCENARIO_SCHEDULE_ID);
                   V_ERROR_LOCATION := '50';
                 IF V_QUERY_TEXT_SCHEDULE IS NOT NULL THEN
                      V_ERROR_LOCATION := '60';
                      PKG_ALERT.SP_PROCESS_SCENARIO(V_SCENARIO_ID, CONST_SYS_USER, V_QUERY_TEXT_SCHEDULE, SCHED.SCENARIO_SCHEDULE_ID);
END IF;
END LOOP;
EXCEPTION
                WHEN OTHERS THEN
                  sp_error_log('',
                               'SYSTEM',
                               'DBSERVER',
                               'PKG_ALERT.SP_PROCESS_ALL_SCENARIOS -> Error for ' || V_SCENARIO_ID || ' at location ' || V_ERROR_LOCATION,
                               SQLCODE,
                               SQLERRM
                       );
END;

            -- Cyclic scenario
ELSE

                SP_PROCESS_SCENARIO (V_SCENARIO_ID, P_USER_ID);
COMMIT;

END IF;
END LOOP;
COMMIT;


IF CUR_SCENARIO%ISOPEN THEN
            CLOSE CUR_SCENARIO;
END IF;

EXCEPTION
        WHEN OTHERS THEN
            IF CUR_SCENARIO%ISOPEN THEN
                CLOSE CUR_SCENARIO;
END IF;

END;

   FUNCTION FN_GET_XML_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2
   IS
      V_SQL               VARCHAR2(200);
      V_XML_INSTANCE_ROW  VARCHAR2(4000);
      V_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE;
BEGIN
BEGIN
EXECUTE IMMEDIATE P_QUERY_TEXT INTO V_INSTANCE_ID;

V_SQL := q'[SELECT HOST_ID,ENTITY_ID,CURRENCY_CODE,ACCOUNT_ID,VALUE_DATE,MOVEMENT_ID,MATCH_ID,SWEEP_ID,PAYMENT_ID,OTHER_ID FROM P_SCENARIO_INSTANCE WHERE ID = ]' || V_INSTANCE_ID;
        V_XML_INSTANCE_ROW := PKG_ALERT.FN_GET_QUERYRESULT_AS_XML(V_SQL, CONST_DATE_FORMAT, 'rowset', 'instance');

SELECT EXTRACT (XMLTYPE(V_XML_INSTANCE_ROW),'/rowset/instance').getclobval()
INTO V_XML_INSTANCE_ROW
FROM DUAL;

EXCEPTION
        WHEN TOO_MANY_ROWS THEN
          V_XML_INSTANCE_ROW := NULL;
WHEN NO_DATA_FOUND THEN
          V_XML_INSTANCE_ROW := NULL;
END;

RETURN V_XML_INSTANCE_ROW;
END;

   FUNCTION FN_GET_JSON_INSTANCE_ROW (P_QUERY_TEXT   VARCHAR2)
   RETURN VARCHAR2
   IS
      V_SQL               VARCHAR2(4000);
      V_INSTANCE_ID       P_SCENARIO_INSTANCE.ID%TYPE;
BEGIN
EXECUTE IMMEDIATE P_QUERY_TEXT INTO V_INSTANCE_ID;

V_SQL := q'[SELECT JSON_ARRAYAGG (JSON_OBJECT('HOST_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "HOST_ID"),
                               'ENTITY_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "ENTITY_ID"),
                               'CURRENCY_CODE' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "CURRENCY_CODE"),
                               'ACCOUNT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "ACCOUNT_ID"),
                               'VALUE_DATE' VALUE JSON_OBJECT('TYPE' VALUE 'DATE', 'content' VALUE "VALUE_DATE"),
                               'MOVEMENT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "MOVEMENT_ID"),
                               'MATCH_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "MATCH_ID"),
                               'SWEEP_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "SWEEP_ID"),
                               'PAYMENT_ID' VALUE JSON_OBJECT('TYPE' VALUE 'NUMBER', 'content' VALUE "PAYMENT_ID"),
                               'OTHER_ID' VALUE JSON_OBJECT('TYPE' VALUE 'TEXT', 'content' VALUE "OTHER_ID")
                               ))
                   FROM P_SCENARIO_INSTANCE WHERE ID = ]' || V_INSTANCE_ID;



RETURN V_SQL;
END;

   FUNCTION fn_get_queryresult_as_xml(
                                pv_query_text IN VARCHAR2,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
        RETURN CLOB
    AS
        vn_metadataCursorId   NUMBER;
        vn_metadataExecute    NUMBER;
        vn_col_count          INTEGER;
        vt_metadataTab        DBMS_SQL.desc_tab;
        vn_colnum             NUMBER;
        vr_metadata_rec       DBMS_SQL.desc_rec;
        vc_xml_query          VARCHAR2(32000);
        vv_query_text_io      VARCHAR2(32000);
        vv_xml_elements       VARCHAR2(32000);
        vx_result             XMLTYPE;
        case_cdata            VARCHAR2(1000);
        vv_date_format        VARCHAR2(100);
        vv_row_set_tag        VARCHAR2(100);
        vv_row_tag            VARCHAR2(100);
        v_exist_count_col     VARCHAR2(1) := 'N';
        vv_xml_instance_row   VARCHAR2(4000);
BEGIN
        vv_query_text_io := pv_query_text;
        -- Remove double cotes to avoid getting error related to case sensitive in column names
        --vv_query_text_io := REPLACE(vv_query_text_io, '"');
        -- Fill in default values
        IF pv_date_format IS NULL THEN
            vv_date_format := 'YYYY-MM-DD';
ELSE
            vv_date_format := pv_date_format;
END IF;

        IF pv_row_set_tag IS NULL THEN
            vv_row_set_tag := 'rowset';
ELSE
            vv_row_set_tag := pv_row_set_tag;
END IF;

        IF pv_row_tag IS NULL THEN
            vv_row_tag := 'row';
ELSE
            vv_row_tag := pv_row_tag;
END IF;

        -- Oracle metadata, get the list of columns from the select query
        vn_metadataCursorId := DBMS_SQL.open_cursor;
        DBMS_SQL.parse (vn_metadataCursorId, vv_query_text_io, DBMS_SQL.native);
        vn_metadataExecute := DBMS_SQL.EXECUTE (vn_metadataCursorId);
        DBMS_SQL.describe_columns (vn_metadataCursorId, vn_col_count, vt_metadataTab);
        IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
          DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
END IF;

        -- loop inside columns metadata and construct the SQL query for XMLELEMENT's
        vn_colnum := vt_metadataTab.FIRST;
        IF (vn_colnum IS NOT NULL)
        THEN
          vc_xml_query := 'SELECT XMLELEMENT("'||vv_row_set_tag||'",
                               XMLAGG(
                                 XMLELEMENT("'||vv_row_tag||'",<xml_elements>)
                               )
                    )
            FROM (
                SELECT rownum row_number, t.* FROM ('||vv_query_text_io||') t )';

         LOOP
vr_metadata_rec := vt_metadataTab (vn_colnum);
            case_cdata := CASE
                              WHEN vr_metadata_rec.col_type = 12 THEN 'TO_CHAR("'||vr_metadata_rec.col_name||'",'''||vv_date_format||''')'
                              ELSE '"'||vr_metadata_rec.col_name||'"'
END;

            IF v_exist_count_col = 'N' AND vr_metadata_rec.col_name = 'COUNT' THEN
              v_exist_count_col := 'Y';
END IF;

            -- XMLELEMENT sql frangment
            vv_xml_elements := vv_xml_elements                      ||
                                 CHR(10)                            ||
                                 '                                 '||
                                 'XMLELEMENT ("'||lower(vr_metadata_rec.col_name)||'", XMLATTRIBUTES (''' || CASE WHEN vr_metadata_rec.col_type=12 THEN 'DATE' ELSE 'TEXT' END || ''' AS "TYPE"), XMLCDATA ('||case_cdata||')),';
            -- Increment the tab counter
            vn_colnum := vt_metadataTab.NEXT (vn_colnum);
            EXIT WHEN (vn_colnum IS NULL);
END LOOP;
         vv_xml_elements := RTRIM(vv_xml_elements, ',');
END IF;
        vc_xml_query := REPLACE(vc_xml_query, '<xml_elements>',vv_xml_elements);

EXECUTE IMMEDIATE vc_xml_query INTO vx_result;

IF v_exist_count_col = 'Y' AND pv_additional_qry IS NOT NULL THEN
           vv_xml_instance_row := fn_get_xml_instance_row (pv_additional_qry);
           IF vv_xml_instance_row IS NOT NULL THEN
SELECT APPENDCHILDXML(vx_result,
                      'rowset/row',
                      XMLType(vv_xml_instance_row)
       )
INTO vx_result
FROM DUAL;
END IF;
END IF;

        -- Prettify the XML before sending it to client side
RETURN vx_result/*.EXTRACT ('/*')*/.getClobVal(); --Note: Uncommenting .EXTRACT ('/*') will prettify the XML output clob, but a performance bug is encountred on oracle 10g
    EXCEPTION
          WHEN OTHERS
          THEN
            IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
              DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
            END IF;
             sp_error_log ('',
                           'SYSTEM',
                           pkg_s_utility.gl_host_ip,
                           'PKG_ALERT.fn_get_queryresult_as_xml:' || DBMS_UTILITY.format_error_backtrace,
                            SQLCODE,
                            SQLERRM
                            );
             RAISE;
    END;


   FUNCTION fn_get_queryresult_as_json(
                                pv_query_text IN CLOB,
                                pv_date_format IN VARCHAR2 DEFAULT NULL,
                                pv_row_set_tag IN VARCHAR2 DEFAULT 'rowset',
                                pv_row_tag IN VARCHAR2 DEFAULT 'row',
                                max_rows IN NUMBER DEFAULT -1,
                                pv_total_count IN NUMBER DEFAULT -1,
                                pv_additional_qry IN VARCHAR2 DEFAULT NULL)
        RETURN CLOB
    AS
        vn_metadataCursorId   NUMBER;
        vn_metadataExecute    NUMBER;
        vn_col_count          INTEGER;
        vt_metadataTab        DBMS_SQL.desc_tab;
        vn_colnum             NUMBER;
        vr_metadata_rec       DBMS_SQL.desc_rec;
        vc_xml_query          VARCHAR2(32000);
        vv_query_text_io      VARCHAR2(32000);
        vv_xml_elements       VARCHAR2(32000);
        vx_result             CLOB;
        case_cdata            VARCHAR2(32000);
        vv_date_format        VARCHAR2(100);
        vv_row_set_tag        VARCHAR2(100);
        vv_row_tag            VARCHAR2(100);
        v_exist_count_col     VARCHAR2(1) := 'N';
    BEGIN
        vv_query_text_io := pv_query_text;
        -- Remove double cotes to avoid getting error related to case sensitive in column names
        --vv_query_text_io := REPLACE(vv_query_text_io, '"');
        -- Fill in default values
        IF pv_date_format IS NULL THEN
            vv_date_format := 'YYYY-MM-DD';
        ELSE
            vv_date_format := pv_date_format;
        END IF;

        IF pv_row_set_tag IS NULL THEN
            vv_row_set_tag := 'rowset';
        ELSE
            vv_row_set_tag := pv_row_set_tag;
        END IF;

        IF pv_row_tag IS NULL THEN
            vv_row_tag := 'row';
        ELSE
            vv_row_tag := pv_row_tag;
        END IF;

        -- Oracle metadata, get the list of columns from the select query
        vn_metadataCursorId := DBMS_SQL.open_cursor;
        DBMS_SQL.parse (vn_metadataCursorId, vv_query_text_io, DBMS_SQL.native);
        vn_metadataExecute := DBMS_SQL.EXECUTE (vn_metadataCursorId);
        DBMS_SQL.describe_columns (vn_metadataCursorId, vn_col_count, vt_metadataTab);
        IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
          DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
        END IF;

        -- loop inside columns metadata and construct the SQL query for XMLELEMENT's
        vn_colnum := vt_metadataTab.FIRST;
        IF (vn_colnum IS NOT NULL)
        THEN
          vc_xml_query := 'SELECT JSON_OBJECT('''||vv_row_set_tag||''' VALUE
                                 JSON_OBJECT('''||vv_row_tag||''' VALUE JSON_ARRAYAGG (JSON_OBJECT(<xml_elements> RETURNING CLOB) RETURNING CLOB)
                               RETURNING CLOB)
                    RETURNING CLOB)
            FROM (
                SELECT rownum row_number, t.* FROM ('||vv_query_text_io||') t )';

         LOOP
            vr_metadata_rec := vt_metadataTab (vn_colnum);
            case_cdata := CASE
                              WHEN vr_metadata_rec.col_type = 12 THEN 'TO_CHAR("'||vr_metadata_rec.col_name||'",'''||vv_date_format||''')'
                              ELSE CASE WHEN vr_metadata_rec.col_type = 12 THEN 'TO_NUMBER(REPLACE(REPLACE("'||vr_metadata_rec.col_name||'", '','', ''.''), '' '', ''''), ''9999999999999999999999999990D9999'', ''NLS_NUMERIC_CHARACTERS = ''. '')'
                                   ELSE
                                    '"'||vr_metadata_rec.col_name||'"'
                                   END
                          END;

            IF v_exist_count_col = 'N' AND vr_metadata_rec.col_name = 'COUNT' THEN
              v_exist_count_col := 'Y';
            END IF;

            -- XMLELEMENT sql frangment
            vv_xml_elements := vv_xml_elements                      ||
                                 CHR(10)                            ||
                                 '                                 '||
                                 ''''||lower(vr_metadata_rec.col_name)||''' VALUE JSON_OBJECT(''TYPE'' VALUE ''' ||
                                 CASE WHEN vr_metadata_rec.col_type=12 THEN 'DATE'
                                 ELSE CASE WHEN vr_metadata_rec.col_type=2 THEN 'NUMBER'
                                      ELSE 'TEXT'
                                      END
                                 END || ''', ''content'' VALUE '||case_cdata||'),';
            -- Increment the tab counter
            vn_colnum := vt_metadataTab.NEXT (vn_colnum);
            EXIT WHEN (vn_colnum IS NULL);
         END LOOP;

         IF v_exist_count_col = 'Y' AND pv_additional_qry IS NOT NULL THEN
           vv_xml_elements := vv_xml_elements || ' ''INSTANCE'' VALUE (' || fn_get_json_instance_row (pv_additional_qry) || ')';
         END IF;

         vv_xml_elements := RTRIM(vv_xml_elements, ',');
        END IF;
        vc_xml_query := REPLACE(vc_xml_query, '<xml_elements>',vv_xml_elements);

        EXECUTE IMMEDIATE vc_xml_query INTO vx_result;

        RETURN vx_result;
    EXCEPTION
          WHEN OTHERS
          THEN
            IF DBMS_SQL.IS_OPEN(vn_metadataCursorId) THEN
              DBMS_SQL.CLOSE_CURSOR(vn_metadataCursorId);
            END IF;
             sp_error_log ('',
                           'SYSTEM',
                           pkg_s_utility.gl_host_ip,
                           'PKG_ALERT.fn_get_queryresult_as_json:' || DBMS_UTILITY.format_error_backtrace,
                            SQLCODE,
                            SQLERRM
                            );
             RAISE;
    END;

    FUNCTION FN_IS_ROW_HIGHLIGHTED (P_FACILITY_ID   P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                    P_HOST_ID       P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                    P_ENTITY_ID     P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                    P_CURRENCY_CODE P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                    P_VALUE_DATE    DATE,
                                    P_USER_ID       S_USERS.USER_ID%TYPE,
                                    P_CCY_THRESHOLD VARCHAR2 DEFAULT 'N',
                                    P_ACCOUNT_ID    P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                    P_MOVEMENT_ID   P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                    P_MATCH_ID      P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                    P_ILM_GROUP_ID  P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                    P_ROLE_ID       S_ROLE.ROLE_ID%TYPE DEFAULT NULL)
    RETURN VARCHAR2
    IS
        V_GUI_HIGHLIGHT     VARCHAR2(1);
        V_ROLE_ID           S_USERS.ROLE_ID%TYPE := P_ROLE_ID;
        V_REQUIRES_SCENARIO_INSTANCE  P_SCENARIO_GUI_ALERT_FACILITY.REQUIRES_SCENARIO_INSTANCE%TYPE;
    BEGIN
        -- Get user's role if p_role_id is not supplied
        IF V_ROLE_ID IS NULL THEN
          BEGIN
          SELECT ROLE_ID
            INTO V_ROLE_ID
            FROM S_USERS
           WHERE USER_ID = P_USER_ID;
          EXCEPTION
              WHEN OTHERS THEN
                  V_ROLE_ID := NULL;
          END;
        END IF;

        -- Is this facility requires scenario instance
        BEGIN
            SELECT REQUIRES_SCENARIO_INSTANCE
              INTO V_REQUIRES_SCENARIO_INSTANCE
              FROM P_SCENARIO_GUI_ALERT_FACILITY
             WHERE ID = P_FACILITY_ID;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                V_REQUIRES_SCENARIO_INSTANCE := 'N';
        END;
        -- If exists data in p_scenario_counts for a critical scenario (P_SCENARIO.CRITICAL_GUI_HIGHLIGHT)
        -- Then return C
        -- If exists data for a non-critical scenario then return Y
        -- If no data in p_scenario_counts then return N
        IF V_REQUIRES_SCENARIO_INSTANCE = 'N' THEN
        SELECT DECODE(MAX(DECODE(CRITICAL_GUI_HIGHLIGHT,NULL,0, 'Y', 2, 1)),2, 'C', 1, 'Y', 'N')
          INTO V_GUI_HIGHLIGHT
        FROM (
                SELECT PS.SCENARIO_ID,
                       NVL(PS.CRITICAL_GUI_HIGHLIGHT,'N') CRITICAL_GUI_HIGHLIGHT,
                       PS.RECORD_SCENARIO_INSTANCES
                  FROM P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO PS ON (PS.SCENARIO_ID = GAM.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_COUNTS PSC ON (PS.SCENARIO_ID = PSC.SCENARIO_ID)
                 WHERE GAF.ID = P_FACILITY_ID
                   AND PS.ACTIVE_FLAG = 'Y'
                   AND (SCENARIO_COUNT_OVER_T > 0 OR P_CCY_THRESHOLD = 'N')
                       -- Attributes required by the facility
                   AND PSC.HOST_ID = P_HOST_ID
                   AND PSC.ENTITY_ID = P_ENTITY_ID
                   AND PSC.CURRENCY_CODE = SUBSTR(P_CURRENCY_CODE, 1, 3)
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, V_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) != '2'
                   AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, V_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
                 GROUP BY PS.SCENARIO_ID, PS.CRITICAL_GUI_HIGHLIGHT, PS.RECORD_SCENARIO_INSTANCES
                 );
        ELSE
              SELECT DECODE(MAX(DECODE(CRITICAL_GUI_HIGHLIGHT,NULL,0, 'Y', 2, 1)),2, 'C', 1, 'Y', 'N')
                INTO V_GUI_HIGHLIGHT
                FROM (
                SELECT P_SCENARIO.SCENARIO_ID,
                       NVL(P_SCENARIO.CRITICAL_GUI_HIGHLIGHT,'N') CRITICAL_GUI_HIGHLIGHT,
                       P_SCENARIO.RECORD_SCENARIO_INSTANCES
                  FROM P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)
                       INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)
                 WHERE GAF.ID = P_FACILITY_ID
                   AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                   AND (PSI.OVER_THRESHOLD = 'Y' OR P_CCY_THRESHOLD = 'N')
                       -- Attributes required by the facility
                   AND NVL(P_HOST_ID, 'All') IN ('All', PSI.HOST_ID)
                   AND NVL(P_ENTITY_ID, 'All') IN ('All', PSI.ENTITY_ID)
                   AND NVL(SUBSTR(P_CURRENCY_CODE, 1, 3), 'All') IN ('All', PSI.CURRENCY_CODE)
                   AND (PSI.VALUE_DATE = P_VALUE_DATE OR P_VALUE_DATE IS NULL)
                   AND (PSI.ACCOUNT_ID = P_ACCOUNT_ID OR P_ACCOUNT_ID IS NULL)
                   AND (PSI.MOVEMENT_ID = P_MOVEMENT_ID OR P_MOVEMENT_ID IS NULL)
                   AND (PSI.MATCH_ID = P_MATCH_ID OR P_MATCH_ID IS NULL)
                   AND PSI.STATUS IN ('A','P','O')
                   --AND ((PSI.HOST_ID,PSI.ENTITY_ID,PSI.ACCOUNT_ID) IN (SELECT HOST_ID,ENTITY_ID,ACCOUNT_ID FROM TABLE(PKG_ILM.FN_GET_ILM_ACC_IN_GROUP(P_ILM_GROUP_ID))) OR NVL(P_ILM_GROUP_ID, 'All') = 'All')
                   AND (PSI.OTHER_ID = P_ILM_GROUP_ID OR (PSI.OTHER_ID IS NULL OR P_ILM_GROUP_ID IS NULL))
                   -- check user's access for entity/currency and scenario
                   AND (   (P_USER_ID IS NULL AND P_ROLE_ID IS NULL)
                        OR
                           (    PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, V_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) != '2'
                            AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSI.SCENARIO_ID, PSI.HOST_ID, V_ROLE_ID, PSI.ENTITY_ID), 1,1) = 'Y')
                        )
                 GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES
             );
        END IF;

        RETURN V_GUI_HIGHLIGHT;
    END;

    FUNCTION FN_GET_HIGHLIGHTING_DETAILS (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                          P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                          P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                          P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                          P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                          P_USER_ID         S_USERS.USER_ID%TYPE,
                                          P_CALL_OPTION     VARCHAR2,
                                          P_SELECTED_TAB    P_CATEGORY.DISPLAY_TAB%TYPE DEFAULT 1,
                                          P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                          P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                          PV_INS_STATUS     VARCHAR2 DEFAULT NULL,
                                          P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                          P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                          P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                          P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                          P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                          P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                          P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL)
    RETURN CLOB --TAB_SCEN_HIGHLIGHT PIPELINED
    IS
        V_SCENARIO_ID                 P_SCENARIO.SCENARIO_ID%TYPE;
        V_RECORD_SCENARIO_INSTANCES   P_SCENARIO.RECORD_SCENARIO_INSTANCES%TYPE;
        V_CUSTOM_TREE_LEVEL1          P_SCENARIO.CUSTOM_TREE_LEVEL1%TYPE;
        V_CUSTOM_TREE_LEVEL2          P_SCENARIO.CUSTOM_TREE_LEVEL2%TYPE;
        V_ROLE_ID                     S_USERS.ROLE_ID%TYPE;
        V_LIST_GROUP_COL              P_SCENARIO_GUI_ALERT_FACILITY.GROUP_COLUMNS%TYPE;
        V_SQL                         CLOB;
        V_SQL_PARMS                   CLOB;
        CUR_SCEN_HIGHLIGHT            SYS_REFCURSOR;
        V_SCEN_DETAILS_XML            CLOB;
        V_REQUIRES_SCENARIO_INSTANCE  P_SCENARIO_GUI_ALERT_FACILITY.REQUIRES_SCENARIO_INSTANCE%TYPE;
        V_HOST_COL                    P_SCENARIO.SEC_HOST_COL%TYPE;
        V_ENTITY_COL                  P_SCENARIO.SEC_ENTITY_COL%TYPE;
        V_CURRENCY_COL                P_SCENARIO.SEC_CURRENCY_COL%TYPE;
        V_ACCOUNT_ID_COL              P_SCENARIO.ACCOUNT_ID_COL%TYPE;
        V_VALUE_DATE_COL              P_SCENARIO.VALUE_DATE_COL%TYPE;
        V_MOVEMENT_ID_COL             P_SCENARIO.MOVEMENT_ID_COL%TYPE;
        V_MATCH_ID_COL                P_SCENARIO.MATCH_ID_COL%TYPE;
        V_SWEEP_ID_COL                P_SCENARIO.SWEEP_ID_COL%TYPE;
        V_PAYMENT_ID_COL              P_SCENARIO.PAYMENT_ID_COL%TYPE;
        V_OTHER_ID_COL                P_SCENARIO.OTHER_ID_COL%TYPE;
        V_GROUP_COLUMNS               VARCHAR2(500);
        V_SQL_INSTANCE                VARCHAR2(10000);
    BEGIN
        -- Get user's role
        BEGIN
        SELECT ROLE_ID--, DECODE (DATE_FORMAT , 1, 'DD/MM/RRRR', 2, 'MM/DD/RRRR', NULL, 'DD/MM/RRRR')
          INTO V_ROLE_ID--, V_DATE_FORMAT
          FROM S_USERS
         WHERE USER_ID = P_USER_ID;
        EXCEPTION
            WHEN OTHERS THEN
                V_ROLE_ID := NULL;
                --V_DATE_FORMAT := 'DD/MM/RRRR';
        END;

        -- Get group columns to be used for grouping results
        BEGIN
            SELECT GROUP_COLUMNS, REQUIRES_SCENARIO_INSTANCE
              INTO V_LIST_GROUP_COL, V_REQUIRES_SCENARIO_INSTANCE
              FROM P_SCENARIO_GUI_ALERT_FACILITY
             WHERE ID = P_FACILITY_ID;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                V_LIST_GROUP_COL := NULL;
                V_REQUIRES_SCENARIO_INSTANCE := 'N';
        END;

        -- All for Wokflow_monitor and scenario_instance_monitor
        IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) OR V_REQUIRES_SCENARIO_INSTANCE = 'N'
        THEN
          V_REQUIRES_SCENARIO_INSTANCE := 'A';
        END IF;

        -- Get list of scenarios
        V_SQL_PARMS := q'[WITH PARMS AS (SELECT ']' ||P_FACILITY_ID|| q'[' P_FACILITY_ID,']' ||
                      P_HOST_ID || q'[' P_HOST_ID,']' ||
                      P_ENTITY_ID || q'[' P_ENTITY_ID,']' ||
                      P_CURRENCY_CODE || q'[' P_CURRENCY_CODE,]' ||
                      'TO_DATE(''' || TO_CHAR(P_VALUE_DATE, 'YYYY-MM-DD') || q'[', 'YYYY-MM-DD') P_VALUE_DATE,']' ||
                      V_ROLE_ID || q'[' P_ROLE_ID,']' ||
                      P_SELECTED_TAB || q'[' P_SELECTED_TAB, ']' ||
                      P_CCY_THRESHOLD || q'[' P_CCY_THRESHOLD,']' ||
                      P_ACCOUNT_ID || q'[' P_ACCOUNT_ID,]' ||
                      NVL(TO_CHAR(P_MOVEMENT_ID), 'NULL') || q'[ P_MOVEMENT_ID,]' ||
                      NVL(TO_CHAR(P_MATCH_ID), 'NULL') || q'[ P_MATCH_ID,']' ||
                      P_ILM_GROUP_ID || q'[' P_ILM_GROUP_ID,]' ||
                      NVL(TO_CHAR(P_SWEEP_ID), 'NULL') || q'[ P_SWEEP_ID, ]' ||
                      NVL(TO_CHAR(P_PAYMENT_ID), 'NULL') || q'[ P_PAYMENT_ID]' ||
             q'[ FROM DUAL)]';

        IF V_REQUIRES_SCENARIO_INSTANCE = 'Y' THEN
          V_SQL := V_SQL_PARMS || q'[
          SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                 SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
            FROM PARMS CROSS JOIN ]' ||
            CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
            THEN
              q'[ P_SCENARIO ]'
            ELSE
              q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                 INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                 INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
            END ||
            q'[
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
            CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
             q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
            END ||
            q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
            CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
            THEN
              q'[ WHERE ]'
            ELSE
              q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
            END ||
            q'[ (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')]' ||
            CASE WHEN NVL(P_HOST_ID, 'All') <> 'All'       THEN q'[ AND PARMS.P_HOST_ID = PSI.HOST_ID]' END ||
            CASE WHEN NVL(P_ENTITY_ID, 'All') <> 'All'     THEN q'[ AND PARMS.P_ENTITY_ID = PSI.ENTITY_ID]' END ||
            CASE WHEN NVL(P_CURRENCY_CODE, 'All') <> 'All' THEN q'[ AND PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE]' END ||
            CASE WHEN P_VALUE_DATE IS NOT NULL             THEN q'[ AND PARMS.P_VALUE_DATE = PSI.VALUE_DATE]' END ||
            CASE WHEN NVL(P_ACCOUNT_ID, 'All') <> 'All'    THEN q'[ AND PARMS.P_ACCOUNT_ID = PSI.ACCOUNT_ID]' END ||
            CASE WHEN NVL(P_MOVEMENT_ID, -1) <> -1   THEN q'[ AND PARMS.P_MOVEMENT_ID = PSI.MOVEMENT_ID]' END ||
            CASE WHEN NVL(P_MATCH_ID, -1) <> -1      THEN q'[ AND PARMS.P_MATCH_ID = PSI.MATCH_ID]' END ||
            CASE WHEN NVL(P_ILM_GROUP_ID, 'All') <> 'All'  THEN q'[ AND PARMS.P_ILM_GROUP_ID = PSI.OTHER_ID]' END ||
            CASE WHEN NVL(P_SWEEP_ID, -1) <> -1      THEN q'[ AND PARMS.P_SWEEP_ID = PSI.SWEEP_ID]' END ||
            CASE WHEN NVL(P_PAYMENT_ID, -1) <> -1    THEN q'[ AND PARMS.P_PAYMENT_ID = PSI.PAYMENT_ID]' END ||
             CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
               q'[ AND PSI.STATUS IN ('A','P','O') ]'
             END ||
             q'[
             -- check user's access for entity/currency and scenario
             AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
             AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
             AND P_SCENARIO.ACTIVE_FLAG = 'Y'
             AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
            ]' ||
            CASE WHEN PV_INS_STATUS IS NULL THEN
              q'[ AND PSI.STATUS IN ('A','P','O') ]'
            ELSE
            q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
            END ||
            CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
              q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
            END;
        ELSE
          IF V_REQUIRES_SCENARIO_INSTANCE <> 'N' THEN
            V_SQL := V_SQL_PARMS || q'[
              SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                     SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
                FROM PARMS CROSS JOIN ]' ||
                CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                THEN
                  q'[ P_SCENARIO ]'
                ELSE
                  q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                     INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                     INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                END ||
                q'[
                     INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
                CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                 q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                END ||
                q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                THEN
                  q'[ WHERE ]'
                ELSE
                  q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                END ||
                q'[ (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')]' ||
                CASE WHEN NVL(P_HOST_ID, 'All') <> 'All'       THEN q'[ AND PARMS.P_HOST_ID = PSI.HOST_ID]' END ||
                CASE WHEN NVL(P_ENTITY_ID, 'All') <> 'All'     THEN q'[ AND PARMS.P_ENTITY_ID = PSI.ENTITY_ID]' END ||
                CASE WHEN NVL(P_CURRENCY_CODE, 'All') <> 'All' THEN q'[ AND PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE]' END ||
                CASE WHEN P_VALUE_DATE IS NOT NULL             THEN q'[ AND PARMS.P_VALUE_DATE = PSI.VALUE_DATE]' END ||
                CASE WHEN NVL(P_ACCOUNT_ID, 'All') <> 'All'    THEN q'[ AND PARMS.P_ACCOUNT_ID = PSI.ACCOUNT_ID]' END ||
                CASE WHEN NVL(P_MOVEMENT_ID, -1) <> -1   THEN q'[ AND PARMS.P_MOVEMENT_ID = PSI.MOVEMENT_ID]' END ||
                CASE WHEN NVL(P_MATCH_ID, -1) <> -1      THEN q'[ AND PARMS.P_MATCH_ID = PSI.MATCH_ID]' END ||
                CASE WHEN NVL(P_ILM_GROUP_ID, 'All') <> 'All'  THEN q'[ AND PARMS.P_ILM_GROUP_ID = PSI.OTHER_ID]' END ||
                CASE WHEN NVL(P_SWEEP_ID, -1) <> -1      THEN q'[ AND PARMS.P_SWEEP_ID = PSI.SWEEP_ID]' END ||
                CASE WHEN NVL(P_PAYMENT_ID, -1) <> -1    THEN q'[ AND PARMS.P_PAYMENT_ID = PSI.PAYMENT_ID]' END ||
                 CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                   q'[ AND PSI.STATUS IN ('A','P','O') ]'
                 END ||
                 q'[
                 -- check user's access for entity/currency and scenario
                 AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                 AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                 AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                 AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
              ]' ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                  q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                     THEN 'Y'
                     ELSE 'N' END = 'Y']'
               END ||
               CASE WHEN PV_INS_STATUS IS NULL THEN
                  q'[ AND PSI.STATUS IN ('A','P','O') ]'
                ELSE
                q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
              END ||
               CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                 q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
               END ||
                q'[ UNION
                 SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                        SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
                  FROM PARMS
                       CROSS JOIN ]' ||
                       CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                       THEN
                        q'[ P_SCENARIO ]'
                       ELSE
                        q'[P_SCENARIO_GUI_ALERT_FACILITY GAF
                       INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                       INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                       END ||
                      q'[ INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)]' ||
                      q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                      CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                      THEN
                        q'[  WHERE ]'
                      ELSE
                        q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                      END ||
                  q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
                   AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
                   AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
                   AND NVL(P_SCENARIO.RECORD_SCENARIO_INSTANCES, 'N') = 'N'
                   -- Do not display zeroes
                   AND PSC.SCENARIO_COUNT > 0
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
                   AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
                   AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                   AND P_SCENARIO.ACTIVE_FLAG = 'Y'
                   AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
                   CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                      q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END = 'Y']'
                   END || q'[ ORDER BY 1,2,3,4]';
          ELSE
            V_SQL := V_SQL_PARMS || q'[
             SELECT DISTINCT P_SCENARIO.SCENARIO_ID, P_SCENARIO.RECORD_SCENARIO_INSTANCES, P_SCENARIO.CUSTOM_TREE_LEVEL1, P_SCENARIO.CUSTOM_TREE_LEVEL2,
                    SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, ACCOUNT_ID_COL, VALUE_DATE_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL, OTHER_ID_COL
              FROM PARMS
                   CROSS JOIN ]' ||
                   CASE WHEN (P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                         ) THEN
                    q'[ P_SCENARIO ]'
                   ELSE
                    q'[P_SCENARIO_GUI_ALERT_FACILITY GAF
                   INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                   INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                   END ||
                  q'[ INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)
                  LEFT JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID)]' ||
                  CASE WHEN (P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                         ) THEN
                    q'[  WHERE ]'
                  ELSE
                    q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                  END ||
              q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
               AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
               AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
               -- Do not display zeroes
               AND PSC.SCENARIO_COUNT > 0
               -- check user's access for entity/currency and scenario
               AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSC.SCENARIO_ID, PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID), 1,1) = 'Y'
               AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
               AND P_SCENARIO.ACTIVE_FLAG = 'Y'
               AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                  q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                     THEN 'Y'
                     ELSE 'N' END = 'Y']'
               END || q'[ ORDER BY 1,2,3,4]';
          END IF;
        END IF;
        OPEN CUR_SCEN_HIGHLIGHT FOR V_SQL;

        LOOP
            FETCH CUR_SCEN_HIGHLIGHT INTO V_SCENARIO_ID, V_RECORD_SCENARIO_INSTANCES, V_CUSTOM_TREE_LEVEL1, V_CUSTOM_TREE_LEVEL2,
                  V_HOST_COL, V_ENTITY_COL, V_CURRENCY_COL, V_ACCOUNT_ID_COL, V_VALUE_DATE_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_SWEEP_ID_COL, V_PAYMENT_ID_COL, V_OTHER_ID_COL;
            EXIT WHEN CUR_SCEN_HIGHLIGHT%NOTFOUND;

            V_GROUP_COLUMNS := NULL;

            IF V_RECORD_SCENARIO_INSTANCES = 'Y' THEN

              IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                 AND INSTR (NVL(REPLACE(V_GROUP_COLUMNS,'"'), ' '), 'ENTITY_ID') = 0
                 AND INSTR (NVL(REPLACE(V_LIST_GROUP_COL,'"'), ' '), 'CURRENCY_CODE') = 0
              THEN
                 V_GROUP_COLUMNS :=  V_GROUP_COLUMNS || ',ENTITY_ID';
              END IF;

              IF P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                 AND INSTR (NVL(REPLACE(V_GROUP_COLUMNS,'"'), ' '), 'CURRENCY_CODE') = 0
                 AND INSTR (NVL(REPLACE(V_LIST_GROUP_COL,'"'), ' '), 'CURRENCY_CODE') = 0
              THEN
                 V_GROUP_COLUMNS :=  V_GROUP_COLUMNS || ',CURRENCY_CODE';
              END IF;

              IF V_CUSTOM_TREE_LEVEL1 IS NOT NULL
                 AND INSTR(',' ||REPLACE(V_GROUP_COLUMNS,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL1,'"')||',')=0
                 AND INSTR(',' ||REPLACE(V_LIST_GROUP_COL,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL1,'"')||',')=0
              THEN
                V_GROUP_COLUMNS := V_GROUP_COLUMNS || ',' || V_CUSTOM_TREE_LEVEL1;
              END IF;

              IF V_CUSTOM_TREE_LEVEL2 IS NOT NULL
                AND INSTR(',' ||REPLACE(V_GROUP_COLUMNS,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL2,'"')||',')=0
                AND INSTR(',' ||REPLACE(V_LIST_GROUP_COL,'"')||',',',' ||REPLACE(V_CUSTOM_TREE_LEVEL2,'"')||',')=0
              THEN
                V_GROUP_COLUMNS := V_GROUP_COLUMNS || ',' || V_CUSTOM_TREE_LEVEL2;
              END IF;

              IF V_LIST_GROUP_COL IS NOT NULL
              THEN
                V_GROUP_COLUMNS :=  ',' || V_LIST_GROUP_COL || V_GROUP_COLUMNS;
              END IF;

            END IF;

            IF V_REQUIRES_SCENARIO_INSTANCE IN ('A','Y') AND V_RECORD_SCENARIO_INSTANCES = 'Y' THEN
              V_SQL := V_SQL_PARMS || q'[
                  SELECT P_SCENARIO.SCENARIO_ID,
                         CAT.TITLE TITLE_CAT,
                         CAT.DISPLAY_ORDER DISP_ORDER_CAT,
                         CAT.DESCRIPTION DESC_CAT,
                         P_SCENARIO.DISPLAY_ORDER DISP_ORDER_SCEN,
                         P_SCENARIO.TITLE TITLE_SCEN,
                         PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
                        (CASE WHEN
                             (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,P_SCENARIO.START_TIME, P_SCENARIO.END_TIME)='Y')
                             AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                             AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')
                             THEN ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                             ELSE GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END) AS TIME_TO_CUT_OFF,
                         CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END AS IsScenarioAlertable,
                         P_SCENARIO.DESCRIPTION DESC_SCEN,
                         P_SCENARIO.RECORD_SCENARIO_INSTANCES,
                         P_SCENARIO.CATEGORY_ID ]' || CHR(10) ||
                         V_GROUP_COLUMNS ||
                    q'[, q'[]' || LTRIM(V_GROUP_COLUMNS, ',') ||
                    q'<]' AS GROUP_COLUMNS >' ||
                    q'[, CASE WHEN P_CCY_THRESHOLD = 'Y'
                                   THEN SUM(CASE WHEN NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' THEN 1 ELSE 0 END)
                              ELSE COUNT(*)
                         END COUNT
                    FROM PARMS CROSS JOIN ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ P_SCENARIO ]'
                    ELSE
                      q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                    END ||
                  q'[ INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
                    CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                     q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                    END ||
                  q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ WHERE ]'
                    ELSE
                      q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                    END ||
                     CASE WHEN V_HOST_COL IS NOT NULL AND NVL(P_HOST_ID,'All') <> 'All' THEN q'[ PARMS.P_HOST_ID = PSI.HOST_ID AND ]' END ||
                     CASE WHEN V_ENTITY_COL IS NOT NULL AND NVL(P_ENTITY_ID,'All') <> 'All' THEN q'[ PARMS.P_ENTITY_ID = PSI.ENTITY_ID AND ]' END ||
                     CASE WHEN V_CURRENCY_COL IS NOT NULL AND NVL(P_CURRENCY_CODE,'All') <> 'All' THEN q'[ PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE AND ]' END ||
                     CASE WHEN V_ACCOUNT_ID_COL IS NOT NULL AND NVL(P_ACCOUNT_ID,'All') <> 'All' THEN q'[ NVL(PSI.ACCOUNT_ID,PARMS.P_ACCOUNT_ID) = PARMS.P_ACCOUNT_ID AND ]' END ||
                     CASE WHEN V_VALUE_DATE_COL IS NOT NULL AND P_VALUE_DATE IS NOT NULL THEN q'[ NVL(PSI.VALUE_DATE, PARMS.P_VALUE_DATE) = PARMS.P_VALUE_DATE AND ]' END ||
                     CASE WHEN V_MOVEMENT_ID_COL IS NOT NULL AND NVL(P_MOVEMENT_ID,-1) <> -1 THEN q'[ PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID AND ]' END ||
                     CASE WHEN V_MATCH_ID_COL IS NOT NULL AND NVL(P_MATCH_ID,-1) <> -1 THEN q'[ PSI.MATCH_ID = PARMS.P_MATCH_ID AND ]' END ||
                     CASE WHEN V_OTHER_ID_COL IS NOT NULL AND NVL(P_ILM_GROUP_ID,'All') <> 'All' THEN q'[ PSI.OTHER_ID = PARMS.P_ILM_GROUP_ID AND ]' END ||
                     CASE WHEN V_SWEEP_ID_COL IS NOT NULL AND NVL(P_SWEEP_ID,-1) <> -1 THEN q'[ PSI.SWEEP_ID = PARMS.P_SWEEP_ID AND ]' END ||
                     CASE WHEN V_PAYMENT_ID_COL IS NOT NULL AND NVL(P_PAYMENT_ID,-1) <> -1 THEN q'[ PSI.PAYMENT_ID = PARMS.P_PAYMENT_ID  AND ]' END ||
                     q'[ PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                     AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                     AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
                     ]' ||
                     CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                       q'[ AND PSI.STATUS IN ('A','P','O') ]'
                     END ||
                     q'[
                     AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                     CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                        q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                           THEN 'Y'
                           ELSE 'N' END = 'Y']'
                     END ||
                     CASE WHEN PV_INS_STATUS IS NULL THEN
                        q'[ AND PSI.STATUS IN ('A','P','O') ]'
                      ELSE
                     q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
                   END ||
                     CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                        q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
                     END ||
                   q'[ GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_CCY_THRESHOLD,P_SCENARIO.CATEGORY_ID,
                       CAT.TITLE, CAT.DISPLAY_ORDER, CAT.DESCRIPTION, P_SCENARIO.DISPLAY_ORDER, P_SCENARIO.TITLE,
                       P_SCENARIO.DESCRIPTION, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_SCENARIO.START_TIME,P_SCENARIO.END_TIME,PSI.SCENARIO_ID, PSI.HOST_ID, PSI.ENTITY_ID,PARMS.P_ROLE_ID
                       ]' ||
                   V_GROUP_COLUMNS ||
                   q'[ HAVING CASE WHEN P_CCY_THRESHOLD = 'Y'
                                   THEN SUM(CASE WHEN NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' THEN 1 ELSE 0 END)
                              ELSE COUNT(*)
                         END > 0 ORDER BY 1,2,3,4]';

              V_SQL_INSTANCE := V_SQL_PARMS || q'[
              SELECT PSI.ID
                FROM PARMS CROSS JOIN ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ P_SCENARIO ]'
                    ELSE
                      q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                    END ||
                  q'[ INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID) ]' ||
                    CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                     q'[ INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
                    END ||
                  q'[ LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                    CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                    THEN
                      q'[ WHERE ]'
                    ELSE
                      q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                    END ||
                     CASE WHEN V_HOST_COL IS NOT NULL AND NVL(P_HOST_ID,'All') <> 'All' THEN q'[ PARMS.P_HOST_ID = PSI.HOST_ID AND ]' END ||
                     CASE WHEN V_ENTITY_COL IS NOT NULL AND NVL(P_ENTITY_ID,'All') <> 'All' THEN q'[ PARMS.P_ENTITY_ID = PSI.ENTITY_ID AND ]' END ||
                     CASE WHEN V_CURRENCY_COL IS NOT NULL AND NVL(P_CURRENCY_CODE,'All') <> 'All' THEN q'[ PARMS.P_CURRENCY_CODE = PSI.CURRENCY_CODE AND ]' END ||
                     CASE WHEN V_ACCOUNT_ID_COL IS NOT NULL AND NVL(P_ACCOUNT_ID,'All') <> 'All' THEN q'[ NVL(PSI.ACCOUNT_ID,PARMS.P_ACCOUNT_ID) = PARMS.P_ACCOUNT_ID AND ]' END ||
                     CASE WHEN V_VALUE_DATE_COL IS NOT NULL AND P_VALUE_DATE IS NOT NULL THEN q'[ NVL(PSI.VALUE_DATE, PARMS.P_VALUE_DATE) = PARMS.P_VALUE_DATE AND ]' END ||
                     CASE WHEN V_MOVEMENT_ID_COL IS NOT NULL AND NVL(P_MOVEMENT_ID,-1) <> -1 THEN q'[ PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID AND ]' END ||
                     CASE WHEN V_MATCH_ID_COL IS NOT NULL AND NVL(P_MATCH_ID,-1) <> -1 THEN q'[ PSI.MATCH_ID = PARMS.P_MATCH_ID AND ]' END ||
                     CASE WHEN V_SWEEP_ID_COL IS NOT NULL AND NVL(P_SWEEP_ID,-1) <> -1 THEN q'[ PSI.SWEEP_ID = PARMS.P_SWEEP_ID AND ]' END ||
                     CASE WHEN V_PAYMENT_ID_COL IS NOT NULL AND NVL(P_PAYMENT_ID,-1) <> -1 THEN q'[ PSI.PAYMENT_ID = PARMS.P_PAYMENT_ID  AND ]' END ||
                     q'[ PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) IN ('0','1')
                     AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                     AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSI.SCENARIO_ID,PSI.HOST_ID,PARMS.P_ROLE_ID,PSI.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_'
                     ]' ||
                     CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                       q'[ AND PSI.STATUS IN ('A','P','O') ]'
                     END ||
                     q'[
                     AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                     CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                        q'[ AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                           THEN 'Y'
                           ELSE 'N' END = 'Y']'
                     END ||
                     CASE WHEN PV_INS_STATUS IS NULL THEN
                        q'[ AND PSI.STATUS IN ('A','P','O') ]'
                      ELSE
                     q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
                   END ||
                     CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                        q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
                     END;
            ELSE

                 V_SQL := V_SQL_PARMS || q'[
                 SELECT P_SCENARIO.SCENARIO_ID,
                        CAT.TITLE TITLE_CAT,
                        CAT.DISPLAY_ORDER DISP_ORDER_CAT,
                        CAT.DESCRIPTION DESC_CAT,
                        P_SCENARIO.DISPLAY_ORDER DISP_ORDER_SCEN,
                        P_SCENARIO.TITLE TITLE_SCEN,
                        PKG_ALERT.FN_CONV_SECS_TO_DUR_STRING
                        (CASE WHEN
                             (PKG_ALERT.FN_TEST_START_END_TIME(GLOBAL_VAR.SYS_DATE,P_SCENARIO.START_TIME, P_SCENARIO.END_TIME)='Y')
                             AND (TO_TIMESTAMP(END_TIME,'HH24:MI') < TO_TIMESTAMP(start_time,'hh24:mi'))
                             AND TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI') > TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')
                             THEN
                                 ABS(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI')+ 1 - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')))
                             ELSE
                                     GREATEST(PKG_ALERT.FN_CONV_INTERVAL_TO_SECS(TO_TIMESTAMP(P_SCENARIO.END_TIME,'HH24:MI') - TO_TIMESTAMP(TO_CHAR(systimestamp,'HH24:MI'),'HH24:MI')),0)
                         END) AS TIME_TO_CUT_OFF,
                         CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END AS IsScenarioAlertable,
                         P_SCENARIO.DESCRIPTION DESC_SCEN,
                        P_SCENARIO.RECORD_SCENARIO_INSTANCES,
                        P_SCENARIO.CATEGORY_ID
                       ]' ||
                  q'[, NULL AS GROUP_COLUMNS ]' ||
                  q'[, CASE WHEN P_CCY_THRESHOLD = 'Y'
                                 THEN SUM(PSC.SCENARIO_COUNT_OVER_T)
                            ELSE SUM (PSC.SCENARIO_COUNT)
                       END COUNT
                  FROM PARMS
                       CROSS JOIN ]' ||
                  CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                     q'[ P_SCENARIO ]'
                  ELSE
                     q'[ P_SCENARIO_GUI_ALERT_FACILITY GAF
                         INNER JOIN  (SELECT GUI_FACILITY_ID, SCENARIO_ID FROM P_SCENARIO_GUI_ALERT_MAPPING) GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                         INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
                  END ||
                  q'[
                       INNER JOIN P_SCENARIO_COUNTS PSC ON (P_SCENARIO.SCENARIO_ID = PSC.SCENARIO_ID)
                       LEFT OUTER JOIN P_CATEGORY CAT ON (CAT.CATEGORY_ID = P_SCENARIO.CATEGORY_ID) ]' ||
                  CASE WHEN P_FACILITY_ID IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                    q'[ WHERE ]'
                  ELSE
                    q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
                  END ||
                  CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY)
                  THEN
                    q'[ NVL(GAF.REQUIRES_SCENARIO_INSTANCE, 'N') = 'N' AND]'
                  END ||
                  q'[  (NVL(PARMS.P_HOST_ID, 'All') IN ('All', PSC.HOST_ID) OR PSC.HOST_ID = 'All')
                   AND (NVL(PARMS.P_ENTITY_ID, 'All') IN ('All', PSC.ENTITY_ID) OR PSC.ENTITY_ID = 'All')
                   AND (NVL(PARMS.P_CURRENCY_CODE, 'All') IN ('All', PSC.CURRENCY_CODE) OR PSC.CURRENCY_CODE = 'All')
                   -- Do not display zeroes
                   AND PSC.SCENARIO_COUNT > 0
                   -- check user's access for entity/currency and scenario
                   AND PK_APPLICATION.FNGETCURRENCYACCESS(PSC.HOST_ID, PARMS.P_ROLE_ID, PSC.ENTITY_ID, PSC.CURRENCY_CODE) IN ('0','1')
                   AND CAT.DISPLAY_TAB = NVL(PARMS.P_SELECTED_TAB, CAT.DISPLAY_TAB)
                   AND PKG_ALERT.FN_GET_SCENARIO_ACCESS (PSC.SCENARIO_ID,PSC.HOST_ID,PARMS.P_ROLE_ID,PSC.ENTITY_ID) LIKE 'Y' || ']' ||P_CALL_OPTION||q'[' || '_']' ||
                   q'[ AND P_SCENARIO.SCENARIO_ID = ']' || V_SCENARIO_ID || q'[']' ||
                   CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                      q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSC.SCENARIO_ID, PSC.HOST_ID,PARMS.P_ROLE_ID, PSC.ENTITY_ID),2,3) LIKE '%Y%'
                         THEN 'Y'
                         ELSE 'N' END = 'Y']'
                   END ||
                 q'[ GROUP BY P_SCENARIO.SCENARIO_ID, P_SCENARIO.CRITICAL_GUI_HIGHLIGHT, P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_CCY_THRESHOLD,P_SCENARIO.CATEGORY_ID,
                      CAT.TITLE, CAT.DISPLAY_ORDER, CAT.DESCRIPTION, P_SCENARIO.DISPLAY_ORDER, P_SCENARIO.TITLE,
                      P_SCENARIO.DESCRIPTION,
                      P_SCENARIO.RECORD_SCENARIO_INSTANCES,P_SCENARIO.START_TIME,P_SCENARIO.END_TIME,PSC.SCENARIO_ID, PSC.HOST_ID, PSC.ENTITY_ID,PARMS.P_ROLE_ID
                   HAVING CASE WHEN P_CCY_THRESHOLD = 'Y'
                                 THEN SUM(PSC.SCENARIO_COUNT_OVER_T)
                            ELSE SUM (PSC.SCENARIO_COUNT)
                       END > 0 ORDER BY 1,2,3,4
                   ]';
            END IF;

            V_SCEN_DETAILS_XML := V_SCEN_DETAILS_XML || FN_GET_QUERYRESULT_AS_XML(V_SQL, CONST_DATE_FORMAT, 'rowset', 'row', -1, -1, V_SQL_INSTANCE);

        END LOOP;

        V_SCEN_DETAILS_XML := '<details>' || V_SCEN_DETAILS_XML || '</details>';

        IF CUR_SCEN_HIGHLIGHT%ISOPEN THEN
            CLOSE CUR_SCEN_HIGHLIGHT;
        END IF;

        RETURN V_SCEN_DETAILS_XML;
    EXCEPTION
        WHEN OTHERS THEN
            IF CUR_SCEN_HIGHLIGHT%ISOPEN THEN
                CLOSE CUR_SCEN_HIGHLIGHT;
            END IF;
            RAISE;
    END;

    FUNCTION FN_GET_UNIQUE_IDENTIFIER_API (P_TAB      TAB_VARCHAR2,
                                           P_UNIQUE_EXPRESSION  P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE)
    RETURN VARCHAR2
    IS
      V_SQL                 VARCHAR2(1000);
      L_INDEX               VARCHAR2(30);
      V_UNIQUE_IDENTIFIER   P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
    BEGIN
      L_INDEX := P_TAB.FIRST;
      WHILE (L_INDEX IS NOT NULL)
      LOOP
        V_SQL := V_SQL || 'q''[' || P_TAB(L_INDEX) || ']'' as ' || L_INDEX || ',';
        L_INDEX := P_TAB.NEXT(L_INDEX);
      END LOOP;
      V_SQL := RTRIM(V_SQL, ',');
      V_SQL := 'SELECT ' || P_UNIQUE_EXPRESSION || ' FROM (SELECT '|| V_SQL||' FROM DUAL)';

      EXECUTE IMMEDIATE V_SQL INTO V_UNIQUE_IDENTIFIER;

      RETURN 'q''[' || V_UNIQUE_IDENTIFIER || ']''';

    END;

    FUNCTION FN_GET_TAB_VAL_IGNORE_QUOTE (P_TAB             TAB_VARCHAR2,
                                          P_INDEX           VARCHAR2)
    RETURN VARCHAR2
    IS
      V_VALUE   VARCHAR2(4000);
    BEGIN
      IF P_TAB.EXISTS(P_INDEX) THEN
        V_VALUE := P_TAB(P_INDEX);
      ELSIF P_TAB.EXISTS('"' || P_INDEX || '"') THEN
        V_VALUE := P_TAB('"' || P_INDEX || '"');
      ELSIF P_TAB.EXISTS(REPLACE(P_INDEX, '"')) THEN
        V_VALUE := P_TAB(REPLACE(P_INDEX, '"'));
      ELSE
        V_VALUE := '';
      END IF;

      RETURN V_VALUE;
    END;

    PROCEDURE SP_CREATE_INSTANCE_API (P_SCENARIO_ID     P_SCENARIO.SCENARIO_ID%TYPE,
                                      P_TAB             TAB_VARCHAR2,
                                      P_USER_ID         VARCHAR2,
                                      P_LOG_TEXT    OUT VARCHAR2,
                                      P_INSTANCE_ID OUT P_SCENARIO_INSTANCE.ID%TYPE)
    IS
      TYPE T_ID IS TABLE OF P_SCENARIO_INSTANCE.ID%TYPE;
      V_ID                    T_ID;
      V_UNIQUE_IDENTIFIER     P_SCENARIO_INSTANCE.UNIQUE_IDENTIFIER%TYPE;
      V_SQL_PART1             VARCHAR2(10000) := 'ID,SCENARIO_ID,STATUS,RAISED_DATETIME,LAST_RAISED_DATETIME,UNIQUE_IDENTIFIER,';
      V_SQL_PART2             VARCHAR2(10000) := 'SEQ_P_SCENARIO_INSTANCE.NEXTVAL,'''||P_SCENARIO_ID||''',''A'',GLOBAL_VAR.SYS_DATE,GLOBAL_VAR.SYS_DATE,';
      V_SQL                   VARCHAR2(10000);
      V_IDX                   VARCHAR2(50);
      V_DATA_TYPE             VARCHAR2(30);
      V_SQL_XML_ATTRIBUTES    VARCHAR2(10000);
      V_ATTRIBUTES_JSON       P_SCENARIO_INSTANCE.ATTRIBUTES_JSON%TYPE;
      V_API_REQUIRED_COLS     P_SCENARIO.API_REQUIRED_COLS%TYPE;
      V_MISSED_COL            P_SCENARIO.API_REQUIRED_COLS%TYPE;
      V_UNIQUE_EXPRESSION     P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION%TYPE;
      V_EVENTS_LAUNCH_STATUS  P_SCENARIO_INSTANCE.EVENTS_LAUNCH_STATUS%TYPE;
      V_COUNT                 NUMBER;
      V_AMT_THRESH_COL        P_SCENARIO.AMT_THRESHOLD_COL%TYPE;
      V_HOST_COL              P_SCENARIO.SEC_HOST_COL%TYPE;
      V_ENTITY_COL            P_SCENARIO.SEC_ENTITY_COL%TYPE;
      V_CURRENCY_COL          P_SCENARIO.SEC_CURRENCY_COL%TYPE;
      V_CCY_THRESHOLD_VALUE   S_CURRENCY.THRESHOLD_PRODUCT%TYPE;
      V_OVER_THRESHOLD        P_SCENARIO_INSTANCE.OVER_THRESHOLD%TYPE;
      V_ACCOUNT_ID_COL        P_SCENARIO.ACCOUNT_ID_COL%TYPE;
      V_SIGN_COL              P_SCENARIO.SIGN_COL%TYPE;
      V_MOVEMENT_ID_COL       P_SCENARIO.MOVEMENT_ID_COL%TYPE;
      V_MATCH_ID_COL          P_SCENARIO.MATCH_ID_COL%TYPE;
      V_SWEEP_ID_COL          P_SCENARIO.SWEEP_ID_COL%TYPE;
      V_PAYMENT_ID_COL        P_SCENARIO.PAYMENT_ID_COL%TYPE;
      V_VALUE_DATE_COL        P_SCENARIO.VALUE_DATE_COL%TYPE;
      V_OTHER_ID_COL          P_SCENARIO.OTHER_ID_COL%TYPE;
      V_OTHER_ID_TYPE         P_SCENARIO.OTHER_ID_TYPE%TYPE;
    BEGIN
      -- Get the unique identifier based on instance_unique_expression in P_SCENARIO
      SELECT INSTANCE_UNIQUE_EXPRESSION, AMT_THRESHOLD_COL, SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL,
             ACCOUNT_ID_COL, SIGN_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL, PAYMENT_ID_COL,
             VALUE_DATE_COL, OTHER_ID_COL, OTHER_ID_TYPE
        INTO V_UNIQUE_EXPRESSION, V_AMT_THRESH_COL, V_HOST_COL, V_ENTITY_COL, V_CURRENCY_COL,
             V_ACCOUNT_ID_COL, V_SIGN_COL, V_MOVEMENT_ID_COL, V_MATCH_ID_COL, V_SWEEP_ID_COL, V_PAYMENT_ID_COL,
             V_VALUE_DATE_COL, V_OTHER_ID_COL, V_OTHER_ID_TYPE
        FROM P_SCENARIO S
       WHERE SCENARIO_ID = P_SCENARIO_ID;

      IF V_UNIQUE_EXPRESSION IS NULL THEN
        P_LOG_TEXT := q'[P_SCENARIO.INSTANCE_UNIQUE_EXPRESSION is mandatory to evaluate UNIQUE_IDENTIIFER]';
      END IF;

      V_UNIQUE_IDENTIFIER := FN_GET_UNIQUE_IDENTIFIER_API (P_TAB, V_UNIQUE_EXPRESSION);

      -- Add unique_identifier value in the part 2 as the column UNIQUE_IDENTIFIER is already initiated
      V_SQL_PART2 := V_SQL_PART2 || V_UNIQUE_IDENTIFIER || ',';

      -- Set LAUNCH_EVENT_STATUS: If no events defined then N else W
      SELECT COUNT(*)
        INTO V_COUNT
        FROM P_SCENARIO_EVENT_FACILITY PEF
             INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
       WHERE MAP.SCENARIO_ID = P_SCENARIO_ID;

      IF V_COUNT = 0 THEN
        V_EVENTS_LAUNCH_STATUS := 'N';
      ELSE
        V_EVENTS_LAUNCH_STATUS := 'W';
      END IF;

      V_SQL_PART1 := V_SQL_PART1 || 'EVENTS_LAUNCH_STATUS,';
      V_SQL_PART2 := V_SQL_PART2 || q'[']' || V_EVENTS_LAUNCH_STATUS || q'[',]';

      V_IDX := P_TAB.FIRST;

      LOOP
        EXIT WHEN V_IDX IS NULL;
        -- If a given column_name is wrong then ignore it
        BEGIN
          -- if V_IDX is defined with quotes then remove them to get data type
          SELECT DATA_TYPE
            INTO V_DATA_TYPE
            FROM USER_TAB_COLUMNS
           WHERE TABLE_NAME = 'P_SCENARIO_INSTANCE'
             AND COLUMN_NAME = UPPER(REPLACE(V_IDX, '"'));

          IF V_DATA_TYPE ='NUMBER' THEN
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[TO_NUMBER(']' || REPLACE(REPLACE(P_TAB(V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)  || q'['),]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[TO_NUMBER(']' || REPLACE(REPLACE(P_TAB(V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL) || q'[')]' || ' AS ' || V_IDX || ',';

          ELSIF V_DATA_TYPE IN ('DATE', 'DATETIME') THEN
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[TO_DATE(']' || P_TAB(V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'['),]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[TO_DATE(']' || P_TAB(V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'[')]' || ' AS ' || V_IDX || ',';
          ELSE
            V_SQL_PART1 := V_SQL_PART1 || V_IDX || ',';
            V_SQL_PART2 := V_SQL_PART2 || q'[']' || P_TAB(V_IDX)  || q'[',]';
            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[']' || P_TAB(V_IDX) || q'[']' || ' AS ' || V_IDX || ',';
          END IF;


        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            -- If the given column is not a p_scenario_instance column but it corresponds to one of the
            -- columns HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID, AMOUNT, SIGN, MOVEMENT_ID, MATCH_ID, SWEEP_ID
            -- PAYMENT_ID, VALUE_DATE, OTHER_ID

            V_SQL_PART1 := V_SQL_PART1 ||
                      CASE REPLACE(V_IDX, '"')
                        WHEN REPLACE(V_HOST_COL, '"') THEN 'HOST_ID,'
                        WHEN REPLACE(V_ENTITY_COL, '"') THEN 'ENTITY_ID,'
                        WHEN REPLACE(V_CURRENCY_COL, '"') THEN 'CURRENCY_CODE,'
                        WHEN REPLACE(V_ACCOUNT_ID_COL, '"') THEN 'ACCOUNT_ID,'
                        WHEN REPLACE(V_AMT_THRESH_COL, '"') THEN 'AMOUNT,'
                        WHEN REPLACE(V_SIGN_COL, '"') THEN 'SIGN,'
                        WHEN REPLACE(V_MOVEMENT_ID_COL, '"') THEN 'MOVEMENT_ID,'
                        WHEN REPLACE(V_MATCH_ID_COL, '"') THEN 'MATCH_ID,'
                        WHEN REPLACE(V_SWEEP_ID_COL, '"') THEN 'SWEEP_ID,'
                        WHEN REPLACE(V_PAYMENT_ID_COL, '"') THEN 'PAYMENT_ID,'
                        WHEN REPLACE(V_VALUE_DATE_COL, '"') THEN 'VALUE_DATE,'
                        WHEN REPLACE(V_OTHER_ID_COL, '"') THEN 'OTHER_ID,'
                      END;

            V_SQL_PART2 := V_SQL_PART2 ||
                      CASE REPLACE(V_IDX, '"')
                        WHEN REPLACE(V_HOST_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_ENTITY_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_CURRENCY_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_ACCOUNT_ID_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_AMT_THRESH_COL, '"') THEN q'[TO_NUMBER(']' || REPLACE(REPLACE(FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)  || q'['),]'
                        WHEN REPLACE(V_SIGN_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                        WHEN REPLACE(V_MOVEMENT_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_MATCH_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_SWEEP_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_PAYMENT_ID_COL, '"') THEN FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ','
                        WHEN REPLACE(V_VALUE_DATE_COL, '"') THEN q'[TO_DATE(']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX)  || q'[',']' || CONST_DATE_FORMAT || q'['),]'
                        WHEN REPLACE(V_OTHER_ID_COL, '"') THEN 'q''[' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || ']'','
                      END;

            V_SQL_XML_ATTRIBUTES := V_SQL_XML_ATTRIBUTES || q'[']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) || q'[']' || ' AS ' || V_IDX || ',';
        END;

        -- Set over_threshold column if amount column exists otherwise set it to N
        -- to allow displaying instances in monitor
        IF REPLACE(V_IDX, '"') = REPLACE(V_AMT_THRESH_COL, '"') -- Ignore existing quotes between tab and P_SCENARIO
           AND FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX) IS NOT NULL
           AND V_OVER_THRESHOLD IS NULL THEN
          IF V_HOST_COL IS NOT NULL AND V_ENTITY_COL IS NOT NULL AND V_CURRENCY_COL IS NOT NULL THEN
            BEGIN
              EXECUTE IMMEDIATE q'[SELECT THRESHOLD_PRODUCT FROM S_CURRENCY S WHERE S.HOST_ID = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_HOST_COL) || q'[' AND S.ENTITY_ID = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_ENTITY_COL) || q'[' AND S.CURRENCY_CODE = ']' || FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_CURRENCY_COL) || q'[']' INTO V_CCY_THRESHOLD_VALUE;
            EXCEPTION
              WHEN OTHERS THEN
                sp_error_log ('', P_USER_ID, 'DBSERVER',
                'PKG_ALERT.SP_CREATE_INSTANCE_API -> Error when evaluating OVER_THRESHOLD for Scenario ID = ' || P_SCENARIO_ID,
                SQLCODE,
                SQLERRM);

            END;
            IF (TO_NUMBER(REPLACE(REPLACE(FN_GET_TAB_VAL_IGNORE_QUOTE(P_TAB, V_IDX), '.', CONST_DECIMAL), ',', CONST_DECIMAL)) > V_CCY_THRESHOLD_VALUE) THEN
              V_OVER_THRESHOLD := 'Y';
            ELSE
              V_OVER_THRESHOLD := 'N';
            END IF;
          END IF;
        END IF;

        V_IDX := P_TAB.NEXT (V_IDX);
      END LOOP;

      -- If V_OVER_THRESHOLD is null then set it N in p_scenario_instance to allow displaying instances in monitor
      IF V_OVER_THRESHOLD IS NOT NULL THEN
        V_SQL_PART1 := V_SQL_PART1 || 'OVER_THRESHOLD,';
        V_SQL_PART2 := V_SQL_PART2 || q'[']' || V_OVER_THRESHOLD || q'[',]';
      END IF;

      -- Check if API_REQUIRED_COLS columns exist in V_TAB
      SELECT REPLACE(REPLACE(REPLACE(API_REQUIRED_COLS, '['), ']'), '"')
        INTO V_API_REQUIRED_COLS
        FROM P_SCENARIO
       WHERE SCENARIO_ID = P_SCENARIO_ID;

      WITH REQ_COLS AS (
        SELECT LTRIM(RTRIM(REGEXP_SUBSTR(V_API_REQUIRED_COLS, '[^,]+', 1, LEVEL))) COL, LEVEL IDX
          FROM DUAL
       CONNECT BY LEVEL <= LENGTH(REGEXP_REPLACE(V_API_REQUIRED_COLS,'[^,]+'))+1)
      SELECT LISTAGG(COL, ',') WITHIN GROUP (ORDER BY IDX)
        INTO V_MISSED_COL
        FROM REQ_COLS
       WHERE INSTR(V_SQL_PART1, COL) = 0;

      IF V_MISSED_COL IS NOT NULL THEN
        P_LOG_TEXT := q'[The following columns are required for this scenario: ]' || V_MISSED_COL;
        RETURN;
      END IF;

      V_SQL_XML_ATTRIBUTES := 'SELECT ' || RTRIM(V_SQL_XML_ATTRIBUTES, ',') || ' FROM DUAL';
      V_ATTRIBUTES_JSON := FN_GET_QUERYRESULT_AS_JSON(V_SQL_XML_ATTRIBUTES);

      V_SQL_PART1 := V_SQL_PART1 || 'ATTRIBUTES_JSON';
      V_SQL_PART2 := V_SQL_PART2 || 'q''[' || V_ATTRIBUTES_JSON || ']''';

      V_SQL := 'INSERT INTO P_SCENARIO_INSTANCE (' || RTRIM(V_SQL_PART1, ',') ||
      ') VALUES (' || RTRIM(V_SQL_PART2, ',') || ') RETURNING ID INTO :1';

      BEGIN
        EXECUTE IMMEDIATE V_SQL RETURNING BULK COLLECT INTO V_ID;
        P_INSTANCE_ID := V_ID(V_ID.FIRST);
        INSERT INTO P_SCENARIO_ACTIVE_INSTANCE (ID)
        SELECT P_INSTANCE_ID
          FROM DUAL
         WHERE NOT EXISTS (SELECT NULL FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID = P_INSTANCE_ID);
        P_LOG_TEXT := q'[Scenario Instance created using API: ID=]' || P_INSTANCE_ID;

        SP_LOG_SCENARIO_INSTANCE(P_INSTANCE_ID, q'[Scenario Instance created using API]', P_USER_ID);
      EXCEPTION
        WHEN OTHERS THEN
          P_LOG_TEXT := q'[Error in creating instance: ]' || SQLCODE || '(' || SQLERRM || ')' || CHR(10) || V_SQL;

      END;

    END;

    FUNCTION FN_GET_SCENARIO_INSTANCES (P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
                                         P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
                                         P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
                                         P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
                                         P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
                                         P_USER_ID         S_USERS.USER_ID%TYPE,
                                         P_FILTER_TREE     VARCHAR2,
                                         P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
                                         PV_INS_STATUS     VARCHAR2 DEFAULT 'All',
                                         P_RESOL_DATETIME  P_SCENARIO_INSTANCE.RESOLVED_DATETIME%TYPE DEFAULT NULL,
                                         P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
                                         P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
                                         P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
                                         P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
                                         P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
                                         P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
                                         P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL,
                                         P_FILTER_GRID     VARCHAR2 DEFAULT NULL,
                                         P_SORT_GRID       VARCHAR2 DEFAULT NULL
                                         ) RETURN SYS_REFCURSOR
    IS
      V_SQL       VARCHAR2(10000);
      P_REF       SYS_REFCURSOR;
      V_ROLE_ID   S_USERS.ROLE_ID%TYPE;
    BEGIN
      SELECT ROLE_ID
        INTO V_ROLE_ID
        FROM S_USERS
       WHERE USER_ID = P_USER_ID;

      V_SQL := q'[
        WITH PARMS
        AS (SELECT :P_FACILITY_ID P_FACILITY_ID,
                   :P_HOST_ID P_HOST_ID,
                   :P_ENTITY_ID P_ENTITY_ID,
                   :P_CURRENCY_CODE P_CURRENCY_CODE,
                   :P_VALUE_DATE P_VALUE_DATE,
                   :P_ROLE_ID P_ROLE_ID,
                   :P_CCY_THRESHOLD P_CCY_THRESHOLD,
                   :P_ACCOUNT_ID P_ACCOUNT_ID,
                   :P_MOVEMENT_ID P_MOVEMENT_ID,
                   :P_MATCH_ID P_MATCH_ID,
                   :P_ILM_GROUP_ID P_ILM_GROUP_ID,
                   :P_SWEEP_ID P_SWEEP_ID,
                   :P_PAYMENT_ID P_PAYMENT_ID
              FROM DUAL)
            SELECT * FROM (
            SELECT PSI.*
              FROM PARMS
                   CROSS JOIN ]' ||
              CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                q'[   P_SCENARIO_GUI_ALERT_FACILITY GAF
                     INNER JOIN P_SCENARIO_GUI_ALERT_MAPPING GAM ON (GAF.ID = GAM.GUI_FACILITY_ID)
                     INNER JOIN P_SCENARIO ON (P_SCENARIO.SCENARIO_ID = GAM.SCENARIO_ID)]'
              ELSE
                q'[   P_SCENARIO ]'
              END ||
              q'[
                   INNER JOIN P_SCENARIO_INSTANCE PSI ON (P_SCENARIO.SCENARIO_ID = PSI.SCENARIO_ID)]' ||
              CASE WHEN NVL(PV_INS_STATUS, 'A') IN ('A','P','O') THEN
                 q'[
                   INNER JOIN P_SCENARIO_ACTIVE_INSTANCE PAI ON (PSI.ID = PAI.ID)]'
              END ||
              CASE WHEN P_FACILITY_ID NOT IN (CONST_SCEN_INSTANCE_FACILITY, CONST_WRKFLW_MONITOR_FACILITY) THEN
                q'[ WHERE GAF.ID = PARMS.P_FACILITY_ID AND ]'
              ELSE
                q'[ WHERE ]'
              END ||
              q'[ P_SCENARIO.ACTIVE_FLAG = 'Y' AND (NVL(PSI.OVER_THRESHOLD, 'Y') = 'Y' OR NVL(P_CCY_THRESHOLD, 'N') = 'N')
               AND PARMS.P_HOST_ID IN ('All', PSI.HOST_ID)
               AND PARMS.P_ENTITY_ID IN ('All', PSI.ENTITY_ID)
               AND PARMS.P_CURRENCY_CODE IN ('All', PSI.CURRENCY_CODE)
               AND (PSI.VALUE_DATE = PARMS.P_VALUE_DATE OR PARMS.P_VALUE_DATE IS NULL)
               AND (PSI.ACCOUNT_ID = PARMS.P_ACCOUNT_ID OR PARMS.P_ACCOUNT_ID IS NULL)
               AND (PSI.MOVEMENT_ID = PARMS.P_MOVEMENT_ID OR PARMS.P_MOVEMENT_ID IS NULL)
               AND (PSI.MATCH_ID = P_MATCH_ID OR P_MATCH_ID IS NULL)
               --AND ((PSI.HOST_ID,PSI.ENTITY_ID,PSI.ACCOUNT_ID) IN (SELECT HOST_ID,ENTITY_ID,ACCOUNT_ID FROM TABLE(PKG_ILM.FN_GET_ILM_ACC_IN_GROUP(P_ILM_GROUP_ID))) OR P_ILM_GROUP_ID IS NULL)
               AND (PSI.OTHER_ID = P_ILM_GROUP_ID OR (PSI.OTHER_ID IS NULL OR P_ILM_GROUP_ID IS NULL))
               AND (PSI.SWEEP_ID = P_SWEEP_ID OR P_SWEEP_ID IS NULL)
               -- check user's access for entity/currency and scenario
               AND PK_APPLICATION.FNGETCURRENCYACCESS(PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID, PSI.CURRENCY_CODE) != '2'
               AND SUBSTR(PKG_ALERT.FN_GET_SCENARIO_ACCESS(PSI.SCENARIO_ID, PSI.HOST_ID, PARMS.P_ROLE_ID, PSI.ENTITY_ID), 1,1) = 'Y'
               ]' || P_FILTER_TREE ||
               CASE WHEN PV_INS_STATUS IS NULL THEN
                 q'[ AND PSI.STATUS IN ('A','P','O') ]'
               ELSE
                 q'[ AND ']' || PV_INS_STATUS || q'[' IN ('All', PSI.STATUS)]'
               END ||
               CASE WHEN PV_INS_STATUS = 'R' AND P_RESOL_DATETIME IS NOT NULL THEN
                 q'[ AND TRUNC(PSI.RESOLVED_DATETIME) = TO_DATE(']' || TO_CHAR(P_RESOL_DATETIME, CONST_DATE_FORMAT)|| q'[',']' || CONST_DATE_FORMAT||q'[')]'
               END ||
               CASE WHEN P_SHOW_ALERT_SCEN = 'Y' THEN
                 q'[AND CASE WHEN SUBSTR(PKG_ALERT.fn_get_scenario_access(PSI.SCENARIO_ID, PSI.HOST_ID,PARMS.P_ROLE_ID, PSI.ENTITY_ID),2,3) LIKE '%Y%'
                 THEN 'Y'
                 ELSE 'N' END = 'Y']'
               END ||
               ')' ||
               CASE WHEN P_FILTER_GRID IS NOT NULL THEN ' WHERE ' || P_FILTER_GRID END ||
               ' ORDER BY ' || NVL(P_SORT_GRID, ' ID DESC');

           OPEN P_REF FOR V_SQL
            USING P_FACILITY_ID,
                  P_HOST_ID,
                  P_ENTITY_ID,
                  P_CURRENCY_CODE,
                  P_VALUE_DATE,
                  V_ROLE_ID,
                  P_CCY_THRESHOLD,
                  P_ACCOUNT_ID,
                  P_MOVEMENT_ID,
                  P_MATCH_ID,
                  P_ILM_GROUP_ID,
                  P_SWEEP_ID,
                  P_PAYMENT_ID;

           RETURN P_REF;
    END;

    PROCEDURE SP_SET_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                      PV_NEW_STATUS               P_SCENARIO_INSTANCE.STATUS%TYPE,
                                      P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE)
    IS
        V_LOG_TEXT  P_SCENARIO_INSTANCE_LOG.LOG_TEXT%TYPE;
        V_COUNT     NUMBER;
    BEGIN
         IF PV_NEW_STATUS = 'A' THEN
          V_LOG_TEXT := q'[Set Status to Active]';
        ELSIF PV_NEW_STATUS = 'R' THEN
          V_LOG_TEXT := q'[Set Status to Resolved]';
        ELSE
          V_LOG_TEXT := q'[Set Status to ]' || PV_NEW_STATUS;
        END IF;
        -- Are there events to launch
        SELECT COUNT(*)
          INTO V_COUNT
          FROM P_SCENARIO_EVENT_FACILITY PEF
               INNER JOIN P_SCENARIO_EVENT_MAPPING MAP ON (MAP.EVENT_FACILITY_ID = PEF.ID)
               INNER JOIN P_SCENARIO_INSTANCE PSI ON (MAP.SCENARIO_ID = PSI.SCENARIO_ID)
         WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

        IF V_COUNT > 0 THEN
          SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, PV_NEW_STATUS, V_LOG_TEXT, P_USER_ID, 'W');
        ELSE
          SP_UPD_INSTANCE_STATUS (P_SCENARIO_INSTANCE_ID, PV_NEW_STATUS, V_LOG_TEXT, P_USER_ID, 'N');
        END IF;
    END;

    PROCEDURE SP_UPD_SCEN_INSTANCE_COUNTS (P_SCENARIO_ID        P_SCENARIO.SCENARIO_ID%TYPE)
    IS
    BEGIN
        -- Calculate scenario counts
        DELETE P_SCENARIO_COUNTS
         WHERE SCENARIO_ID = P_SCENARIO_ID;

        INSERT INTO P_SCENARIO_COUNTS (SCENARIO_ID, HOST_ID, ENTITY_ID, CURRENCY_CODE, SCENARIO_COUNT, SCENARIO_COUNT_OVER_T, EMAIL_FLAG, EMAILED_SC_COUNT, EMAILED_SC_COUNT_OVER_T)
        SELECT SCENARIO_ID, NVL (HOST_ID, GLOBAL_VAR.FN_GET_HOST), NVL (ENTITY_ID, 'All'), NVL (CURRENCY_CODE, 'All'), COUNT (*) SCENARIO_COUNT,
               SUM (CASE WHEN PSI.OVER_THRESHOLD = 'Y' THEN 1 ELSE 0 END) scenario_count_over_t, 'N' EMAIL_FLAG, 0 EMAILED_SC_COUNT, 0 EMAILED_SC_COUNT_OVER_T
          FROM P_SCENARIO_INSTANCE psi
         WHERE SCENARIO_ID = P_SCENARIO_ID
           AND STATUS IN ('A','P','O')
      GROUP BY SCENARIO_ID, HOST_ID, ENTITY_ID, CURRENCY_CODE;
    END;

    FUNCTION FN_EXIST_INST_FOR_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE)
    RETURN VARCHAR2
    IS
      V_EXIST_SCENARIO_INSTANCES  VARCHAR2(1);
    BEGIN
      BEGIN
        SELECT 'Y'
          INTO V_EXIST_SCENARIO_INSTANCES
          FROM DUAL
         WHERE EXISTS (SELECT NULL
                         FROM P_SCENARIO_INSTANCE
                        WHERE SCENARIO_ID = P_SCENARIO_ID
                          AND STATUS IN ('A','P','O'));
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          V_EXIST_SCENARIO_INSTANCES := 'N';
      END;

      RETURN V_EXIST_SCENARIO_INSTANCES;
    END;

   FUNCTION FN_GET_RESULT_RESOL_QUERY (P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
   RETURN NUMBER
   IS
      V_RESOLUTION_REF_COLS     P_SCENARIO.RESOLUTION_REF_COLS%TYPE;
      V_PEND_RESOL_QUERY_TEXT   P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT%TYPE;
      V_COLUMN_VAL_NBR          NUMBER;
      V_COLUMN_VAL_DATE         DATE;
      V_COLUMN_VAL              VARCHAR2(4000);
      V_COLUMN_TYPE             VARCHAR2(50);
      cursor_name               INTEGER;
      V_COUNT                   NUMBER;
      v_nbr_rows                NUMBER;
      V_BIND_VARIABLE           VARCHAR2(50);
      V_SCENARIO_ID             P_SCENARIO_INSTANCE.SCENARIO_ID%TYPE;
   BEGIN
      -- Get the correspondent scenario
      SELECT SCENARIO_ID
        INTO V_SCENARIO_ID
        FROM P_SCENARIO_INSTANCE
       WHERE ID = P_SCENARIO_INSTANCE_ID;

      -- Get RESOLUTION_REF_COLS from p_scenario
      SELECT RESOLUTION_REF_COLS, PENDING_RESOLUTION_QUERY_TEXT--, REGEXP_COUNT (PENDING_RESOLUTION_QUERY_TEXT, '(:[^: $]+)'), REGEXP_COUNT (RESOLUTION_REF_COLS, '[^,]+')
        INTO V_RESOLUTION_REF_COLS, V_PEND_RESOL_QUERY_TEXT--, V_NBR_BIND_VARIABLES, V_NBR_RESOL_REF_COLS
        FROM P_SCENARIO
       WHERE SCENARIO_ID = V_SCENARIO_ID;

      V_PEND_RESOL_QUERY_TEXT := 'SELECT COUNT(*) cnt FROM ( ' || V_PEND_RESOL_QUERY_TEXT || ' )';
      -- Check if RESOLUTION_REF_COLS contains the same number of bind variables needed in PENDING_RESOLUTION_QUERY_TEXT
      /*IF V_NBR_BIND_VARIABLES != V_NBR_RESOL_REF_COLS THEN
        sp_error_log ('',
                       'SYSTEM',
                       'DBSERVER',
                       'PKG_ALERT.FN_GET_RESULT_RESOL_QUERY -> Error for ' || P_SCENARIO_INSTANCE_ID,
                       '-20001',
                       'Number of bind variables defined in PENDING_RESOLUTION_QUERY_TEXT query is not equivalent to those defined in RESOLUTION_REF_COLS'
                     );
      END IF;*/
      cursor_name := dbms_sql.open_cursor;
      dbms_sql.parse(cursor_name, V_PEND_RESOL_QUERY_TEXT, dbms_sql.native);
      FOR rec IN (
          SELECT REPLACE(T.PARAMETER_NAME, '"') BindColName, ROWNUM IDX
            FROM JSON_TABLE (V_RESOLUTION_REF_COLS
            COLUMNS (NESTED PATH '$[*]' COLUMNS(PARAMETER_NAME VARCHAR2(50) PATH '$.content'))) T
           WHERE T.PARAMETER_NAME IS NOT NULL
      )
      LOOP
        -- Get correspondent values/types of given ref_columns from P_SCENARIO_INSTANCE.ATTRIBUTES_XML column
       V_COLUMN_VAL := FN_GET_SCN_ATTR_VAL (rec.BindColName, P_SCENARIO_INSTANCE_ID);
       BEGIN
        V_COLUMN_TYPE := FN_GET_SCN_ATTR_TYPE (rec.BindColName, P_SCENARIO_INSTANCE_ID);
       EXCEPTION
        WHEN OTHERS THEN
          V_COLUMN_TYPE := 'TEXT';
       END;

        -- Get i-th  bind variable names in P_SCENARIO.PENDING_RESOLUTION_QUERY_TEXT query
        SELECT REGEXP_SUBSTR (V_PEND_RESOL_QUERY_TEXT, '(:[^: $]+)', 1, rec.idx)
          INTO V_BIND_VARIABLE
          FROM DUAL
         WHERE REGEXP_SUBSTR (V_PEND_RESOL_QUERY_TEXT, '(:[^: $]+)', 1, rec.idx) IS NOT NULL;

        -- Bind variables dynamically
        IF V_COLUMN_TYPE = 'NUMBER' THEN
          V_COLUMN_VAL_NBR := TO_NUMBER(REPLACE(REPLACE(V_COLUMN_VAL, '.', CONST_DECIMAL), ',', CONST_DECIMAL));
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL_NBR);
        ELSIF V_COLUMN_TYPE IN ('DATE', 'DATETIME') THEN
          V_COLUMN_VAL_DATE := TO_DATE(V_COLUMN_VAL, CONST_DATE_FORMAT);
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL_DATE);
        ELSE
          dbms_sql.bind_variable(cursor_name, V_BIND_VARIABLE, V_COLUMN_VAL);
        END IF;


      END LOOP;

      DBMS_SQL.DEFINE_COLUMN (cursor_name, 1, V_COUNT);

      v_nbr_rows := dbms_sql.execute(cursor_name);
      LOOP
        IF DBMS_SQL.FETCH_ROWS(cursor_name) > 0 THEN
          DBMS_SQL.COLUMN_VALUE(cursor_name, 1, V_COUNT);
        ELSE
          -- No more rows to copy:
          EXIT;
        END IF;
      END LOOP;
      dbms_sql.close_cursor(cursor_name);

      RETURN V_COUNT;

   EXCEPTION
     WHEN OTHERS THEN
        sp_error_log ('',
            'SYSTEM',
            'DBSERVER',
            'PKG_ALERT.FN_GET_RESULT_RESOL_QUERY -> Error for Instance ID = ' || P_SCENARIO_INSTANCE_ID || ' (SCENARIO_ID = ' || V_SCENARIO_ID || ')',
            SQLCODE,
            SQLERRM
          );
         dbms_sql.close_cursor(cursor_name);
         RETURN -1;
   END;

    FUNCTION FN_GET_SCN_ATTR_VAL (P_ATTRIBUTE_NAME          VARCHAR2,
                                  P_SCENARIO_INSTANCE_ID    P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2
    IS
      V_ATTRIBUTE_VALUE     VARCHAR2(4000);
      V_SQL                 VARCHAR2(4000);
    BEGIN
      IF P_ATTRIBUTE_NAME = CONST_INSTANCE_ID_ATTR THEN
        V_ATTRIBUTE_VALUE := TO_CHAR(P_SCENARIO_INSTANCE_ID);

      ELSIF P_ATTRIBUTE_NAME IN ('HOST_ID','ENTITY_ID','CURRENCY_CODE','SIGN','AMOUNT','ACCOUNT_ID','MOVEMENT_ID','SWEEP_ID','PAYMENT_ID','OTHER_ID') THEN
        EXECUTE IMMEDIATE 'SELECT ' || P_ATTRIBUTE_NAME || ' FROM P_SCENARIO_INSTANCE WHERE ID = :P_SCENARIO_INSTANCE_ID'
           INTO V_ATTRIBUTE_VALUE USING P_SCENARIO_INSTANCE_ID;
      ELSE
        V_SQL := q'[SELECT JSON_VALUE(ATTRIBUTES_JSON, '$.rowset.row."]' || LOWER(P_ATTRIBUTE_NAME) || q'[".content')
          FROM P_SCENARIO_INSTANCE PSI
         WHERE PSI.ID = :P_SCENARIO_INSTANCE_ID]';

         EXECUTE IMMEDIATE V_SQL INTO V_ATTRIBUTE_VALUE USING P_SCENARIO_INSTANCE_ID;
      END IF;

       RETURN V_ATTRIBUTE_VALUE;
    END;

    FUNCTION FN_GET_SCN_ATTR_TYPE (P_ATTRIBUTE_NAME         VARCHAR2,
                                   P_SCENARIO_INSTANCE_ID   P_SCENARIO_INSTANCE.ID%TYPE)
    RETURN VARCHAR2
    IS
      V_ATTRIBUTE_TYPE    VARCHAR2(50);
      V_SQL               VARCHAR2(4000);
    BEGIN
      IF P_ATTRIBUTE_NAME = CONST_INSTANCE_ID_ATTR THEN
        V_ATTRIBUTE_TYPE := 'NUMBER';
      ELSE
        V_SQL := q'[SELECT JSON_VALUE(ATTRIBUTES_JSON, '$.rowset.row."]' || LOWER(P_ATTRIBUTE_NAME) || q'[".type')
          FROM P_SCENARIO_INSTANCE PSI
         WHERE PSI.ID = :P_SCENARIO_INSTANCE_ID]';

        EXECUTE IMMEDIATE V_SQL INTO V_ATTRIBUTE_TYPE USING P_SCENARIO_INSTANCE_ID;
      END IF;

      RETURN V_ATTRIBUTE_TYPE;
    END;

    -- =====================================================================================
    -- ENHANCED PARALLEL PROCESSING IMPLEMENTATION FOR PERFORMANCE OPTIMIZATION
    -- =====================================================================================

    -- Package-level cache for event mappings and SQL statements
    TYPE T_EVENT_CACHE IS TABLE OF CLOB INDEX BY VARCHAR2(100);
    G_EVENT_SQL_CACHE T_EVENT_CACHE;
    G_CACHE_INITIALIZED BOOLEAN := FALSE;

    -- Performance thresholds
    CONST_PARALLEL_THRESHOLD CONSTANT NUMBER := 100;  -- Use parallel processing for 100+ instances
    CONST_MAX_PARALLEL_DEGREE CONSTANT NUMBER := 8;   -- Maximum parallel degree
    CONST_BATCH_SIZE CONSTANT NUMBER := 50;            -- Optimal batch size for parallel processing

    -- Clear the event cache
    PROCEDURE SP_CLEAR_EVENT_CACHE IS
    BEGIN
        G_EVENT_SQL_CACHE.DELETE;
        G_CACHE_INITIALIZED := FALSE;
        SP_LOG_SCENARIO_INSTANCE(NULL, 'Event cache cleared', CONST_SYS_USER);
    END;

    -- Get optimal parallel degree based on scenario and instance count
    FUNCTION FN_GET_OPTIMAL_PARALLEL_DEGREE (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                            P_INSTANCE_COUNT   NUMBER)
    RETURN NUMBER IS
        V_PARALLEL_DEGREE NUMBER;
        V_CPU_COUNT NUMBER;
    BEGIN
        -- Get CPU count from system
        SELECT VALUE INTO V_CPU_COUNT
        FROM V$PARAMETER
        WHERE NAME = 'cpu_count';

        -- Calculate optimal parallel degree
        IF P_INSTANCE_COUNT < CONST_PARALLEL_THRESHOLD THEN
            V_PARALLEL_DEGREE := 1; -- Sequential processing
        ELSIF P_INSTANCE_COUNT < 500 THEN
            V_PARALLEL_DEGREE := LEAST(4, V_CPU_COUNT);
        ELSIF P_INSTANCE_COUNT < 2000 THEN
            V_PARALLEL_DEGREE := LEAST(6, V_CPU_COUNT);
        ELSE
            V_PARALLEL_DEGREE := LEAST(CONST_MAX_PARALLEL_DEGREE, V_CPU_COUNT);
        END IF;

        RETURN V_PARALLEL_DEGREE;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 4; -- Default fallback
    END;

    -- Optimized event launcher with caching and performance improvements
    FUNCTION FN_LAUNCH_SCEN_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                            P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                            P_USE_CACHE                 VARCHAR2 DEFAULT 'Y')
    RETURN NUMBER IS
        V_RETURN_RESULT       NUMBER := 1;
        V_NBR_FAILED_EVENTS   NUMBER := 0;
        V_SQL                 CLOB;
        V_CACHE_KEY           VARCHAR2(100);
        V_EVENT_FACILITY_ID   P_SCENARIO_EVENT_FACILITY.ID%TYPE;
        V_PROGRAM_ID          P_SCENARIO_EVENT_FACILITY.PROGRAM_ID%TYPE;
        V_HOST_ID             P_SCENARIO_INSTANCE.HOST_ID%TYPE;
        V_SCENARIO_ID         P_SCENARIO_INSTANCE.SCENARIO_ID%TYPE;
        V_MOVEMENT_ID         P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE;
        V_STATUS_AFTER_EVENT_TRIGGER P_SCENARIO.STATUS_AFTER_EVENT_TRIGGER%TYPE;
        V_STATUS_AFTER_EVENT_LABEL   VARCHAR2(20);

        -- Cursor for events (optimized with hints)
        CURSOR CUR_EVENTS IS
            SELECT /*+ INDEX(GAM, PK_P_SCENARIO_EVENT_MAPPING) USE_NL(GAM SEF) */
                   GAM.EVENT_FACILITY_ID, SEF.PROGRAM_ID
            FROM P_SCENARIO_EVENT_MAPPING GAM
                 INNER JOIN P_SCENARIO_EVENT_FACILITY SEF ON GAM.EVENT_FACILITY_ID = SEF.ID
            WHERE GAM.SCENARIO_ID = V_SCENARIO_ID
            ORDER BY GAM.EVENT_ORDER;

    BEGIN
        -- Get instance details in one query
        SELECT PSI.HOST_ID, PSI.SCENARIO_ID, PSI.MOVEMENT_ID
        INTO V_HOST_ID, V_SCENARIO_ID, V_MOVEMENT_ID
        FROM P_SCENARIO_INSTANCE PSI
        WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

        -- Process each event for this instance
        FOR event_rec IN CUR_EVENTS LOOP
            V_EVENT_FACILITY_ID := event_rec.EVENT_FACILITY_ID;
            V_PROGRAM_ID := event_rec.PROGRAM_ID;
            V_CACHE_KEY := V_SCENARIO_ID || '_' || V_EVENT_FACILITY_ID || '_' || V_PROGRAM_ID;

            -- Try to get SQL from cache first
            IF P_USE_CACHE = 'Y' AND G_EVENT_SQL_CACHE.EXISTS(V_CACHE_KEY) THEN
                V_SQL := G_EVENT_SQL_CACHE(V_CACHE_KEY);
            ELSE
                -- Build SQL dynamically (simplified version of original logic)
                V_SQL := BUILD_EVENT_SQL(P_SCENARIO_INSTANCE_ID, V_EVENT_FACILITY_ID, V_PROGRAM_ID, V_MOVEMENT_ID);

                -- Cache the SQL for future use
                IF P_USE_CACHE = 'Y' THEN
                    G_EVENT_SQL_CACHE(V_CACHE_KEY) := V_SQL;
                END IF;
            END IF;

            -- Execute the event SQL
            IF V_SQL IS NOT NULL THEN
                BEGIN
                    EXECUTE IMMEDIATE V_SQL;
                    V_RETURN_RESULT := 1;
                EXCEPTION
                    WHEN OTHERS THEN
                        V_NBR_FAILED_EVENTS := V_NBR_FAILED_EVENTS + 1;
                        V_RETURN_RESULT := -1;
                        SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID,
                            'Error in optimized event ' || V_EVENT_FACILITY_ID || ': ' || SQLERRM, P_USER_ID);
                END;
            END IF;
        END LOOP;

        -- Update instance status if no events ran
        IF V_RETURN_RESULT <> 1 AND V_NBR_FAILED_EVENTS = 0 THEN
            SELECT STATUS_AFTER_EVENT_TRIGGER,
                   DECODE(STATUS_AFTER_EVENT_TRIGGER,'P','Pending','A','Active','O','Overdue','R','Resolved')
            INTO V_STATUS_AFTER_EVENT_TRIGGER, V_STATUS_AFTER_EVENT_LABEL
            FROM P_SCENARIO PS
                 INNER JOIN P_SCENARIO_INSTANCE PSI ON PS.SCENARIO_ID = PSI.SCENARIO_ID
            WHERE PSI.ID = P_SCENARIO_INSTANCE_ID;

            SP_UPD_INSTANCE_STATUS(P_SCENARIO_INSTANCE_ID, V_STATUS_AFTER_EVENT_TRIGGER,
                                 'Status updated to ' || V_STATUS_AFTER_EVENT_LABEL, P_USER_ID);
        END IF;

        RETURN V_RETURN_RESULT;
    EXCEPTION
        WHEN OTHERS THEN
            SP_LOG_SCENARIO_INSTANCE(P_SCENARIO_INSTANCE_ID, 'Error in FN_LAUNCH_SCEN_EVENT_OPTIMIZED: ' || SQLERRM, P_USER_ID);
            RETURN -1;
    END;

    -- Helper function to build event SQL (simplified version for performance)
    FUNCTION BUILD_EVENT_SQL(P_SCENARIO_INSTANCE_ID P_SCENARIO_INSTANCE.ID%TYPE,
                            P_EVENT_FACILITY_ID     P_SCENARIO_EVENT_FACILITY.ID%TYPE,
                            P_PROGRAM_ID            P_SCENARIO_EVENT_FACILITY.PROGRAM_ID%TYPE,
                            P_MOVEMENT_ID           P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE)
    RETURN CLOB IS
        V_SQL CLOB;
    BEGIN
        -- For movement update events (Program ID 6), build optimized SQL
        IF P_PROGRAM_ID = 6 AND P_MOVEMENT_ID IS NOT NULL THEN
            V_SQL := 'BEGIN UPDATE P_MOVEMENT SET LAST_UPDATE_DATE = SYSDATE WHERE MOVEMENT_ID = ' || P_MOVEMENT_ID ||
                    '; PKG_ALERT.SP_LOG_SCENARIO_INSTANCE(' || P_SCENARIO_INSTANCE_ID ||
                    ', ''Movement updated: '' || ' || P_MOVEMENT_ID || '); END;';
        ELSE
            -- For other event types, fall back to original function
            RETURN NULL; -- This will trigger the original FN_LAUNCH_SCEN_EVENT
        END IF;

        RETURN V_SQL;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NULL;
    END;

    -- Batch processor for parallel execution
    PROCEDURE SP_PROCESS_INSTANCE_BATCH (P_INSTANCE_IDS     SYS.ODCINUMBERLIST,
                                        P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                        P_BATCH_ID         NUMBER DEFAULT NULL) IS
        V_PROCESSED_COUNT NUMBER := 0;
        V_FAILED_COUNT    NUMBER := 0;
        V_RESULT          NUMBER;
        V_START_TIME      TIMESTAMP := SYSTIMESTAMP;
        V_BATCH_LOG       VARCHAR2(4000);
    BEGIN
        V_BATCH_LOG := 'Processing batch ' || NVL(P_BATCH_ID, 0) || ' with ' || P_INSTANCE_IDS.COUNT || ' instances';
        SP_LOG_SCENARIO_INSTANCE(NULL, V_BATCH_LOG, P_USER_ID);

        -- Process each instance in the batch
        FOR i IN 1..P_INSTANCE_IDS.COUNT LOOP
            BEGIN
                -- Use optimized event launcher
                V_RESULT := FN_LAUNCH_SCEN_EVENT_OPTIMIZED(P_INSTANCE_IDS(i), P_USER_ID, 'Y');

                IF V_RESULT = 1 THEN
                    V_PROCESSED_COUNT := V_PROCESSED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = P_INSTANCE_IDS(i);
                ELSE
                    V_FAILED_COUNT := V_FAILED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_INSTANCE_IDS(i);
                END IF;

                -- Commit every 10 instances to avoid long transactions
                IF MOD(i, 10) = 0 THEN
                    COMMIT;
                END IF;

            EXCEPTION
                WHEN OTHERS THEN
                    V_FAILED_COUNT := V_FAILED_COUNT + 1;
                    UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = P_INSTANCE_IDS(i);
                    sp_error_log('', P_USER_ID, 'DBSERVER',
                               'Error processing instance ' || P_INSTANCE_IDS(i) || ' in batch ' || P_BATCH_ID,
                               SQLCODE, SQLERRM);
            END;
        END LOOP;

        COMMIT;

        V_BATCH_LOG := 'Batch ' || NVL(P_BATCH_ID, 0) || ' completed: ' || V_PROCESSED_COUNT ||
                      ' processed, ' || V_FAILED_COUNT || ' failed in ' ||
                      EXTRACT(SECOND FROM (SYSTIMESTAMP - V_START_TIME)) || ' seconds';
        SP_LOG_SCENARIO_INSTANCE(NULL, V_BATCH_LOG, P_USER_ID);

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', P_USER_ID, 'DBSERVER',
                       'SP_PROCESS_INSTANCE_BATCH error for batch ' || P_BATCH_ID, SQLCODE, SQLERRM);
            RAISE;
    END;

    -- Main parallel processing procedure
    PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                           P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                           P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                           P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL,
                                           P_PARALLEL_DEGREE  NUMBER DEFAULT 4) IS

        TYPE T_INSTANCE_ID_TAB IS TABLE OF NUMBER;
        V_INSTANCE_IDS        T_INSTANCE_ID_TAB;
        V_TOTAL_INSTANCES     NUMBER;
        V_PARALLEL_DEGREE     NUMBER;
        V_BATCH_SIZE          NUMBER;
        V_BATCH_COUNT         NUMBER;
        V_START_TIMESTAMP     TIMESTAMP := SYSTIMESTAMP;
        V_END_TIMESTAMP       TIMESTAMP;
        V_PROCESS_DURATION    INTERVAL DAY TO SECOND;
        V_START_TEST_TIME     DATE := GLOBAL_VAR.SYS_DATE;
        V_NBR_FAILED_INST_EVENT NUMBER := 0;
        V_ERROR_LOCATION      VARCHAR2(10);
        V_JOB_NAME            VARCHAR2(100);
        V_BATCH_IDS           SYS.ODCINUMBERLIST := SYS.ODCINUMBERLIST();

        -- Cursor for instances that need event processing
        CURSOR CUR_OLD_INSTANCES IS
            SELECT PSI.ID
            FROM P_SCENARIO_INSTANCE PSI
                 INNER JOIN P_SCENARIO_EVENT_MAPPING GAM ON PSI.SCENARIO_ID = GAM.SCENARIO_ID
            WHERE PSI.SCENARIO_ID = P_SCENARIO_ID
              AND PSI.EVENTS_LAUNCH_STATUS = 'W'
            ORDER BY PSI.ID;

    BEGIN
        V_ERROR_LOCATION := '10';
        SP_LOG_SCENARIO_INSTANCE(NULL, 'Starting parallel processing for scenario ' || P_SCENARIO_ID, P_USER_ID);

        -- Get all instances that need processing
        V_ERROR_LOCATION := '20';
        OPEN CUR_OLD_INSTANCES;
        FETCH CUR_OLD_INSTANCES BULK COLLECT INTO V_INSTANCE_IDS;
        CLOSE CUR_OLD_INSTANCES;

        V_TOTAL_INSTANCES := V_INSTANCE_IDS.COUNT;

        IF V_TOTAL_INSTANCES = 0 THEN
            SP_LOG_SCENARIO_INSTANCE(NULL, 'No instances to process for scenario ' || P_SCENARIO_ID, P_USER_ID);
            RETURN;
        END IF;

        -- Determine optimal processing strategy
        V_ERROR_LOCATION := '30';
        V_PARALLEL_DEGREE := FN_GET_OPTIMAL_PARALLEL_DEGREE(P_SCENARIO_ID, V_TOTAL_INSTANCES);

        IF V_PARALLEL_DEGREE = 1 OR V_TOTAL_INSTANCES < CONST_PARALLEL_THRESHOLD THEN
            -- Use sequential processing for small batches
            SP_LOG_SCENARIO_INSTANCE(NULL, 'Using sequential processing for ' || V_TOTAL_INSTANCES || ' instances', P_USER_ID);

            FOR i IN 1..V_INSTANCE_IDS.COUNT LOOP
                BEGIN
                    IF FN_LAUNCH_SCEN_EVENT_OPTIMIZED(V_INSTANCE_IDS(i), P_USER_ID, 'Y') <> 1 THEN
                        V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
                    END IF;
                    UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'L' WHERE ID = V_INSTANCE_IDS(i);
                EXCEPTION
                    WHEN OTHERS THEN
                        V_NBR_FAILED_INST_EVENT := V_NBR_FAILED_INST_EVENT + 1;
                        UPDATE P_SCENARIO_INSTANCE SET EVENTS_LAUNCH_STATUS = 'F' WHERE ID = V_INSTANCE_IDS(i);
                END;

                -- Commit every 50 instances
                IF MOD(i, 50) = 0 THEN
                    COMMIT;
                END IF;
            END LOOP;
            COMMIT;
        ELSE
            -- Use parallel processing for large batches
            V_ERROR_LOCATION := '40';
            V_BATCH_SIZE := GREATEST(CONST_BATCH_SIZE, CEIL(V_TOTAL_INSTANCES / V_PARALLEL_DEGREE));
            V_BATCH_COUNT := CEIL(V_TOTAL_INSTANCES / V_BATCH_SIZE);

            SP_LOG_SCENARIO_INSTANCE(NULL, 'Using parallel processing: ' || V_TOTAL_INSTANCES ||
                                   ' instances, ' || V_PARALLEL_DEGREE || ' parallel degree, ' ||
                                   V_BATCH_COUNT || ' batches of size ' || V_BATCH_SIZE, P_USER_ID);

            -- Process batches in parallel using DBMS_PARALLEL_EXECUTE
            V_ERROR_LOCATION := '50';

            -- Create chunks for parallel processing
            FOR batch_num IN 1..V_BATCH_COUNT LOOP
                DECLARE
                    V_START_IDX NUMBER := ((batch_num - 1) * V_BATCH_SIZE) + 1;
                    V_END_IDX   NUMBER := LEAST(batch_num * V_BATCH_SIZE, V_TOTAL_INSTANCES);
                    V_BATCH_INSTANCES SYS.ODCINUMBERLIST := SYS.ODCINUMBERLIST();
                BEGIN
                    -- Build batch of instance IDs
                    FOR i IN V_START_IDX..V_END_IDX LOOP
                        V_BATCH_INSTANCES.EXTEND;
                        V_BATCH_INSTANCES(V_BATCH_INSTANCES.COUNT) := V_INSTANCE_IDS(i);
                    END LOOP;

                    -- Process batch directly (simplified parallel approach)
                    SP_PROCESS_INSTANCE_BATCH(V_BATCH_INSTANCES, P_USER_ID, batch_num);
                END;
            END LOOP;
        END IF;

        -- Update scenario statistics and timing
        V_ERROR_LOCATION := '60';
        SP_UPD_SCEN_INSTANCE_COUNTS(P_SCENARIO_ID);
        SP_UPD_SCENARIO_INSTANCE(P_SCENARIO_ID, P_USER_ID);

        -- Calculate and log performance metrics
        V_ERROR_LOCATION := '70';
        V_END_TIMESTAMP := SYSTIMESTAMP;
        V_PROCESS_DURATION := V_END_TIMESTAMP - V_START_TIMESTAMP;

        -- Update scenario system record
        UPDATE P_SCENARIO_SYSTEM
        SET LAST_RUN_DATE = GLOBAL_VAR.SYS_DATE,
            LAST_RUN_DURATION_SECS = FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION)
        WHERE SCENARIO_ID = P_SCENARIO_ID;

        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO P_SCENARIO_SYSTEM(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
            VALUES (P_SCENARIO_ID, GLOBAL_VAR.SYS_DATE, FN_CONV_INTERVAL_TO_SECS(V_PROCESS_DURATION));
        END IF;

        -- Update schedule record if applicable
        IF P_SCENARIO_SCHEDULE_ID IS NOT NULL THEN
            UPDATE P_SCENARIO_SCHEDULE
            SET LAST_RUN_STARTED = V_START_TEST_TIME,
                LAST_RUN_ENDED = GLOBAL_VAR.SYS_DATE,
                LAST_RUN_STATUS = CASE WHEN V_NBR_FAILED_INST_EVENT > 0 THEN 'F' ELSE 'S' END
            WHERE SCENARIO_ID = P_SCENARIO_ID
              AND SCENARIO_SCHEDULE_ID = P_SCENARIO_SCHEDULE_ID;
        END IF;

        COMMIT;

        SP_LOG_SCENARIO_INSTANCE(NULL, 'Parallel processing completed for scenario ' || P_SCENARIO_ID ||
                               ': ' || V_TOTAL_INSTANCES || ' instances processed in ' ||
                               EXTRACT(SECOND FROM V_PROCESS_DURATION) || ' seconds', P_USER_ID);

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            sp_error_log('', P_USER_ID, 'DBSERVER',
                       'SP_PROCESS_SCENARIO_PARALLEL error for scenario ' || P_SCENARIO_ID ||
                       ' at location ' || V_ERROR_LOCATION, SQLCODE, SQLERRM);
            RAISE;
    END;

END;
/

