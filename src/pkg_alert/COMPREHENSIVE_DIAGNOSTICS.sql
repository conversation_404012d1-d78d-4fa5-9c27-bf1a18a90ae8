-- Comprehensive Performance Diagnostics for Oracle Scenario Processing
-- This script provides detailed analysis of where performance bottlenecks occur

SET SERVEROUTPUT ON SIZE 1000000;
SET PAGESIZE 1000;
SET LINESIZE 200;

-- Replace with your actual scenario ID
DEFINE SCENARIO_ID = 'YOUR_SCENARIO_ID_HERE'

PROMPT ===============================================
PROMPT ORACLE SCENARIO PROCESSING DIAGNOSTICS
PROMPT ===============================================
PROMPT Scenario ID: &SCENARIO_ID
PROMPT Analysis Date: 
SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM DUAL;
PROMPT

-- Step 1: Enable diagnostics for the scenario
PROMPT Step 1: Enabling diagnostics...
BEGIN
    PKG_ALERT.SP_ENABLE_DIAGNOSTICS('&SCENARIO_ID');
END;
/

-- Step 2: Run the optimized processing with full diagnostics
PROMPT
PROMPT Step 2: Running optimized scenario processing with diagnostics...
PROMPT (This will show real-time progress)
PROMPT

DECLARE
    V_START_TIME TIMESTAMP := SYSTIMESTAMP;
    V_END_TIME TIMESTAMP;
    V_DURATION NUMBER;
BEGIN
    -- Run the optimized processing
    PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED(
        P_SCENARIO_ID => '&SCENARIO_ID',
        P_USER_ID => 'DIAGNOSTIC_TEST',
        P_PARALLEL_DEGREE => 4
    );
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== PROCESSING COMPLETED ===');
    DBMS_OUTPUT.PUT_LINE('Total execution time: ' || ROUND(V_DURATION, 3) || ' seconds');
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('ERROR: ' || SQLERRM);
        RAISE;
END;
/

-- Step 3: Analyze performance metrics
PROMPT
PROMPT Step 3: Performance Analysis Results
PROMPT =====================================

-- 3a. Overall performance summary
PROMPT
PROMPT 3a. OVERALL PERFORMANCE SUMMARY:
PROMPT ---------------------------------
SELECT 
    OPERATION,
    EXECUTION_COUNT,
    AVG_DURATION_SEC,
    MIN_DURATION_SEC,
    MAX_DURATION_SEC,
    TOTAL_RECORDS,
    RECORDS_PER_SECOND,
    AVG_PARALLEL_DEGREE
FROM TABLE(PKG_ALERT.FN_GET_PERFORMANCE_REPORT('&SCENARIO_ID', SYSDATE))
ORDER BY FIRST_EXECUTION;

-- 3b. Detailed timing breakdown
PROMPT
PROMPT 3b. DETAILED TIMING BREAKDOWN:
PROMPT -------------------------------
SELECT 
    TO_CHAR(START_TIME, 'HH24:MI:SS.FF3') as START_TIME,
    TO_CHAR(END_TIME, 'HH24:MI:SS.FF3') as END_TIME,
    ROUND(DURATION_SECONDS, 3) as DURATION_SEC,
    OPERATION,
    NVL(RECORD_COUNT, 0) as RECORDS,
    CASE 
        WHEN RECORD_COUNT > 0 AND DURATION_SECONDS > 0 
        THEN ROUND(RECORD_COUNT / DURATION_SECONDS, 2) 
        ELSE NULL 
    END as RECORDS_PER_SEC,
    NVL(PARALLEL_DEGREE, 0) as PARALLEL_DEG,
    ADDITIONAL_INFO
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
ORDER BY START_TIME;

-- 3c. Instance processing results
PROMPT
PROMPT 3c. INSTANCE PROCESSING RESULTS:
PROMPT ---------------------------------
SELECT 
    'Total Instances' as METRIC,
    COUNT(*) as COUNT
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = '&SCENARIO_ID'
UNION ALL
SELECT 
    'Events Launched Successfully' as METRIC,
    COUNT(*) as COUNT
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = '&SCENARIO_ID' 
  AND EVENTS_LAUNCH_STATUS = 'L'
UNION ALL
SELECT 
    'Events Failed' as METRIC,
    COUNT(*) as COUNT
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = '&SCENARIO_ID' 
  AND EVENTS_LAUNCH_STATUS = 'F'
UNION ALL
SELECT 
    'Events Waiting' as METRIC,
    COUNT(*) as COUNT
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = '&SCENARIO_ID' 
  AND EVENTS_LAUNCH_STATUS = 'W'
UNION ALL
SELECT 
    'No Events Required' as METRIC,
    COUNT(*) as COUNT
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = '&SCENARIO_ID' 
  AND EVENTS_LAUNCH_STATUS = 'N';

-- 3d. Performance bottleneck analysis
PROMPT
PROMPT 3d. PERFORMANCE BOTTLENECK ANALYSIS:
PROMPT -------------------------------------
WITH performance_data AS (
    SELECT 
        OPERATION,
        DURATION_SECONDS,
        RECORD_COUNT,
        ROW_NUMBER() OVER (ORDER BY DURATION_SECONDS DESC) as rn
    FROM P_SCENARIO_PERFORMANCE_LOG
    WHERE SCENARIO_ID = '&SCENARIO_ID'
      AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
)
SELECT 
    'Slowest Operation' as ANALYSIS,
    OPERATION as DETAILS,
    ROUND(DURATION_SECONDS, 3) || ' seconds' as VALUE
FROM performance_data 
WHERE rn = 1
UNION ALL
SELECT 
    'Total Processing Time' as ANALYSIS,
    'All Operations' as DETAILS,
    ROUND(SUM(DURATION_SECONDS), 3) || ' seconds' as VALUE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
UNION ALL
SELECT 
    'Average Records/Second' as ANALYSIS,
    'Overall Rate' as DETAILS,
    ROUND(SUM(NVL(RECORD_COUNT, 0)) / NULLIF(SUM(DURATION_SECONDS), 0), 2) || ' rec/sec' as VALUE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE);

-- 3e. Parallel processing effectiveness
PROMPT
PROMPT 3e. PARALLEL PROCESSING EFFECTIVENESS:
PROMPT --------------------------------------
SELECT 
    'Parallel Degree Used' as METRIC,
    MAX(PARALLEL_DEGREE) as VALUE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
  AND PARALLEL_DEGREE IS NOT NULL
UNION ALL
SELECT 
    'Bulk Insert Efficiency' as METRIC,
    CASE 
        WHEN MAX(DURATION_SECONDS) > 0 
        THEN ROUND(MAX(RECORD_COUNT) / MAX(DURATION_SECONDS), 2) || ' instances/sec'
        ELSE 'N/A'
    END as VALUE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND OPERATION = 'BULK_INSERT_INSTANCES'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
UNION ALL
SELECT 
    'Event Processing Efficiency' as METRIC,
    CASE 
        WHEN MAX(DURATION_SECONDS) > 0 
        THEN ROUND(MAX(RECORD_COUNT) / MAX(DURATION_SECONDS), 2) || ' events/sec'
        ELSE 'N/A'
    END as VALUE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND OPERATION = 'PARALLEL_EVENT_PROCESSING'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE);

-- Step 4: Recommendations
PROMPT
PROMPT Step 4: PERFORMANCE RECOMMENDATIONS:
PROMPT ====================================

-- Check if bulk insert is working efficiently
DECLARE
    V_BULK_EFFICIENCY NUMBER;
    V_EVENT_EFFICIENCY NUMBER;
    V_PARALLEL_DEGREE NUMBER;
    V_TOTAL_INSTANCES NUMBER;
BEGIN
    -- Get bulk insert efficiency
    SELECT MAX(RECORD_COUNT) / NULLIF(MAX(DURATION_SECONDS), 0)
    INTO V_BULK_EFFICIENCY
    FROM P_SCENARIO_PERFORMANCE_LOG
    WHERE SCENARIO_ID = '&SCENARIO_ID'
      AND OPERATION = 'BULK_INSERT_INSTANCES'
      AND TRUNC(LOG_DATE) = TRUNC(SYSDATE);
    
    -- Get event processing efficiency
    SELECT MAX(RECORD_COUNT) / NULLIF(MAX(DURATION_SECONDS), 0)
    INTO V_EVENT_EFFICIENCY
    FROM P_SCENARIO_PERFORMANCE_LOG
    WHERE SCENARIO_ID = '&SCENARIO_ID'
      AND OPERATION = 'PARALLEL_EVENT_PROCESSING'
      AND TRUNC(LOG_DATE) = TRUNC(SYSDATE);
    
    -- Get parallel degree used
    SELECT MAX(PARALLEL_DEGREE)
    INTO V_PARALLEL_DEGREE
    FROM P_SCENARIO_PERFORMANCE_LOG
    WHERE SCENARIO_ID = '&SCENARIO_ID'
      AND TRUNC(LOG_DATE) = TRUNC(SYSDATE);
    
    -- Get total instances
    SELECT COUNT(*)
    INTO V_TOTAL_INSTANCES
    FROM P_SCENARIO_INSTANCE
    WHERE SCENARIO_ID = '&SCENARIO_ID';
    
    DBMS_OUTPUT.PUT_LINE('ANALYSIS RESULTS:');
    DBMS_OUTPUT.PUT_LINE('================');
    
    IF V_BULK_EFFICIENCY IS NOT NULL THEN
        DBMS_OUTPUT.PUT_LINE('✓ Bulk Insert Rate: ' || ROUND(V_BULK_EFFICIENCY, 2) || ' instances/second');
        IF V_BULK_EFFICIENCY < 100 THEN
            DBMS_OUTPUT.PUT_LINE('  → RECOMMENDATION: Bulk insert is slower than expected. Check for:');
            DBMS_OUTPUT.PUT_LINE('    - Database constraints/triggers on P_SCENARIO_INSTANCE');
            DBMS_OUTPUT.PUT_LINE('    - Index maintenance overhead');
            DBMS_OUTPUT.PUT_LINE('    - FN_GET_SCENARIO_INSTANCE_ROW performance');
        ELSE
            DBMS_OUTPUT.PUT_LINE('  → Bulk insert performance is GOOD');
        END IF;
    END IF;
    
    IF V_EVENT_EFFICIENCY IS NOT NULL THEN
        DBMS_OUTPUT.PUT_LINE('✓ Event Processing Rate: ' || ROUND(V_EVENT_EFFICIENCY, 2) || ' events/second');
        IF V_EVENT_EFFICIENCY < 50 THEN
            DBMS_OUTPUT.PUT_LINE('  → RECOMMENDATION: Event processing is the bottleneck. Consider:');
            DBMS_OUTPUT.PUT_LINE('    - Increasing parallel degree (currently: ' || NVL(V_PARALLEL_DEGREE, 1) || ')');
            DBMS_OUTPUT.PUT_LINE('    - Optimizing FN_LAUNCH_EVENT_OPTIMIZED');
            DBMS_OUTPUT.PUT_LINE('    - Checking for database locks/contention');
        ELSE
            DBMS_OUTPUT.PUT_LINE('  → Event processing performance is GOOD');
        END IF;
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('✓ Total Instances Processed: ' || V_TOTAL_INSTANCES);
    DBMS_OUTPUT.PUT_LINE('✓ Parallel Degree Used: ' || NVL(V_PARALLEL_DEGREE, 1));
    
    IF V_TOTAL_INSTANCES > 1000 AND NVL(V_PARALLEL_DEGREE, 1) < 4 THEN
        DBMS_OUTPUT.PUT_LINE('  → RECOMMENDATION: For ' || V_TOTAL_INSTANCES || ' instances, consider increasing parallel degree to 6-8');
    END IF;
    
END;
/

PROMPT
PROMPT Step 5: NEXT STEPS:
PROMPT ===================
PROMPT 1. Review the timing breakdown above to identify the slowest operations
PROMPT 2. Check if bulk insert is working (should be >100 instances/second)
PROMPT 3. Verify parallel processing is effective (multiple batches processed)
PROMPT 4. If event processing is slow, consider increasing parallel degree
PROMPT 5. Run this diagnostic again with different parallel degrees to find optimal setting
PROMPT
PROMPT To test different parallel degrees:
PROMPT   PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED('&SCENARIO_ID', 'TEST', NULL, NULL, 6);
PROMPT   PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED('&SCENARIO_ID', 'TEST', NULL, NULL, 8);
PROMPT

-- Disable diagnostics to avoid log buildup
BEGIN
    PKG_ALERT.SP_DISABLE_DIAGNOSTICS('&SCENARIO_ID');
    DBMS_OUTPUT.PUT_LINE('Diagnostics disabled for scenario: &SCENARIO_ID');
END;
/

PROMPT
PROMPT Diagnostic analysis complete!
PROMPT Check the results above to identify performance bottlenecks.
