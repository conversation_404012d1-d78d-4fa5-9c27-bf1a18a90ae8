-- Test script for optimized scenario processing
-- This script demonstrates the performance improvements

SET SERVEROUTPUT ON;

DECLARE
    V_SCENARIO_ID           P_SCENARIO.SCENARIO_ID%TYPE := 'YOUR_SCENARIO_ID'; -- Replace with actual scenario ID
    V_START_TIME            TIMESTAMP;
    V_END_TIME              TIMESTAMP;
    V_DURATION_ORIGINAL     NUMBER;
    V_DURATION_OPTIMIZED    NUMBER;
    V_IMPROVEMENT_FACTOR    NUMBER;
    
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== ORACLE SCENARIO PROCESSING PERFORMANCE TEST ===');
    DBMS_OUTPUT.PUT_LINE('Testing scenario: ' || V_SCENARIO_ID);
    DBMS_OUTPUT.PUT_LINE('');
    
    -- Test original method (comment out if you don't want to run it)
    /*
    DBMS_OUTPUT.PUT_LINE('Testing ORIGINAL method...');
    V_START_TIME := SYSTIMESTAMP;
    
    PKG_ALERT.SP_PROCESS_SCENARIO(V_SCENARIO_ID, 'SYSTEM');
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION_ORIGINAL := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    DBMS_OUTPUT.PUT_LINE('Original method completed in: ' || V_DURATION_ORIGINAL || ' seconds');
    DBMS_OUTPUT.PUT_LINE('');
    */
    
    -- Test optimized method
    DBMS_OUTPUT.PUT_LINE('Testing OPTIMIZED method...');
    V_START_TIME := SYSTIMESTAMP;
    
    PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED(
        P_SCENARIO_ID => V_SCENARIO_ID,
        P_USER_ID => 'SYSTEM',
        P_PARALLEL_DEGREE => 4  -- Adjust based on your system capabilities
    );
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION_OPTIMIZED := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    DBMS_OUTPUT.PUT_LINE('Optimized method completed in: ' || V_DURATION_OPTIMIZED || ' seconds');
    DBMS_OUTPUT.PUT_LINE('');
    
    -- Calculate improvement (if original was tested)
    IF V_DURATION_ORIGINAL IS NOT NULL THEN
        V_IMPROVEMENT_FACTOR := V_DURATION_ORIGINAL / V_DURATION_OPTIMIZED;
        DBMS_OUTPUT.PUT_LINE('Performance improvement: ' || ROUND(V_IMPROVEMENT_FACTOR, 2) || 'x faster');
        DBMS_OUTPUT.PUT_LINE('Time saved: ' || ROUND(V_DURATION_ORIGINAL - V_DURATION_OPTIMIZED, 2) || ' seconds');
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST COMPLETED ===');
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error during testing: ' || SQLERRM);
        RAISE;
END;
/

-- Query to check scenario instance processing results
SELECT 
    SCENARIO_ID,
    COUNT(*) as TOTAL_INSTANCES,
    COUNT(CASE WHEN EVENTS_LAUNCH_STATUS = 'L' THEN 1 END) as SUCCESSFUL_EVENTS,
    COUNT(CASE WHEN EVENTS_LAUNCH_STATUS = 'F' THEN 1 END) as FAILED_EVENTS,
    COUNT(CASE WHEN EVENTS_LAUNCH_STATUS = 'W' THEN 1 END) as WAITING_EVENTS
FROM P_SCENARIO_INSTANCE 
WHERE SCENARIO_ID = 'YOUR_SCENARIO_ID'  -- Replace with actual scenario ID
GROUP BY SCENARIO_ID;

-- Query to check performance metrics
SELECT 
    SCENARIO_ID,
    LAST_RUN_DATE,
    LAST_RUN_DURATION_SECS,
    ROUND(LAST_RUN_DURATION_SECS/60, 2) as DURATION_MINUTES
FROM P_SCENARIO_SYSTEM 
WHERE SCENARIO_ID = 'YOUR_SCENARIO_ID'  -- Replace with actual scenario ID
ORDER BY LAST_RUN_DATE DESC;
