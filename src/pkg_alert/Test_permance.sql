set serveroutput on
set timing on
spool test_atef.log

B<PERSON><PERSON>
  delete p_scenario_active_instance where id in (select id from p_scenario_instance where SCENARIO_ID = 'Atef1');
  delete p_scenario_instance where SCENARIO_ID = 'Atef1';
  delete P_SCENARIO WHERE SCENARIO_ID = 'Atef1';
  delete P_SCENARIO_SYSTEM WHERE SCENARIO_ID = 'Atef1';
  delete P_SCENARIO_EVENT_MAPPING where scenario_id='Atef1';
  delete p_movement where reference4='RCHA2' and value_date = TO_DATE('04/10/2008','DD/MM/YYYY') and amount = 999999;
update p_movement set reference4 = null, EXPECTED_SETTLEMENT_DATETIME = null where reference4 = 'RCHA2';
--delete debug_log;
--delete s_error_log where source like 'PKG_ALERT.%' and trunc(error_date)=trunc(global_var.sys_date);
commit;
Insert into P_SCENARIO
(SCENARIO_ID, TITLE, DESC<PERSON><PERSON><PERSON><PERSON>, ACTIVE_FLAG, SY<PERSON><PERSON>_FLAG,
 CATEG<PERSON><PERSON>_<PERSON>, DIS<PERSON>AY_ORDER, SUMMARY_GROUPING, RUN_EVERY, START_TIME,
 END_TIME, SEC_HOST_COL, SEC_ENTITY_COL, SEC_CURRENCY_COL, AMT_THRESHOLD_COL,
 USE_GENERIC_DISPLAY, FACILITY_ID, FACILITY_REF_COLS, FACILITY_PARAM_VALS, EMAIL_PCT_DIFF,
 SIGN_COL, ACCOUNT_ID_COL, MOVEMENT_ID_COL, MATCH_ID_COL, SWEEP_ID_COL,
 PAYMENT_ID_COL, OTHER_ID_TYPE, OTHER_ID_COL, VALUE_DATE_COL, RECORD_SCENARIO_INSTANCES,
 CUSTOM_TREE_LEVEL1, CUSTOM_TREE_LEVEL2, GENERATION_BASIS, SCHEDULE_PARAMETERS_XML, API_REQUIRED_COLS,
 INSTANCE_UNIQUE_EXPRESSION, INSTANCE_EXPIRY_MINS, ALLOW_RERAISE_AFTER_EXPIRY, RERAISE_INTERVAL_MINS, STATUS_AFTER_EVENT_TRIGGER,
 PENDING_RESOLUTION_QUERY_TEXT, PENDING_RESOLUTION_TIME_LIMIT, ALERT_INST_COL, RESOLUTION_REF_COLS, CRITICAL_GUI_HIGHLIGHT,
 QUERY_EXECUTION_LIST_COLUMNS, QUERY_TEXT)
Values
    ('Atef1', 'Atef1', 'Atef1', 'Y', 'N',
     'MOVEMENTS', NULL, 'C', '00:00:30', NULL,
     NULL, '"HOST_ID"', '"ENTITY_ID"', '"CURRENCY_CODE"', '"AMOUNT"',
     NULL, NULL, '[]', NULL, NULL,
     '"SIGN"', '"ACCOUNT_ID"', '"MOVEMENT_ID"', '"MATCH_ID"', NULL,
     NULL, NULL, NULL, '"VALUE_DATE"', 'Y',
     NULL, NULL, 'C', NULL, NULL,
     'movement_id', 60, 'N', NULL, 'A',
     NULL, -1, '[]', '[]', 'N',
     '"HOST_ID","ENTITY_ID","MOVEMENT_ID","CURRENCY_CODE","BOOKCODE","VALUE_DATE","AMOUNT","SIGN","MOVEMENT_TYPE","ACCOUNT_ID","REFERENCE1","REFERENCE2","REFERENCE3","REFERENCE4","COUNTERPARTY_ID","COUNTERPARTY_TEXT1","COUNTERPARTY_TEXT2","COUNTERPARTY_TEXT3","COUNTERPARTY_TEXT4","BENEFICIARY_ID","BENEFICIARY_TEXT1","BENEFICIARY_TEXT2","BENEFICIARY_TEXT3","BENEFICIARY_TEXT4","CUSTODIAN_ID","CUSTODIAN_TEXT1","CUSTODIAN_TEXT2","CUSTODIAN_TEXT3","CUSTODIAN_TEXT4","BOOKCODE_AVAIL","POSITION_LEVEL","PREDICT_STATUS","EXTRACT_STATUS","MATCH_ID","MATCH_STATUS","UPDATE_DATE","UPDATE_USER","INPUT_DATE","INPUT_SOURCE","MESSAGE_ID","MESSAGE_FORMAT","INITIAL_PREDICT_STATUS","INPUT_ROLE","INPUT_USER","ORIG_VALUE_DATE","NOTES_COUNT","OPEN","EXT_BAL_STATUS","ORIGINAL_ACCOUNT_REFERENCE","TO_MATCH","ORIGINAL_BENEFICIARY_ID","TO_MATCH_DATE","TO_MATCH_STAGE","MATCHING_PARTY","PRODUCT_TYPE","POSTING_DATE","SENDING_SYSTEM_STATUS","SETTLEMENT_DATETIME","EXPECTED_SETTLEMENT_DATETIME","CRITICAL_PAYMENT_TYPE","ORDERING_CUSTOMER","ORDERING_INSTITUTION","SENDERS_CORRES","RECEIVERS_CORRES","BENEFICIARY_CUST","PAYMENT_CHANNEL","PAYMENT_TYPE","ORIGINAL_COUNTERPARTY_ID","COUNTERPARTY_TEXT5"#$#VARCHAR2,VARCHAR2,NUMBER,VARCHAR2,VARCHAR2,DATE,NUMBER,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,NUMBER,VARCHAR2,VARCHAR2,NUMBER,VARCHAR2,DATE,VARCHAR2,DATE,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,DATE,NUMBER,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,DATE,NUMBER,VARCHAR2,VARCHAR2,DATE,VARCHAR2,DATE,DATE,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2,VARCHAR2',
     q'[select HOST_ID AS "HOST_ID", ENTITY_ID AS "ENTITY_ID", MOVEMENT_ID AS "MOVEMENT_ID", CURRENCY_CODE AS "CURRENCY_CODE", BOOKCODE AS "BOOKCODE", VALUE_DATE AS "VALUE_DATE", AMOUNT AS "AMOUNT", SIGN AS "SIGN", MOVEMENT_TYPE AS "MOVEMENT_TYPE", ACCOUNT_ID AS "ACCOUNT_ID", REFERENCE1 AS "REFERENCE1", REFERENCE2 AS "REFERENCE2", REFERENCE3 AS "REFERENCE3", REFERENCE4 AS "REFERENCE4", COUNTERPARTY_ID AS "COUNTERPARTY_ID", COUNTERPARTY_TEXT1 AS "COUNTERPARTY_TEXT1", COUNTERPARTY_TEXT2 AS "COUNTERPARTY_TEXT2", COUNTERPARTY_TEXT3 AS "COUNTERPARTY_TEXT3", COUNTERPARTY_TEXT4 AS "COUNTERPARTY_TEXT4", BENEFICIARY_ID AS "BENEFICIARY_ID", BENEFICIARY_TEXT1 AS "BENEFICIARY_TEXT1", BENEFICIARY_TEXT2 AS "BENEFICIARY_TEXT2", BENEFICIARY_TEXT3 AS "BENEFICIARY_TEXT3", BENEFICIARY_TEXT4 AS "BENEFICIARY_TEXT4", CUSTODIAN_ID AS "CUSTODIAN_ID", CUSTODIAN_TEXT1 AS "CUSTODIAN_TEXT1", CUSTODIAN_TEXT2 AS "CUSTODIAN_TEXT2", CUSTODIAN_TEXT3 AS "CUSTODIAN_TEXT3", CUSTODIAN_TEXT4 AS "CUSTODIAN_TEXT4", BOOKCODE_AVAIL AS "BOOKCODE_AVAIL", POSITION_LEVEL AS "POSITION_LEVEL", PREDICT_STATUS AS "PREDICT_STATUS", EXTRACT_STATUS AS "EXTRACT_STATUS", MATCH_ID AS "MATCH_ID", MATCH_STATUS AS "MATCH_STATUS", UPDATE_DATE AS "UPDATE_DATE", UPDATE_USER AS "UPDATE_USER", INPUT_DATE AS "INPUT_DATE", INPUT_SOURCE AS "INPUT_SOURCE", MESSAGE_ID AS "MESSAGE_ID", MESSAGE_FORMAT AS "MESSAGE_FORMAT", INITIAL_PREDICT_STATUS AS "INITIAL_PREDICT_STATUS", INPUT_ROLE AS "INPUT_ROLE", INPUT_USER AS "INPUT_USER", ORIG_VALUE_DATE AS "ORIG_VALUE_DATE", NOTES_COUNT AS "NOTES_COUNT", OPEN AS "OPEN", EXT_BAL_STATUS AS "EXT_BAL_STATUS", ORIGINAL_ACCOUNT_REFERENCE AS "ORIGINAL_ACCOUNT_REFERENCE", TO_MATCH AS "TO_MATCH", ORIGINAL_BENEFICIARY_ID AS "ORIGINAL_BENEFICIARY_ID", TO_MATCH_DATE AS "TO_MATCH_DATE", TO_MATCH_STAGE AS "TO_MATCH_STAGE", MATCHING_PARTY AS "MATCHING_PARTY", PRODUCT_TYPE AS "PRODUCT_TYPE", POSTING_DATE AS "POSTING_DATE", SENDING_SYSTEM_STATUS AS "SENDING_SYSTEM_STATUS", SETTLEMENT_DATETIME AS "SETTLEMENT_DATETIME", EXPECTED_SETTLEMENT_DATETIME AS "EXPECTED_SETTLEMENT_DATETIME", CRITICAL_PAYMENT_TYPE AS "CRITICAL_PAYMENT_TYPE", ORDERING_CUSTOMER AS "ORDERING_CUSTOMER", ORDERING_INSTITUTION AS "ORDERING_INSTITUTION", SENDERS_CORRES AS "SENDERS_CORRES", RECEIVERS_CORRES AS "RECEIVERS_CORRES", BENEFICIARY_CUST AS "BENEFICIARY_CUST", PAYMENT_CHANNEL AS "PAYMENT_CHANNEL", PAYMENT_TYPE AS "PAYMENT_TYPE", ORIGINAL_COUNTERPARTY_ID AS "ORIGINAL_COUNTERPARTY_ID", COUNTERPARTY_TEXT5 AS "COUNTERPARTY_TEXT5"
from p_movement where rownum <= 5]');

Insert into P_SCENARIO_SYSTEM
(SCENARIO_ID, LAST_RUN_DATE, LAST_RUN_DURATION_SECS)
Values
    ('Atef1', TO_DATE('12/09/2013 13:54:35', 'DD/MM/YYYY HH24:MI:SS'), 0.11);

insert into P_SCENARIO_EVENT_MAPPING (map_key, scenario_id, event_facility_id, ordinal, parameters_xml, execute_when, repeat_on_reraise)
select map_key+10, 'Atef1', event_facility_id, ordinal, parameters_xml, execute_when, repeat_on_reraise
from predict_dev.P_SCENARIO_EVENT_MAPPING m
where scenario_id = 'rcha2'
  and not exists (select null from P_SCENARIO_EVENT_MAPPING map where map.scenario_id = 'Atef1' and map.map_key = m.map_key);

DBMS_output.put_line ('Start at ' || TO_CHAR(sysdate, 'DD/MM/YYYY HH24:MI:SS'));
  pkg_alert.sp_process_scenario ('Atef1');

  DBMS_output.put_line ('End at ' || TO_CHAR(sysdate, 'DD/MM/YYYY HH24:MI:SS'));

END;
/

spool off
