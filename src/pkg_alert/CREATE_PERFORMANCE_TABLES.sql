-- Create performance monitoring tables for scenario processing diagnostics

-- Performance metrics logging table
CREATE TABLE P_SCENARIO_PERFORMANCE_LOG (
    ID                  NUMBER PRIMARY KEY,
    SCENARIO_ID         VARCHAR2(50) NOT NULL,
    OPERATION           VARCHAR2(100) NOT NULL,
    START_TIME          TIMESTAMP NOT NULL,
    END_TIME            TIMESTAMP NOT NULL,
    DURATION_SECONDS    NUMBER(10,4),
    RECORD_COUNT        NUMBER,
    BATCH_SIZE          NUMBER,
    PARALLEL_DEGREE     NUMBER,
    ADDITIONAL_INFO     VARCHAR2(4000),
    LOG_DATE            DATE DEFAULT SYSDATE,
    LOG_USER            VARCHAR2(50) DEFAULT USER
);

-- Create sequence for the performance log
CREATE SEQUENCE SEQ_P_SCENARIO_PERF_LOG
    START WITH 1
    INCREMENT BY 1
    NOCACHE;

-- Create index for better query performance
CREATE INDEX IDX_SCENARIO_PERF_LOG_SCEN_DATE 
    ON P_SCENARIO_PERFORMANCE_LOG (SCENARIO_ID, LOG_DATE);

-- Diagnostic settings table
CREATE TABLE P_SCENARIO_DIAGNOSTICS (
    SCENARIO_ID         VARCHAR2(50) PRIMARY KEY,
    DIAGNOSTICS_ENABLED VARCHAR2(1) DEFAULT 'N' CHECK (DIAGNOSTICS_ENABLED IN ('Y', 'N')),
    ENABLED_DATE        DATE DEFAULT SYSDATE,
    ENABLED_BY          VARCHAR2(50) DEFAULT USER,
    DETAILED_LOGGING    VARCHAR2(1) DEFAULT 'Y' CHECK (DETAILED_LOGGING IN ('Y', 'N')),
    BATCH_TIMING        VARCHAR2(1) DEFAULT 'Y' CHECK (BATCH_TIMING IN ('Y', 'N'))
);

-- Real-time processing status table
CREATE TABLE P_SCENARIO_PROCESSING_STATUS (
    SCENARIO_ID         VARCHAR2(50),
    PROCESSING_SESSION  VARCHAR2(50),
    OPERATION           VARCHAR2(100),
    STATUS              VARCHAR2(20), -- STARTED, IN_PROGRESS, COMPLETED, FAILED
    START_TIME          TIMESTAMP,
    LAST_UPDATE         TIMESTAMP DEFAULT SYSTIMESTAMP,
    CURRENT_BATCH       NUMBER,
    TOTAL_BATCHES       NUMBER,
    RECORDS_PROCESSED   NUMBER,
    TOTAL_RECORDS       NUMBER,
    PARALLEL_WORKER_ID  NUMBER,
    ADDITIONAL_INFO     VARCHAR2(4000),
    PRIMARY KEY (SCENARIO_ID, PROCESSING_SESSION, OPERATION, PARALLEL_WORKER_ID)
);

-- Create index for real-time monitoring
CREATE INDEX IDX_SCENARIO_PROC_STATUS_SCEN 
    ON P_SCENARIO_PROCESSING_STATUS (SCENARIO_ID, START_TIME);

-- Grant necessary permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON P_SCENARIO_PERFORMANCE_LOG TO your_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON P_SCENARIO_DIAGNOSTICS TO your_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON P_SCENARIO_PROCESSING_STATUS TO your_role;

-- Sample data for testing
INSERT INTO P_SCENARIO_DIAGNOSTICS (SCENARIO_ID, DIAGNOSTICS_ENABLED, DETAILED_LOGGING, BATCH_TIMING)
VALUES ('TEST_SCENARIO', 'Y', 'Y', 'Y');

COMMIT;

-- Verification queries
SELECT 'P_SCENARIO_PERFORMANCE_LOG' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM P_SCENARIO_PERFORMANCE_LOG
UNION ALL
SELECT 'P_SCENARIO_DIAGNOSTICS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM P_SCENARIO_DIAGNOSTICS
UNION ALL
SELECT 'P_SCENARIO_PROCESSING_STATUS' as TABLE_NAME, COUNT(*) as ROW_COUNT FROM P_SCENARIO_PROCESSING_STATUS;

PROMPT 'Performance monitoring tables created successfully!'
PROMPT 'Use SP_ENABLE_DIAGNOSTICS(scenario_id) to enable monitoring for specific scenarios.'
