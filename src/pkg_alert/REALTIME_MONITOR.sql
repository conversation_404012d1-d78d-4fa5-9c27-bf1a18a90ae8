-- Real-time Performance Monitor for Oracle Scenario Processing
-- Run this in a separate session while your scenario is processing

SET SERVEROUTPUT ON;
SET PAGESIZE 50;
SET LINESIZE 150;

-- Replace with your actual scenario ID
DEFINE SCENARIO_ID = 'YOUR_SCENARIO_ID_HERE'

PROMPT ===============================================
PROMPT REAL-TIME PERFORMANCE MONITOR
PROMPT ===============================================
PROMPT Scenario ID: &SCENARIO_ID
PROMPT Monitoring started at: 
SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM DUAL;
PROMPT
PROMPT This script will show real-time progress every 10 seconds
PROMPT Press Ctrl+C to stop monitoring
PROMPT

-- Create a monitoring loop (you can adjust the timing)
DECLARE
    V_LOOP_COUNT NUMBER := 0;
    V_LAST_RECORD_COUNT NUMBER := 0;
    V_CURRENT_RECORD_COUNT NUMBER := 0;
    V_PROCESSING_RATE NUMBER := 0;
    V_START_TIME DATE := SYSDATE;
    V_CURRENT_TIME DATE;
    V_ELAPSED_MINUTES NUMBER;
    
    -- Performance metrics
    V_TOTAL_INSTANCES NUMBER := 0;
    V_PROCESSED_INSTANCES NUMBER := 0;
    V_FAILED_INSTANCES NUMBER := 0;
    V_WAITING_INSTANCES NUMBER := 0;
    V_NO_EVENT_INSTANCES NUMBER := 0;
    
    -- Latest operation info
    V_LATEST_OPERATION VARCHAR2(100);
    V_LATEST_DURATION NUMBER;
    V_LATEST_RECORDS NUMBER;
    
BEGIN
    LOOP
        V_LOOP_COUNT := V_LOOP_COUNT + 1;
        V_CURRENT_TIME := SYSDATE;
        V_ELAPSED_MINUTES := (V_CURRENT_TIME - V_START_TIME) * 24 * 60;
        
        -- Get current instance counts
        BEGIN
            SELECT 
                COUNT(*),
                SUM(CASE WHEN EVENTS_LAUNCH_STATUS = 'L' THEN 1 ELSE 0 END),
                SUM(CASE WHEN EVENTS_LAUNCH_STATUS = 'F' THEN 1 ELSE 0 END),
                SUM(CASE WHEN EVENTS_LAUNCH_STATUS = 'W' THEN 1 ELSE 0 END),
                SUM(CASE WHEN EVENTS_LAUNCH_STATUS = 'N' THEN 1 ELSE 0 END)
            INTO V_TOTAL_INSTANCES, V_PROCESSED_INSTANCES, V_FAILED_INSTANCES, 
                 V_WAITING_INSTANCES, V_NO_EVENT_INSTANCES
            FROM P_SCENARIO_INSTANCE
            WHERE SCENARIO_ID = '&SCENARIO_ID';
        EXCEPTION
            WHEN OTHERS THEN
                V_TOTAL_INSTANCES := 0;
        END;
        
        -- Get latest performance log entry
        BEGIN
            SELECT OPERATION, DURATION_SECONDS, NVL(RECORD_COUNT, 0)
            INTO V_LATEST_OPERATION, V_LATEST_DURATION, V_LATEST_RECORDS
            FROM (
                SELECT OPERATION, DURATION_SECONDS, RECORD_COUNT,
                       ROW_NUMBER() OVER (ORDER BY START_TIME DESC) as rn
                FROM P_SCENARIO_PERFORMANCE_LOG
                WHERE SCENARIO_ID = '&SCENARIO_ID'
                  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
            )
            WHERE rn = 1;
        EXCEPTION
            WHEN OTHERS THEN
                V_LATEST_OPERATION := 'No data';
                V_LATEST_DURATION := 0;
                V_LATEST_RECORDS := 0;
        END;
        
        -- Calculate processing rate
        V_CURRENT_RECORD_COUNT := V_PROCESSED_INSTANCES + V_FAILED_INSTANCES;
        IF V_LOOP_COUNT > 1 THEN
            V_PROCESSING_RATE := (V_CURRENT_RECORD_COUNT - V_LAST_RECORD_COUNT) / 10; -- per second
        END IF;
        V_LAST_RECORD_COUNT := V_CURRENT_RECORD_COUNT;
        
        -- Display current status
        DBMS_OUTPUT.PUT_LINE('=== MONITOR UPDATE #' || V_LOOP_COUNT || ' (' || 
                           TO_CHAR(V_CURRENT_TIME, 'HH24:MI:SS') || ') ===');
        DBMS_OUTPUT.PUT_LINE('Elapsed Time: ' || ROUND(V_ELAPSED_MINUTES, 1) || ' minutes');
        DBMS_OUTPUT.PUT_LINE('');
        
        -- Instance status
        DBMS_OUTPUT.PUT_LINE('INSTANCE STATUS:');
        DBMS_OUTPUT.PUT_LINE('  Total Instances: ' || V_TOTAL_INSTANCES);
        DBMS_OUTPUT.PUT_LINE('  Processed (L):   ' || V_PROCESSED_INSTANCES || 
                           CASE WHEN V_TOTAL_INSTANCES > 0 THEN ' (' || ROUND(V_PROCESSED_INSTANCES/V_TOTAL_INSTANCES*100, 1) || '%)' END);
        DBMS_OUTPUT.PUT_LINE('  Failed (F):      ' || V_FAILED_INSTANCES);
        DBMS_OUTPUT.PUT_LINE('  Waiting (W):     ' || V_WAITING_INSTANCES);
        DBMS_OUTPUT.PUT_LINE('  No Events (N):   ' || V_NO_EVENT_INSTANCES);
        
        -- Processing rate
        IF V_PROCESSING_RATE > 0 THEN
            DBMS_OUTPUT.PUT_LINE('  Current Rate:    ' || ROUND(V_PROCESSING_RATE, 1) || ' instances/second');
            IF V_WAITING_INSTANCES > 0 THEN
                DBMS_OUTPUT.PUT_LINE('  ETA for remaining: ' || ROUND(V_WAITING_INSTANCES / V_PROCESSING_RATE / 60, 1) || ' minutes');
            END IF;
        END IF;
        
        -- Latest operation
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('LATEST OPERATION:');
        DBMS_OUTPUT.PUT_LINE('  Operation: ' || V_LATEST_OPERATION);
        IF V_LATEST_DURATION > 0 THEN
            DBMS_OUTPUT.PUT_LINE('  Duration:  ' || ROUND(V_LATEST_DURATION, 3) || ' seconds');
            DBMS_OUTPUT.PUT_LINE('  Records:   ' || V_LATEST_RECORDS);
            IF V_LATEST_RECORDS > 0 THEN
                DBMS_OUTPUT.PUT_LINE('  Rate:      ' || ROUND(V_LATEST_RECORDS / V_LATEST_DURATION, 2) || ' records/second');
            END IF;
        END IF;
        
        -- Performance indicators
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('PERFORMANCE INDICATORS:');
        
        -- Check if processing seems stuck
        IF V_LOOP_COUNT > 3 AND V_PROCESSING_RATE = 0 AND V_WAITING_INSTANCES > 0 THEN
            DBMS_OUTPUT.PUT_LINE('  ⚠️  WARNING: No progress detected - processing may be stuck');
        ELSIF V_PROCESSING_RATE > 0 AND V_PROCESSING_RATE < 5 THEN
            DBMS_OUTPUT.PUT_LINE('  ⚠️  SLOW: Processing rate is below 5 instances/second');
        ELSIF V_PROCESSING_RATE >= 50 THEN
            DBMS_OUTPUT.PUT_LINE('  ✅ GOOD: Processing rate is excellent (>50 instances/second)');
        ELSIF V_PROCESSING_RATE >= 20 THEN
            DBMS_OUTPUT.PUT_LINE('  ✅ OK: Processing rate is acceptable (>20 instances/second)');
        END IF;
        
        -- Check for high failure rate
        IF V_TOTAL_INSTANCES > 0 AND (V_FAILED_INSTANCES / V_TOTAL_INSTANCES) > 0.1 THEN
            DBMS_OUTPUT.PUT_LINE('  ⚠️  WARNING: High failure rate (' || ROUND(V_FAILED_INSTANCES/V_TOTAL_INSTANCES*100, 1) || '%)');
        END IF;
        
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('----------------------------------------');
        
        -- Exit if processing is complete
        IF V_WAITING_INSTANCES = 0 AND V_TOTAL_INSTANCES > 0 THEN
            DBMS_OUTPUT.PUT_LINE('✅ PROCESSING COMPLETED!');
            DBMS_OUTPUT.PUT_LINE('Total time: ' || ROUND(V_ELAPSED_MINUTES, 1) || ' minutes');
            DBMS_OUTPUT.PUT_LINE('Average rate: ' || ROUND(V_TOTAL_INSTANCES / (V_ELAPSED_MINUTES * 60), 2) || ' instances/second');
            EXIT;
        END IF;
        
        -- Wait 10 seconds before next check
        DBMS_LOCK.SLEEP(10);
        
        -- Safety exit after 2 hours
        IF V_ELAPSED_MINUTES > 120 THEN
            DBMS_OUTPUT.PUT_LINE('⏰ TIMEOUT: Monitoring stopped after 2 hours');
            EXIT;
        END IF;
        
    END LOOP;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in monitoring: ' || SQLERRM);
END;
/

PROMPT
PROMPT Monitoring completed.
PROMPT
PROMPT To get a detailed performance report, run:
PROMPT @COMPREHENSIVE_DIAGNOSTICS.sql
PROMPT
