-- Quick Performance Test and Comparison Script
-- This script helps you quickly test different configurations and compare results

SET SERVEROUTPUT ON SIZE 1000000;
SET TIMING ON;

-- Replace with your actual scenario ID
DEFINE SCENARIO_ID = 'YOUR_SCENARIO_ID_HERE'

PROMPT ===============================================
PROMPT QUICK PERFORMANCE TEST
PROMPT ===============================================
PROMPT Scenario ID: &SCENARIO_ID
PROMPT

-- Enable diagnostics
BEGIN
    PKG_ALERT.SP_ENABLE_DIAGNOSTICS('&SCENARIO_ID');
    DBMS_OUTPUT.PUT_LINE('Diagnostics enabled for scenario: &SCENARIO_ID');
END;
/

-- Test 1: Parallel Degree 2
PROMPT
PROMPT TEST 1: Parallel Degree = 2
PROMPT =============================
DECLARE
    V_START_TIME TIMESTAMP := SYSTIMESTAMP;
    V_END_TIME TIMESTAMP;
    V_DURATION NUMBER;
    V_INSTANCE_COUNT NUMBER;
BEGIN
    -- Clear previous instances for clean test
    DELETE FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    DELETE FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID IN (
        SELECT ID FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID'
    );
    COMMIT;
    
    -- Run test
    PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED(
        P_SCENARIO_ID => '&SCENARIO_ID',
        P_USER_ID => 'PERF_TEST_2',
        P_PARALLEL_DEGREE => 2
    );
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    SELECT COUNT(*) INTO V_INSTANCE_COUNT 
    FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    
    DBMS_OUTPUT.PUT_LINE('TEST 1 RESULTS:');
    DBMS_OUTPUT.PUT_LINE('  Duration: ' || ROUND(V_DURATION, 3) || ' seconds');
    DBMS_OUTPUT.PUT_LINE('  Instances: ' || V_INSTANCE_COUNT);
    DBMS_OUTPUT.PUT_LINE('  Rate: ' || ROUND(V_INSTANCE_COUNT / GREATEST(V_DURATION, 0.001), 2) || ' instances/second');
END;
/

-- Test 2: Parallel Degree 4
PROMPT
PROMPT TEST 2: Parallel Degree = 4
PROMPT =============================
DECLARE
    V_START_TIME TIMESTAMP := SYSTIMESTAMP;
    V_END_TIME TIMESTAMP;
    V_DURATION NUMBER;
    V_INSTANCE_COUNT NUMBER;
BEGIN
    -- Clear previous instances for clean test
    DELETE FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    DELETE FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID IN (
        SELECT ID FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID'
    );
    COMMIT;
    
    -- Run test
    PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED(
        P_SCENARIO_ID => '&SCENARIO_ID',
        P_USER_ID => 'PERF_TEST_4',
        P_PARALLEL_DEGREE => 4
    );
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    SELECT COUNT(*) INTO V_INSTANCE_COUNT 
    FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    
    DBMS_OUTPUT.PUT_LINE('TEST 2 RESULTS:');
    DBMS_OUTPUT.PUT_LINE('  Duration: ' || ROUND(V_DURATION, 3) || ' seconds');
    DBMS_OUTPUT.PUT_LINE('  Instances: ' || V_INSTANCE_COUNT);
    DBMS_OUTPUT.PUT_LINE('  Rate: ' || ROUND(V_INSTANCE_COUNT / GREATEST(V_DURATION, 0.001), 2) || ' instances/second');
END;
/

-- Test 3: Parallel Degree 8
PROMPT
PROMPT TEST 3: Parallel Degree = 8
PROMPT =============================
DECLARE
    V_START_TIME TIMESTAMP := SYSTIMESTAMP;
    V_END_TIME TIMESTAMP;
    V_DURATION NUMBER;
    V_INSTANCE_COUNT NUMBER;
BEGIN
    -- Clear previous instances for clean test
    DELETE FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    DELETE FROM P_SCENARIO_ACTIVE_INSTANCE WHERE ID IN (
        SELECT ID FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID'
    );
    COMMIT;
    
    -- Run test
    PKG_ALERT.SP_PROCESS_SCENARIO_OPTIMIZED(
        P_SCENARIO_ID => '&SCENARIO_ID',
        P_USER_ID => 'PERF_TEST_8',
        P_PARALLEL_DEGREE => 8
    );
    
    V_END_TIME := SYSTIMESTAMP;
    V_DURATION := EXTRACT(EPOCH FROM (V_END_TIME - V_START_TIME));
    
    SELECT COUNT(*) INTO V_INSTANCE_COUNT 
    FROM P_SCENARIO_INSTANCE WHERE SCENARIO_ID = '&SCENARIO_ID';
    
    DBMS_OUTPUT.PUT_LINE('TEST 3 RESULTS:');
    DBMS_OUTPUT.PUT_LINE('  Duration: ' || ROUND(V_DURATION, 3) || ' seconds');
    DBMS_OUTPUT.PUT_LINE('  Instances: ' || V_INSTANCE_COUNT);
    DBMS_OUTPUT.PUT_LINE('  Rate: ' || ROUND(V_INSTANCE_COUNT / GREATEST(V_DURATION, 0.001), 2) || ' instances/second');
END;
/

-- Performance Comparison Summary
PROMPT
PROMPT PERFORMANCE COMPARISON SUMMARY
PROMPT ==============================

SELECT 
    CASE 
        WHEN LOG_USER = 'PERF_TEST_2' THEN 'Parallel Degree 2'
        WHEN LOG_USER = 'PERF_TEST_4' THEN 'Parallel Degree 4'
        WHEN LOG_USER = 'PERF_TEST_8' THEN 'Parallel Degree 8'
        ELSE LOG_USER
    END as TEST_CONFIGURATION,
    ROUND(DURATION_SECONDS, 3) as TOTAL_DURATION_SEC,
    RECORD_COUNT as INSTANCES_PROCESSED,
    ROUND(RECORD_COUNT / GREATEST(DURATION_SECONDS, 0.001), 2) as INSTANCES_PER_SECOND,
    PARALLEL_DEGREE
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND OPERATION = 'TOTAL_OPTIMIZED_PROCESSING'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
  AND LOG_USER IN ('PERF_TEST_2', 'PERF_TEST_4', 'PERF_TEST_8')
ORDER BY START_TIME;

-- Detailed breakdown by operation
PROMPT
PROMPT DETAILED BREAKDOWN BY OPERATION
PROMPT ===============================

SELECT 
    CASE 
        WHEN LOG_USER = 'PERF_TEST_2' THEN 'PD=2'
        WHEN LOG_USER = 'PERF_TEST_4' THEN 'PD=4'
        WHEN LOG_USER = 'PERF_TEST_8' THEN 'PD=8'
        ELSE LOG_USER
    END as TEST,
    OPERATION,
    ROUND(DURATION_SECONDS, 3) as DURATION_SEC,
    NVL(RECORD_COUNT, 0) as RECORDS,
    CASE 
        WHEN RECORD_COUNT > 0 AND DURATION_SECONDS > 0 
        THEN ROUND(RECORD_COUNT / DURATION_SECONDS, 2) 
        ELSE NULL 
    END as RECORDS_PER_SEC
FROM P_SCENARIO_PERFORMANCE_LOG
WHERE SCENARIO_ID = '&SCENARIO_ID'
  AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
  AND LOG_USER IN ('PERF_TEST_2', 'PERF_TEST_4', 'PERF_TEST_8')
ORDER BY LOG_USER, START_TIME;

-- Recommendations
PROMPT
PROMPT RECOMMENDATIONS
PROMPT ===============

DECLARE
    V_BEST_CONFIG VARCHAR2(50);
    V_BEST_RATE NUMBER := 0;
    V_CURRENT_RATE NUMBER;
    
    CURSOR c_results IS
    SELECT 
        CASE 
            WHEN LOG_USER = 'PERF_TEST_2' THEN 'Parallel Degree 2'
            WHEN LOG_USER = 'PERF_TEST_4' THEN 'Parallel Degree 4'
            WHEN LOG_USER = 'PERF_TEST_8' THEN 'Parallel Degree 8'
        END as CONFIG,
        ROUND(RECORD_COUNT / GREATEST(DURATION_SECONDS, 0.001), 2) as RATE
    FROM P_SCENARIO_PERFORMANCE_LOG
    WHERE SCENARIO_ID = '&SCENARIO_ID'
      AND OPERATION = 'TOTAL_OPTIMIZED_PROCESSING'
      AND TRUNC(LOG_DATE) = TRUNC(SYSDATE)
      AND LOG_USER IN ('PERF_TEST_2', 'PERF_TEST_4', 'PERF_TEST_8')
    ORDER BY START_TIME;
    
BEGIN
    FOR rec IN c_results LOOP
        IF rec.RATE > V_BEST_RATE THEN
            V_BEST_RATE := rec.RATE;
            V_BEST_CONFIG := rec.CONFIG;
        END IF;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('ANALYSIS:');
    DBMS_OUTPUT.PUT_LINE('=========');
    
    IF V_BEST_CONFIG IS NOT NULL THEN
        DBMS_OUTPUT.PUT_LINE('✅ Best Configuration: ' || V_BEST_CONFIG);
        DBMS_OUTPUT.PUT_LINE('✅ Best Rate: ' || V_BEST_RATE || ' instances/second');
        
        IF V_BEST_RATE > 50 THEN
            DBMS_OUTPUT.PUT_LINE('✅ EXCELLENT: Performance is very good (>50 instances/sec)');
        ELSIF V_BEST_RATE > 20 THEN
            DBMS_OUTPUT.PUT_LINE('✅ GOOD: Performance is acceptable (>20 instances/sec)');
        ELSIF V_BEST_RATE > 10 THEN
            DBMS_OUTPUT.PUT_LINE('⚠️  MODERATE: Performance is improved but could be better');
            DBMS_OUTPUT.PUT_LINE('   Consider checking for database bottlenecks');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ POOR: Performance is still slow (<10 instances/sec)');
            DBMS_OUTPUT.PUT_LINE('   The bottleneck may not be in parallel processing');
            DBMS_OUTPUT.PUT_LINE('   Check: Database locks, I/O, CPU, or query performance');
        END IF;
        
        -- Specific recommendations
        IF INSTR(V_BEST_CONFIG, '8') > 0 THEN
            DBMS_OUTPUT.PUT_LINE('💡 TIP: Higher parallel degree works best - consider testing PD=12 or 16');
        ELSIF INSTR(V_BEST_CONFIG, '2') > 0 THEN
            DBMS_OUTPUT.PUT_LINE('💡 TIP: Lower parallel degree works best - may indicate resource contention');
        END IF;
        
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ No test results found - check if tests ran successfully');
    END IF;
    
END;
/

-- Disable diagnostics
BEGIN
    PKG_ALERT.SP_DISABLE_DIAGNOSTICS('&SCENARIO_ID');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Diagnostics disabled for scenario: &SCENARIO_ID');
END;
/

PROMPT
PROMPT Performance testing completed!
PROMPT Use the best configuration identified above for production runs.
PROMPT
